import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/live_stream_provider.dart';
import '../models/live_stream.dart';
import '../models/user.dart';
import '../theme/app_theme.dart';
import 'live_stream_screen.dart';

class StartLiveStreamScreen extends StatefulWidget {
  const StartLiveStreamScreen({super.key});

  @override
  State<StartLiveStreamScreen> createState() => _StartLiveStreamScreenState();
}

class _StartLiveStreamScreenState extends State<StartLiveStreamScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  
  LiveStreamPrivacy _selectedPrivacy = LiveStreamPrivacy.public;
  LiveStreamQuality _selectedQuality = LiveStreamQuality.auto;
  bool _isStarting = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'بدء بث مباشر',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // معاينة الكاميرا
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[700]!),
              ),
              child: Stack(
                children: [
                  // محاكاة معاينة الكاميرا
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.blue.withValues(alpha: 0.3),
                          Colors.purple.withValues(alpha: 0.3),
                          Colors.pink.withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.videocam,
                            size: 80,
                            color: Colors.white70,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'معاينة الكاميرا',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'سيتم تشغيل الكاميرا عند بدء البث',
                            style: TextStyle(
                              color: Colors.white54,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // أزرار التحكم في الكاميرا
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Column(
                      children: [
                        _buildControlButton(
                          icon: Icons.flip_camera_ios,
                          onTap: () {
                            // تبديل الكاميرا الأمامية/الخلفية
                          },
                        ),
                        const SizedBox(height: 12),
                        _buildControlButton(
                          icon: Icons.videocam,
                          onTap: () {
                            // تبديل الكاميرا
                          },
                        ),
                        const SizedBox(height: 12),
                        _buildControlButton(
                          icon: Icons.mic,
                          onTap: () {
                            // تبديل المايكروفون
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // إعدادات البث
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان البث
                    const Text(
                      'عنوان البث',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _titleController,
                      decoration: InputDecoration(
                        hintText: 'اكتب عنوان البث...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                      ),
                      maxLength: 100,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // وصف البث
                    const Text(
                      'وصف البث (اختياري)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        hintText: 'اكتب وصف البث...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                      ),
                      maxLines: 3,
                      maxLength: 500,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // إعدادات الخصوصية والجودة
                    Row(
                      children: [
                        // خصوصية البث
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'الخصوصية',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              DropdownButtonFormField<LiveStreamPrivacy>(
                                value: _selectedPrivacy,
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  filled: true,
                                  fillColor: Colors.grey[100],
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                ),
                                items: LiveStreamPrivacy.values.map((privacy) {
                                  return DropdownMenuItem(
                                    value: privacy,
                                    child: Text(_getPrivacyText(privacy)),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedPrivacy = value!;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(width: 16),
                        
                        // جودة البث
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'الجودة',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              DropdownButtonFormField<LiveStreamQuality>(
                                value: _selectedQuality,
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  filled: true,
                                  fillColor: Colors.grey[100],
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                ),
                                items: LiveStreamQuality.values.map((quality) {
                                  return DropdownMenuItem(
                                    value: quality,
                                    child: Text(_getQualityText(quality)),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedQuality = value!;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // زر بدء البث
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _isStarting ? null : _startLiveStream,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isStarting
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    'جاري بدء البث...',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              )
                            : const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.play_circle_filled, size: 24),
                                  SizedBox(width: 8),
                                  Text(
                                    'بدء البث المباشر',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  String _getPrivacyText(LiveStreamPrivacy privacy) {
    switch (privacy) {
      case LiveStreamPrivacy.public:
        return 'عام';
      case LiveStreamPrivacy.friends:
        return 'الأصدقاء';
      case LiveStreamPrivacy.custom:
        return 'مخصص';
    }
  }

  String _getQualityText(LiveStreamQuality quality) {
    switch (quality) {
      case LiveStreamQuality.low:
        return '480p';
      case LiveStreamQuality.medium:
        return '720p';
      case LiveStreamQuality.high:
        return '1080p';
      case LiveStreamQuality.auto:
        return 'تلقائي';
    }
  }

  Future<void> _startLiveStream() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال عنوان للبث'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isStarting = true;
    });

    try {
      final liveStreamProvider = Provider.of<LiveStreamProvider>(context, listen: false);
      
      // إنشاء مستخدم تجريبي
      final currentUser = User(
        id: 'current_user',
        name: 'أنت',
        email: '<EMAIL>',
        avatar: null,
        gender: 'male',
        joinDate: DateTime.now(),
      );

      final stream = await liveStreamProvider.startLiveStream(
        streamerId: currentUser.id,
        streamer: currentUser,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty 
            ? _descriptionController.text.trim() 
            : null,
        privacy: _selectedPrivacy,
      );

      if (stream != null && mounted) {
        // الانتقال لشاشة البث المباشر
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => LiveStreamScreen(
              stream: stream,
              isStreamer: true,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في بدء البث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isStarting = false;
        });
      }
    }
  }
}
