import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../providers/chat_provider.dart';
import '../models/chat.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import '../screens/advanced_chat_screen.dart';

class AdvancedChatsListScreen extends StatefulWidget {
  const AdvancedChatsListScreen({super.key});

  @override
  State<AdvancedChatsListScreen> createState() => _AdvancedChatsListScreenState();
}

class _AdvancedChatsListScreenState extends State<AdvancedChatsListScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // تحميل المحادثات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ChatProvider>(context, listen: false).loadChats();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),
          
          // التبويبات
          _buildTabBar(),
          
          // قائمة المحادثات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildChatsList(), // جميع المحادثات
                _buildPinnedChats(), // المحادثات المثبتة
                _buildActiveChats(), // المحادثات النشطة
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _startNewChat,
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.chat, color: Colors.white),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      title: const Text(
        'المحادثات',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        IconButton(
          onPressed: _showChatOptions,
          icon: const Icon(Icons.more_vert),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في المحادثات...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[100],
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.primaryColor,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.grey,
        tabs: const [
          Tab(text: 'الكل'),
          Tab(text: 'مثبتة'),
          Tab(text: 'نشطة'),
        ],
      ),
    );
  }

  Widget _buildChatsList() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        if (chatProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (chatProvider.errorMessage != null) {
          return _buildErrorState(chatProvider);
        }

        final chats = _getFilteredChats(chatProvider.chats);

        if (chats.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => chatProvider.loadChats(),
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: chats.length,
            itemBuilder: (context, index) {
              final chat = chats[index];
              return _buildChatItem(chat);
            },
          ),
        );
      },
    );
  }

  Widget _buildPinnedChats() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        final pinnedChats = chatProvider.chats.where((chat) => chat.isPinned).toList();
        
        if (pinnedChats.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.push_pin_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد محادثات مثبتة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'اضغط مطولاً على أي محادثة لتثبيتها',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: pinnedChats.length,
          itemBuilder: (context, index) {
            final chat = pinnedChats[index];
            return _buildChatItem(chat);
          },
        );
      },
    );
  }

  Widget _buildActiveChats() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        final activeChats = chatProvider.chats
            .where((chat) => chat.otherUser.isOnline)
            .toList();
        
        if (activeChats.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.people_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا يوجد أصدقاء متصلون',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'سيظهر هنا الأصدقاء المتصلون حالياً',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: activeChats.length,
          itemBuilder: (context, index) {
            final chat = activeChats[index];
            return _buildChatItem(chat);
          },
        );
      },
    );
  }

  Widget _buildChatItem(Chat chat) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Stack(
          children: [
            SmartAvatarWithText(
              user: chat.otherUser,
              radius: 28,
            ),
            
            // مؤشر الاتصال
            if (chat.otherUser.isOnline)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
            
            // أيقونة التثبيت
            if (chat.isPinned)
              Positioned(
                top: 0,
                left: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.push_pin,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
        
        title: Row(
          children: [
            Expanded(
              child: Text(
                chat.otherUser.name,
                style: TextStyle(
                  fontWeight: chat.unreadCount > 0 ? FontWeight.bold : FontWeight.w500,
                  fontSize: 16,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // أيقونة الكتم
            if (chat.isMuted)
              Icon(
                Icons.volume_off,
                size: 16,
                color: Colors.grey[600],
              ),
          ],
        ),
        
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: Text(
                    chat.lastMessage?.content ?? 'لا توجد رسائل',
                    style: TextStyle(
                      color: chat.unreadCount > 0 ? Colors.black87 : Colors.grey[600],
                      fontWeight: chat.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                
                const SizedBox(width: 8),
                
                Text(
                  chat.lastMessage != null
                      ? timeago.format(chat.lastMessage!.timestamp, locale: 'ar')
                      : '',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ],
        ),
        
        trailing: chat.unreadCount > 0
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  chat.unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : null,
        
        onTap: () => _openChat(chat),
        onLongPress: () => _showChatActions(chat),
      ),
    );
  }

  Widget _buildErrorState(ChatProvider chatProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            chatProvider.errorMessage!,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => chatProvider.loadChats(),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد محادثات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ محادثة جديدة مع أصدقائك',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _startNewChat,
            icon: const Icon(Icons.add),
            label: const Text('محادثة جديدة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  List<Chat> _getFilteredChats(List<Chat> chats) {
    if (_searchQuery.isEmpty) return chats;
    
    return chats.where((chat) {
      return chat.otherUser.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             (chat.lastMessage?.content.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
    }).toList();
  }

  void _openChat(Chat chat) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AdvancedChatScreen(chat: chat),
      ),
    );
  }

  void _showChatActions(Chat chat) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(
                chat.isPinned ? Icons.push_pin : Icons.push_pin_outlined,
                color: AppTheme.primaryColor,
              ),
              title: Text(chat.isPinned ? 'إلغاء التثبيت' : 'تثبيت المحادثة'),
              onTap: () {
                Navigator.pop(context);
                final chatProvider = Provider.of<ChatProvider>(context, listen: false);
                if (chat.isPinned) {
                  chatProvider.unpinChat(chat.id);
                } else {
                  chatProvider.pinChat(chat.id);
                }
              },
            ),
            
            ListTile(
              leading: Icon(
                chat.isMuted ? Icons.volume_up : Icons.volume_off,
                color: Colors.orange,
              ),
              title: Text(chat.isMuted ? 'إلغاء الكتم' : 'كتم الإشعارات'),
              onTap: () {
                Navigator.pop(context);
                final chatProvider = Provider.of<ChatProvider>(context, listen: false);
                if (chat.isMuted) {
                  chatProvider.unmuteChat(chat.id);
                } else {
                  chatProvider.muteChat(chat.id);
                }
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف المحادثة', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteChatDialog(chat);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteChatDialog(Chat chat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المحادثة'),
        content: Text('هل تريد حذف المحادثة مع ${chat.otherUser.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: تنفيذ حذف المحادثة
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تم حذف المحادثة مع ${chat.otherUser.name}')),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _startNewChat() {
    // TODO: فتح شاشة اختيار المستخدمين لبدء محادثة جديدة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء محادثة جديدة')),
    );
  }

  void _showChatOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.group_add, color: Colors.blue),
              title: const Text('إنشاء مجموعة'),
              onTap: () {
                Navigator.pop(context);
                // TODO: فتح شاشة إنشاء مجموعة
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('إنشاء مجموعة جديدة')),
                );
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.archive, color: Colors.orange),
              title: const Text('المحادثات المؤرشفة'),
              onTap: () {
                Navigator.pop(context);
                // TODO: فتح شاشة المحادثات المؤرشفة
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('المحادثات المؤرشفة')),
                );
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.settings, color: Colors.grey),
              title: const Text('إعدادات الدردشة'),
              onTap: () {
                Navigator.pop(context);
                // TODO: فتح إعدادات الدردشة
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('إعدادات الدردشة')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
