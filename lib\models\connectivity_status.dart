enum ConnectivityStatus {
  connected,
  disconnected,
  reconnecting,
  unknown,
}

enum ConnectionType {
  wifi,
  mobile,
  ethernet,
  bluetooth,
  vpn,
  other,
  none,
}

class NetworkState {
  final ConnectivityStatus status;
  final ConnectionType type;
  final bool isOnline;
  final DateTime lastConnected;
  final DateTime? lastDisconnected;
  final int reconnectAttempts;
  final Duration? reconnectDelay;
  final String? errorMessage;

  const NetworkState({
    required this.status,
    required this.type,
    required this.isOnline,
    required this.lastConnected,
    this.lastDisconnected,
    this.reconnectAttempts = 0,
    this.reconnectDelay,
    this.errorMessage,
  });

  NetworkState copyWith({
    ConnectivityStatus? status,
    ConnectionType? type,
    bool? isOnline,
    DateTime? lastConnected,
    DateTime? lastDisconnected,
    int? reconnectAttempts,
    Duration? reconnectDelay,
    String? errorMessage,
  }) {
    return NetworkState(
      status: status ?? this.status,
      type: type ?? this.type,
      isOnline: isOnline ?? this.isOnline,
      lastConnected: lastConnected ?? this.lastConnected,
      lastDisconnected: lastDisconnected ?? this.lastDisconnected,
      reconnectAttempts: reconnectAttempts ?? this.reconnectAttempts,
      reconnectDelay: reconnectDelay ?? this.reconnectDelay,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  String get statusDisplayName {
    switch (status) {
      case ConnectivityStatus.connected:
        return 'متصل';
      case ConnectivityStatus.disconnected:
        return 'غير متصل';
      case ConnectivityStatus.reconnecting:
        return 'جارٍ إعادة الاتصال...';
      case ConnectivityStatus.unknown:
        return 'حالة غير معروفة';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case ConnectionType.wifi:
        return 'Wi-Fi';
      case ConnectionType.mobile:
        return 'بيانات الجوال';
      case ConnectionType.ethernet:
        return 'إيثرنت';
      case ConnectionType.bluetooth:
        return 'بلوتوث';
      case ConnectionType.vpn:
        return 'VPN';
      case ConnectionType.other:
        return 'أخرى';
      case ConnectionType.none:
        return 'لا يوجد اتصال';
    }
  }

  String get connectionQualityText {
    if (!isOnline) return 'لا يوجد اتصال';
    
    switch (type) {
      case ConnectionType.wifi:
        return 'اتصال ممتاز';
      case ConnectionType.mobile:
        return 'اتصال جيد';
      case ConnectionType.ethernet:
        return 'اتصال ممتاز';
      default:
        return 'اتصال متاح';
    }
  }

  Duration get timeSinceLastConnection {
    if (isOnline) return Duration.zero;
    return DateTime.now().difference(lastDisconnected ?? lastConnected);
  }

  String get formattedTimeSinceLastConnection {
    final duration = timeSinceLastConnection;
    
    if (duration.inDays > 0) {
      return 'منذ ${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return 'منذ ${duration.inHours} ساعة';
    } else if (duration.inMinutes > 0) {
      return 'منذ ${duration.inMinutes} دقيقة';
    } else {
      return 'منذ لحظات';
    }
  }

  static NetworkState get initial => NetworkState(
    status: ConnectivityStatus.unknown,
    type: ConnectionType.none,
    isOnline: false,
    lastConnected: DateTime.now(),
  );

  @override
  String toString() {
    return 'NetworkState(status: $status, type: $type, isOnline: $isOnline, '
           'reconnectAttempts: $reconnectAttempts)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is NetworkState &&
           other.status == status &&
           other.type == type &&
           other.isOnline == isOnline;
  }

  @override
  int get hashCode {
    return status.hashCode ^ type.hashCode ^ isOnline.hashCode;
  }
}

class CachedData {
  final String key;
  final Map<String, dynamic> data;
  final DateTime cachedAt;
  final Duration? ttl; // Time to live
  final String? etag;

  const CachedData({
    required this.key,
    required this.data,
    required this.cachedAt,
    this.ttl,
    this.etag,
  });

  bool get isExpired {
    if (ttl == null) return false;
    return DateTime.now().difference(cachedAt) > ttl!;
  }

  Duration get age => DateTime.now().difference(cachedAt);

  CachedData copyWith({
    String? key,
    Map<String, dynamic>? data,
    DateTime? cachedAt,
    Duration? ttl,
    String? etag,
  }) {
    return CachedData(
      key: key ?? this.key,
      data: data ?? this.data,
      cachedAt: cachedAt ?? this.cachedAt,
      ttl: ttl ?? this.ttl,
      etag: etag ?? this.etag,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'data': data,
      'cachedAt': cachedAt.toIso8601String(),
      'ttl': ttl?.inMilliseconds,
      'etag': etag,
    };
  }

  factory CachedData.fromJson(Map<String, dynamic> json) {
    return CachedData(
      key: json['key'],
      data: Map<String, dynamic>.from(json['data']),
      cachedAt: DateTime.parse(json['cachedAt']),
      ttl: json['ttl'] != null ? Duration(milliseconds: json['ttl']) : null,
      etag: json['etag'],
    );
  }
}
