import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/social_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/post_card.dart';
import '../models/user.dart';
import '../services/mock_data_service.dart';
import '../widgets/smart_avatar.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatefulWidget {
  final String userId;
  final String userName;

  const ProfileScreen({
    super.key,
    required this.userId,
    required this.userName,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isFollowing = false;
  User? _userInfo;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this); // تقليل عدد التبويبات
    _loadUserInfo();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserInfo() {
    final mockDataService = MockDataService();
    if (widget.userId == 'current_user') {
      // تحميل بيانات المستخدم الحالي
      _userInfo = User(
        id: 'current_user',
        name: widget.userName,
        email: '<EMAIL>',
        joinDate: DateTime.now().subtract(const Duration(days: 30)),
        bio: 'مرحباً بكم في ملفي الشخصي! 👋',
        city: 'الرياض',
        country: 'السعودية',
        work: 'مستخدم تطبيق Arzawo',
        education: 'جامعة',
        followersCount: 42,
        followingCount: 38,
        postsCount: 15,
        phone: '+966 50 123 4567',
        birthDate: DateTime(1995, 5, 15),
        gender: 'male',
        relationship: 'single',
        website: 'https://arzawo.app',
        interests: ['التقنية', 'البرمجة', 'السفر', 'القراءة'],
        languages: ['العربية', 'الإنجليزية'],
      );
    } else {
      // تحميل بيانات المستخدمين الآخرين
      final users = mockDataService.mockUsers;
      _userInfo = users.firstWhere(
        (user) => user.id == widget.userId,
        orElse: () => User(
          id: widget.userId,
          name: widget.userName,
          email: '${widget.userName.toLowerCase()}@example.com',
          joinDate: DateTime.now().subtract(const Duration(days: 100)),
          bio: 'مستخدم في تطبيق Arzawo',
          followersCount: 150,
          followingCount: 89,
          postsCount: 25,
        ),
      );
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              floating: false,
              pinned: true,
              backgroundColor: AppTheme.primaryColor,
              flexibleSpace: FlexibleSpaceBar(
                background: _buildProfileHeader(),
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onPressed: _showMoreOptions,
                ),
              ],
            ),
            SliverPersistentHeader(
              delegate: _SliverAppBarDelegate(
                TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.primaryColor,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: AppTheme.primaryColor,
                  tabs: const [
                    Tab(text: 'المنشورات'),
                    Tab(text: 'الصور'),
                    Tab(text: 'المعلومات'),
                  ],
                ),
              ),
              pinned: true,
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildPostsTab(),
            _buildPhotosTab(),
            _buildInfoTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    if (_userInfo == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Stack(
      children: [
        // صورة الغلاف
        Container(
          height: 200,
          width: double.infinity,
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          child: _userInfo!.coverImageUrl != null
              ? Image.network(
                  _userInfo!.coverImageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[100],
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.photo_camera_outlined,
                              size: 40,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 8),
                            Text(
                              'لا توجد صورة غلاف',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                )
              : Container(
                  color: Colors.grey[100],
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.photo_camera_outlined,
                          size: 40,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'لا توجد صورة غلاف',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
        ),

        // زر تعديل صورة الغلاف (للمستخدم الحالي فقط)
        if (widget.userId == 'current_user')
          Positioned(
            top: 10,
            right: 10,
            child: SafeArea(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(Icons.camera_alt, color: Colors.white),
                  onPressed: _changeCoverPhoto,
                ),
              ),
            ),
          ),

        // المحتوى الرئيسي
        Positioned.fill(
          child: Container(
            color: Colors.white,
            child: SafeArea(
              child: Column(
                children: [
                const SizedBox(height: 120),

                // صورة الملف الشخصي مع قلم التعديل
                Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.3),
                            spreadRadius: 2,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: SmartAvatarWithText(
                        user: _userInfo,
                        radius: 60,
                      ),
                    ),

                  // قلم التعديل (للمستخدم الحالي فقط)
                  if (widget.userId == 'current_user')
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.edit, color: Colors.white, size: 20),
                          onPressed: _editProfile,
                        ),
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 16),

              // اسم المستخدم مع علامة التوثيق
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _userInfo!.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  if (_userInfo!.isVerified) ...[
                    const SizedBox(width: 8),
                    const Icon(
                      Icons.verified,
                      color: Colors.blue,
                      size: 24,
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 8),

              // النبذة الشخصية
              if (_userInfo!.bio != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    _userInfo!.bio!,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

              const SizedBox(height: 8),

              // معلومات إضافية
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (_userInfo!.city != null) ...[
                    Icon(Icons.location_on, color: Colors.grey[600], size: 16),
                    const SizedBox(width: 4),
                    Text(
                      _userInfo!.city!,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                  if (_userInfo!.work != null) ...[
                    if (_userInfo!.city != null) Text(' • ', style: TextStyle(color: Colors.grey[600])),
                    Icon(Icons.work, color: Colors.grey[600], size: 16),
                    const SizedBox(width: 4),
                    Text(
                      _userInfo!.work!,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 20),

              // إحصائيات المستخدم
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStatColumn(_userInfo!.postsCount.toString(), 'منشور'),
                    Container(
                      height: 40,
                      width: 1,
                      color: Colors.grey[300],
                    ),
                    _buildStatColumn(_formatCount(_userInfo!.followersCount), 'متابع'),
                    Container(
                      height: 40,
                      width: 1,
                      color: Colors.grey[300],
                    ),
                    _buildStatColumn(_formatCount(_userInfo!.followingCount), 'متابَع'),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // أزرار الإجراءات
              if (widget.userId != 'current_user') ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _toggleFollow,
                          icon: Icon(_isFollowing ? Icons.check : Icons.person_add),
                          label: Text(_isFollowing ? 'متابَع' : 'متابعة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _isFollowing ? Colors.grey[300] : AppTheme.primaryColor,
                            foregroundColor: _isFollowing ? Colors.black87 : Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _sendMessage,
                          icon: const Icon(Icons.message),
                          label: const Text('رسالة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[200],
                            foregroundColor: Colors.black87,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _editProfile,
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل الملف الشخصي'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatColumn(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildPostsTab() {
    return Consumer<SocialProvider>(
      builder: (context, socialProvider, child) {
        final userPosts = socialProvider.posts
            .where((post) => post.authorId == widget.userId)
            .toList();

        if (userPosts.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.post_add, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد منشورات بعد',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8),
          itemCount: userPosts.length,
          itemBuilder: (context, index) {
            return PostCard(
              key: ValueKey(userPosts[index].id),
              post: userPosts[index],
            );
          },
          addAutomaticKeepAlives: true,
          addRepaintBoundaries: true,
        );
      },
    );
  }

  Widget _buildPhotosTab() {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: 12,
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.image,
            size: 40,
            color: Colors.grey,
          ),
        );
      },
    );
  }



  Widget _buildInfoTab() {
    if (_userInfo == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // المعلومات الأساسية
          _buildInfoSection(
            'المعلومات الأساسية',
            [
              if (_userInfo!.email.isNotEmpty)
                _buildInfoItem(Icons.email, 'البريد الإلكتروني', _userInfo!.email),
              if (_userInfo!.phone != null)
                _buildInfoItem(Icons.phone, 'رقم الهاتف', _userInfo!.phone!),
              if (_userInfo!.bio != null)
                _buildInfoItem(Icons.info, 'النبذة الشخصية', _userInfo!.bio!),
            ],
          ),

          // الموقع
          if (_userInfo!.city != null || _userInfo!.country != null)
            _buildInfoSection(
              'الموقع',
              [
                if (_userInfo!.city != null)
                  _buildInfoItem(Icons.location_city, 'المدينة', _userInfo!.city!),
                if (_userInfo!.country != null)
                  _buildInfoItem(Icons.flag, 'البلد', _userInfo!.country!),
              ],
            ),

          // المعلومات الشخصية
          _buildInfoSection(
            'المعلومات الشخصية',
            [
              if (_userInfo!.birthDate != null)
                _buildInfoItem(
                  Icons.cake,
                  'تاريخ الميلاد',
                  '${_userInfo!.birthDate!.day}/${_userInfo!.birthDate!.month}/${_userInfo!.birthDate!.year}',
                ),
              if (_userInfo!.age != null)
                _buildInfoItem(Icons.person, 'العمر', '${_userInfo!.age} سنة'),
              if (_userInfo!.gender != null)
                _buildInfoItem(
                  Icons.person_outline,
                  'الجنس',
                  _userInfo!.gender == 'male' ? 'ذكر' : 'أنثى',
                ),
              if (_userInfo!.relationship != null)
                _buildInfoItem(
                  Icons.favorite,
                  'الحالة الاجتماعية',
                  _getRelationshipText(_userInfo!.relationship!),
                ),
            ],
          ),

          // العمل والتعليم
          if (_userInfo!.work != null || _userInfo!.education != null)
            _buildInfoSection(
              'العمل والتعليم',
              [
                if (_userInfo!.work != null)
                  _buildInfoItem(Icons.work, 'العمل', _userInfo!.work!),
                if (_userInfo!.education != null)
                  _buildInfoItem(Icons.school, 'التعليم', _userInfo!.education!),
              ],
            ),

          // الاتصال
          if (_userInfo!.website != null)
            _buildInfoSection(
              'الاتصال',
              [
                _buildInfoItem(Icons.web, 'الموقع الإلكتروني', _userInfo!.website!),
              ],
            ),

          // معلومات الحساب
          _buildInfoSection(
            'معلومات الحساب',
            [
              _buildInfoItem(
                Icons.calendar_today,
                'تاريخ الانضمام',
                '${_userInfo!.joinDate.day}/${_userInfo!.joinDate.month}/${_userInfo!.joinDate.year}',
              ),
              if (_userInfo!.isVerified)
                _buildInfoItem(Icons.verified, 'الحساب', 'موثق ✓'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> items) {
    if (items.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 24, bottom: 12),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  String _getRelationshipText(String relationship) {
    switch (relationship) {
      case 'single':
        return 'أعزب';
      case 'married':
        return 'متزوج';
      case 'divorced':
        return 'مطلق';
      case 'widowed':
        return 'أرمل';
      default:
        return relationship;
    }
  }

  void _toggleFollow() {
    setState(() {
      _isFollowing = !_isFollowing;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isFollowing ? 'تم متابعة ${widget.userName}! 👥' : 'تم إلغاء متابعة ${widget.userName}'),
        backgroundColor: _isFollowing ? Colors.blue : Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _sendMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح محادثة مع ${widget.userName}! 💬'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _editProfile() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditProfileScreen(user: _userInfo!),
      ),
    );

    if (result == true) {
      // إعادة تحميل بيانات المستخدم بعد التعديل
      _loadUserInfo();
    }
  }

  void _changeCoverPhoto() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة تغيير صورة الغلاف قريباً! 📸'),
        backgroundColor: AppTheme.primaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            if (widget.userId == 'current_user') ...[
              // خيارات الملف الشخصي الخاص
              ListTile(
                leading: const Icon(Icons.edit, color: Colors.blue),
                title: const Text('تعديل الملف الشخصي'),
                onTap: () {
                  Navigator.pop(context);
                  _editProfile();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera, color: Colors.green),
                title: const Text('تغيير صورة الملف الشخصي'),
                onTap: () {
                  Navigator.pop(context);
                  _changeProfilePhoto();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.purple),
                title: const Text('تغيير صورة الغلاف'),
                onTap: () {
                  Navigator.pop(context);
                  _changeCoverPhoto();
                },
              ),
              ListTile(
                leading: const Icon(Icons.archive, color: Colors.orange),
                title: const Text('أرشيف المنشورات'),
                onTap: () {
                  Navigator.pop(context);
                  _showArchive();
                },
              ),
              ListTile(
                leading: const Icon(Icons.settings, color: Colors.grey),
                title: const Text('إعدادات الخصوصية'),
                onTap: () {
                  Navigator.pop(context);
                  _showPrivacySettings();
                },
              ),
              ListTile(
                leading: const Icon(Icons.qr_code, color: Colors.indigo),
                title: const Text('رمز QR للملف الشخصي'),
                onTap: () {
                  Navigator.pop(context);
                  _showQRCode();
                },
              ),
            ] else ...[
              // خيارات ملف شخصي آخر
              ListTile(
                leading: const Icon(Icons.message, color: Colors.blue),
                title: const Text('إرسال رسالة'),
                onTap: () {
                  Navigator.pop(context);
                  _sendMessage();
                },
              ),
              ListTile(
                leading: const Icon(Icons.person_add, color: Colors.green),
                title: const Text('إضافة صديق'),
                onTap: () {
                  Navigator.pop(context);
                  _addFriend();
                },
              ),
              ListTile(
                leading: const Icon(Icons.notifications, color: Colors.orange),
                title: const Text('تشغيل الإشعارات'),
                onTap: () {
                  Navigator.pop(context);
                  _toggleNotifications();
                },
              ),
              ListTile(
                leading: const Icon(Icons.copy, color: Colors.purple),
                title: const Text('نسخ رابط الملف الشخصي'),
                onTap: () {
                  Navigator.pop(context);
                  _copyProfileLink();
                },
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.visibility_off, color: Colors.grey),
                title: const Text('إخفاء من الخلاصة'),
                onTap: () {
                  Navigator.pop(context);
                  _hideFromFeed();
                },
              ),
              ListTile(
                leading: const Icon(Icons.block, color: Colors.red),
                title: const Text('حظر المستخدم'),
                onTap: () {
                  Navigator.pop(context);
                  _blockUser();
                },
              ),
              ListTile(
                leading: const Icon(Icons.report, color: Colors.red),
                title: const Text('الإبلاغ عن المستخدم'),
                onTap: () {
                  Navigator.pop(context);
                  _reportUser();
                },
              ),
            ],

            // خيارات مشتركة
            const Divider(),
            ListTile(
              leading: const Icon(Icons.share, color: Colors.blue),
              title: const Text('مشاركة الملف الشخصي'),
              onTap: () {
                Navigator.pop(context);
                _shareProfile();
              },
            ),
            ListTile(
              leading: const Icon(Icons.search, color: Colors.grey),
              title: const Text('البحث في المنشورات'),
              onTap: () {
                Navigator.pop(context);
                _searchInPosts();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _blockUser() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حظر ${widget.userName}'),
        content: Text('هل تريد حظر ${widget.userName}؟ لن تتمكن من رؤية منشوراته أو ملفه الشخصي.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حظر ${widget.userName}! 🚫'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حظر', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _reportUser() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم الإبلاغ عن ${widget.userName}! 🚨'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  // دوال الملف الشخصي الخاص
  void _changeProfilePhoto() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم التقاط صورة جديدة!')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم اختيار صورة من المعرض!')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showArchive() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('شاشة أرشيف المنشورات قريباً!')),
    );
  }

  void _showPrivacySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('شاشة إعدادات الخصوصية قريباً!')),
    );
  }

  void _showQRCode() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رمز QR للملف الشخصي'),
        content: Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(
              Icons.qr_code,
              size: 100,
              color: Colors.grey,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حفظ رمز QR!')),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // دوال ملف شخصي آخر
  void _addFriend() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إرسال طلب صداقة إلى ${widget.userName}! 👥'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _toggleNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تشغيل الإشعارات لـ ${widget.userName}! 🔔'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _copyProfileLink() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ رابط الملف الشخصي! 🔗'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _hideFromFeed() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إخفاء منشورات ${widget.userName} من الخلاصة'),
        backgroundColor: Colors.grey,
      ),
    );
  }

  // دوال مشتركة
  void _shareProfile() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'مشاركة الملف الشخصي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _ShareButton(
                  icon: Icons.message,
                  label: 'رسالة',
                  color: Colors.blue,
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم مشاركة الملف عبر الرسائل!')),
                    );
                  },
                ),
                _ShareButton(
                  icon: Icons.copy,
                  label: 'نسخ الرابط',
                  color: Colors.grey,
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم نسخ رابط الملف الشخصي!')),
                    );
                  },
                ),
                _ShareButton(
                  icon: Icons.share,
                  label: 'تطبيقات أخرى',
                  color: Colors.green,
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم فتح قائمة المشاركة!')),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _searchInPosts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('شاشة البحث في المنشورات قريباً!')),
    );
  }
}

class _ShareButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _ShareButton({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(icon, color: color, size: 30),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}
