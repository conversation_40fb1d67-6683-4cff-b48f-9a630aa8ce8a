import 'package:flutter/material.dart';
import '../models/post.dart';
import '../widgets/smart_avatar.dart';
import 'facebook_profile_screen.dart';

class SharesDetailScreen extends StatefulWidget {
  final Post post;

  const SharesDetailScreen({
    super.key,
    required this.post,
  });

  @override
  State<SharesDetailScreen> createState() => _SharesDetailScreenState();
}

class _SharesDetailScreenState extends State<SharesDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  Map<String, List<PostShare>> _groupedShares = {};
  int _totalShares = 0;

  @override
  void initState() {
    super.initState();
    _groupShares();
    _tabController = TabController(
      length: _groupedShares.length + 1, // +1 للتبويب "الكل"
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _groupShares() {
    _groupedShares.clear();
    _totalShares = widget.post.shares.length;

    for (final share in widget.post.shares) {
      if (_groupedShares[share.shareType] == null) {
        _groupedShares[share.shareType] = [];
      }
      _groupedShares[share.shareType]!.add(share);
    }

    // ترتيب المشاركات حسب العدد
    final sortedEntries = _groupedShares.entries.toList();
    sortedEntries.sort((a, b) => b.value.length.compareTo(a.value.length));
    _groupedShares = Map.fromEntries(sortedEntries);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'المشاركات',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size(double.infinity, 60),
          child: _buildTabBar(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllSharesTab(),
          ..._groupedShares.entries.map((entry) =>
              _buildShareTab(entry.key, entry.value)),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: const Color(0xFF1877F2),
        indicatorWeight: 3,
        labelColor: const Color(0xFF1877F2),
        unselectedLabelColor: Colors.grey[600],
        tabs: [
          // تبويب "الكل"
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('الكل'),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$_totalShares',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // تبويبات أنواع المشاركات
          ..._groupedShares.entries.map((entry) {
            final shareTypeData = _getShareTypeData(entry.key);
            return Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    shareTypeData['icon'],
                    size: 20,
                    color: shareTypeData['color'],
                  ),
                  const SizedBox(width: 8),
                  Text(shareTypeData['name']),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: shareTypeData['color'].withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${entry.value.length}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: shareTypeData['color'],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildAllSharesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.post.shares.length,
      itemBuilder: (context, index) {
        final share = widget.post.shares[index];
        return _buildShareItem(share);
      },
    );
  }

  Widget _buildShareTab(String shareType, List<PostShare> shares) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: shares.length,
      itemBuilder: (context, index) {
        final share = shares[index];
        return _buildShareItem(share);
      },
    );
  }

  Widget _buildShareItem(PostShare share) {
    final shareTypeData = _getShareTypeData(share.shareType);
    final userName = _getUserName(share.userId);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToProfile(share.userId),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // الصورة الشخصية
              Stack(
                children: [
                  SmartAvatar(
                    name: userName,
                    radius: 24,
                  ),
                  // أيقونة نوع المشاركة
                  Positioned(
                    bottom: -2,
                    right: -2,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: shareTypeData['color'],
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: Icon(
                        shareTypeData['icon'],
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(width: 12),
              
              // معلومات المستخدم
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _getTimeAgo(share.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    if (share.targetId != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        _getTargetDescription(share.shareType, share.targetId!),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // نوع المشاركة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: shareTypeData['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: shareTypeData['color'].withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      shareTypeData['icon'],
                      size: 16,
                      color: shareTypeData['color'],
                    ),
                    const SizedBox(width: 6),
                    Text(
                      shareTypeData['name'],
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: shareTypeData['color'],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getShareTypeData(String shareType) {
    switch (shareType) {
      case 'external':
        return {
          'name': 'مشاركة خارجية',
          'icon': Icons.share,
          'color': const Color(0xFF1877F2),
        };
      case 'message':
        return {
          'name': 'رسالة',
          'icon': Icons.message,
          'color': const Color(0xFF00BCD4),
        };
      case 'group':
        return {
          'name': 'مجموعة',
          'icon': Icons.group,
          'color': const Color(0xFF4CAF50),
        };
      case 'story':
        return {
          'name': 'قصة',
          'icon': Icons.auto_stories,
          'color': const Color(0xFFFF9800),
        };
      default:
        return {
          'name': 'مشاركة',
          'icon': Icons.share,
          'color': Colors.grey,
        };
    }
  }

  void _navigateToProfile(String userId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FacebookProfileScreen(
          userId: userId,
          userName: _getUserName(userId),
        ),
      ),
    );
  }

  String _getUserName(String userId) {
    switch (userId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  String _getTargetDescription(String shareType, String targetId) {
    switch (shareType) {
      case 'group':
        return 'في مجموعة: ${_getGroupName(targetId)}';
      case 'message':
        return 'في محادثة مع: ${_getUserName(targetId)}';
      default:
        return '';
    }
  }

  String _getGroupName(String groupId) {
    switch (groupId) {
      case 'group1': return 'مجموعة التقنية';
      case 'group2': return 'مجموعة الأصدقاء';
      default: return 'مجموعة';
    }
  }
}
