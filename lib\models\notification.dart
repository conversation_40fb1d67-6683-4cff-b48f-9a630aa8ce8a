enum NotificationType {
  like,              // إعجاب بمنشور
  comment,           // تعليق على منشور
  reply,             // رد على تعليق
  share,             // مشاركة منشور
  mention,           // ذكر في منشور أو تعليق
  follow,            // متابعة جديدة
  friendRequest,     // طلب صداقة
  groupPost,         // منشور جديد في مجموعة
  groupInvite,       // دعوة للانضمام لمجموعة
  groupJoinRequest,  // طلب انضمام لمجموعة أديرها
  storyView,         // مشاهدة قصة
  message,           // رسالة جديدة
  birthday,          // عيد ميلاد
  memory,            // ذكرى
  liveStream,        // بث مباشر
  event,             // حدث
  reaction,          // تفاعل آخر (حب، ضحك، إلخ)
}

enum NotificationStatus {
  unread,
  read,
  archived,
}

class AppNotification {
  final String id;
  final String userId; // المستخدم الذي سيستقبل الإشعار
  final String fromUserId; // المستخدم الذي قام بالفعل
  final NotificationType type;
  final String title;
  final String message;
  final String? postId;
  final String? commentId;
  final String? groupId;
  final String? storyId;
  final String? chatId;
  final String? imageUrl;
  final DateTime timestamp;
  final NotificationStatus status;
  final Map<String, dynamic>? data;
  final List<String>? additionalUserIds; // للإشعارات المجمعة
  final int? count; // عدد التفاعلات المجمعة
  final String? actionUrl; // رابط التوجيه

  const AppNotification({
    required this.id,
    required this.userId,
    required this.fromUserId,
    required this.type,
    required this.title,
    required this.message,
    this.postId,
    this.commentId,
    this.groupId,
    this.storyId,
    this.chatId,
    this.imageUrl,
    required this.timestamp,
    this.status = NotificationStatus.unread,
    this.data,
    this.additionalUserIds,
    this.count,
    this.actionUrl,
  });

  AppNotification copyWith({
    String? id,
    String? userId,
    String? fromUserId,
    NotificationType? type,
    String? title,
    String? message,
    String? postId,
    String? commentId,
    String? groupId,
    String? storyId,
    String? chatId,
    String? imageUrl,
    DateTime? timestamp,
    NotificationStatus? status,
    Map<String, dynamic>? data,
    List<String>? additionalUserIds,
    int? count,
    String? actionUrl,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fromUserId: fromUserId ?? this.fromUserId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      postId: postId ?? this.postId,
      commentId: commentId ?? this.commentId,
      groupId: groupId ?? this.groupId,
      storyId: storyId ?? this.storyId,
      chatId: chatId ?? this.chatId,
      imageUrl: imageUrl ?? this.imageUrl,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      data: data ?? this.data,
      additionalUserIds: additionalUserIds ?? this.additionalUserIds,
      count: count ?? this.count,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  // دوال مساعدة
  bool get isRead => status == NotificationStatus.read;
  bool get isUnread => status == NotificationStatus.unread;
  bool get isArchived => status == NotificationStatus.archived;

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  String get typeIcon {
    switch (type) {
      case NotificationType.like:
        return '❤️';
      case NotificationType.comment:
        return '💬';
      case NotificationType.reply:
        return '↩️';
      case NotificationType.share:
        return '🔄';
      case NotificationType.mention:
        return '🏷️';
      case NotificationType.follow:
        return '👥';
      case NotificationType.friendRequest:
        return '🤝';
      case NotificationType.groupPost:
        return '👥';
      case NotificationType.groupInvite:
        return '📨';
      case NotificationType.groupJoinRequest:
        return '📋';
      case NotificationType.storyView:
        return '👁️';
      case NotificationType.message:
        return '💌';
      case NotificationType.birthday:
        return '🎂';
      case NotificationType.memory:
        return '📸';
      case NotificationType.liveStream:
        return '📺';
      case NotificationType.event:
        return '📅';
      case NotificationType.reaction:
        return '😍';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'fromUserId': fromUserId,
      'type': type.name,
      'title': title,
      'message': message,
      'postId': postId,
      'commentId': commentId,
      'groupId': groupId,
      'storyId': storyId,
      'chatId': chatId,
      'imageUrl': imageUrl,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'data': data,
      'additionalUserIds': additionalUserIds,
      'count': count,
      'actionUrl': actionUrl,
    };
  }

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'],
      userId: json['userId'],
      fromUserId: json['fromUserId'],
      type: NotificationType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      message: json['message'],
      postId: json['postId'],
      commentId: json['commentId'],
      groupId: json['groupId'],
      storyId: json['storyId'],
      chatId: json['chatId'],
      imageUrl: json['imageUrl'],
      timestamp: DateTime.parse(json['timestamp']),
      status: NotificationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => NotificationStatus.unread,
      ),
      data: json['data'],
      additionalUserIds: json['additionalUserIds']?.cast<String>(),
      count: json['count'],
      actionUrl: json['actionUrl'],
    );
  }

  // Factory methods لإنشاء إشعارات مختلفة
  factory AppNotification.like({
    required String id,
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id,
      userId: userId,
      fromUserId: fromUserId,
      type: NotificationType.like,
      title: 'إعجاب جديد',
      message: 'أعجب $fromUserName بمنشورك',
      postId: postId,
      imageUrl: imageUrl,
      timestamp: DateTime.now(),
    );
  }

  factory AppNotification.comment({
    required String id,
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    required String comment,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id,
      userId: userId,
      fromUserId: fromUserId,
      type: NotificationType.comment,
      title: 'تعليق جديد',
      message: 'علق $fromUserName على منشورك: "$comment"',
      postId: postId,
      imageUrl: imageUrl,
      timestamp: DateTime.now(),
    );
  }

  factory AppNotification.share({
    required String id,
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String postId,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id,
      userId: userId,
      fromUserId: fromUserId,
      type: NotificationType.share,
      title: 'مشاركة جديدة',
      message: 'شارك $fromUserName منشورك',
      postId: postId,
      imageUrl: imageUrl,
      timestamp: DateTime.now(),
    );
  }

  factory AppNotification.storyView({
    required String id,
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String storyId,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id,
      userId: userId,
      fromUserId: fromUserId,
      type: NotificationType.storyView,
      title: 'مشاهدة قصة',
      message: 'شاهد $fromUserName قصتك',
      storyId: storyId,
      imageUrl: imageUrl,
      timestamp: DateTime.now(),
    );
  }
}
