import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/message.dart';
import '../theme/app_theme.dart';

class SwipeableMessage extends StatefulWidget {
  final Widget child;
  final Message message;
  final bool isMe;
  final VoidCallback? onReply;
  final VoidCallback? onDelete;
  final VoidCallback? onArchive;
  final VoidCallback? onMarkUnread;

  const SwipeableMessage({
    super.key,
    required this.child,
    required this.message,
    required this.isMe,
    this.onReply,
    this.onDelete,
    this.onArchive,
    this.onMarkUnread,
  });

  @override
  State<SwipeableMessage> createState() => _SwipeableMessageState();
}

class _SwipeableMessageState extends State<SwipeableMessage>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _actionController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _actionAnimation;
  
  double _dragExtent = 0;
  bool _dragUnderway = false;
  
  static const double _kActionsThreshold = 80.0;
  static const double _kFastThreshold = 150.0;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _actionController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.3, 0),
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOut,
    ));
    
    _actionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _actionController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _slideController.dispose();
    _actionController.dispose();
    super.dispose();
  }

  void _handleDragStart(DragStartDetails details) {
    _dragUnderway = true;
    _slideController.stop();
    _actionController.stop();
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_dragUnderway) return;
    
    final delta = details.primaryDelta ?? 0;
    final oldDragExtent = _dragExtent;
    
    setState(() {
      _dragExtent += delta;
      _dragExtent = _dragExtent.clamp(-200.0, 200.0);
    });
    
    // تحديث الرسوم المتحركة
    if (_dragExtent.abs() > _kActionsThreshold && oldDragExtent.abs() <= _kActionsThreshold) {
      HapticFeedback.lightImpact();
      _actionController.forward();
    } else if (_dragExtent.abs() <= _kActionsThreshold && oldDragExtent.abs() > _kActionsThreshold) {
      _actionController.reverse();
    }
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_dragUnderway) return;
    
    _dragUnderway = false;
    
    // تحديد الإجراء بناءً على اتجاه ومسافة السحب
    if (_dragExtent.abs() > _kFastThreshold) {
      // سحب سريع - تنفيذ الإجراء مباشرة
      _executeAction();
    } else if (_dragExtent.abs() > _kActionsThreshold) {
      // إظهار خيارات الإجراءات
      _showActionButtons();
    } else {
      // العودة للوضع الطبيعي
      _resetPosition();
    }
  }

  void _executeAction() {
    HapticFeedback.mediumImpact();
    
    if (_dragExtent > 0) {
      // سحب يمين - رد سريع
      widget.onReply?.call();
    } else {
      // سحب يسار - حذف سريع
      widget.onDelete?.call();
    }
    
    _resetPosition();
  }

  void _showActionButtons() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_dragExtent > 0) ...[
              // خيارات السحب يمين
              ListTile(
                leading: const Icon(Icons.reply, color: Colors.blue),
                title: const Text('رد على الرسالة'),
                onTap: () {
                  Navigator.pop(context);
                  widget.onReply?.call();
                  _resetPosition();
                },
              ),
            ] else ...[
              // خيارات السحب يسار
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('حذف الرسالة'),
                onTap: () {
                  Navigator.pop(context);
                  widget.onDelete?.call();
                  _resetPosition();
                },
              ),
              ListTile(
                leading: const Icon(Icons.archive, color: Colors.orange),
                title: const Text('أرشفة الرسالة'),
                onTap: () {
                  Navigator.pop(context);
                  widget.onArchive?.call();
                  _resetPosition();
                },
              ),
              ListTile(
                leading: const Icon(Icons.mark_as_unread, color: Colors.grey),
                title: const Text('تحديد كغير مقروءة'),
                onTap: () {
                  Navigator.pop(context);
                  widget.onMarkUnread?.call();
                  _resetPosition();
                },
              ),
            ],
          ],
        ),
      ),
    ).then((_) => _resetPosition());
  }

  void _resetPosition() {
    setState(() {
      _dragExtent = 0;
    });
    _slideController.reverse();
    _actionController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragStart: _handleDragStart,
      onHorizontalDragUpdate: _handleDragUpdate,
      onHorizontalDragEnd: _handleDragEnd,
      child: Stack(
        children: [
          // خلفية الإجراءات
          if (_dragExtent.abs() > 10)
            Positioned.fill(
              child: Container(
                color: _getBackgroundColor(),
                child: Row(
                  mainAxisAlignment: _dragExtent > 0 
                      ? MainAxisAlignment.start 
                      : MainAxisAlignment.end,
                  children: [
                    if (_dragExtent > 0) ...[
                      // أيقونة الرد (يمين)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: AnimatedBuilder(
                          animation: _actionAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 0.8 + (0.4 * _actionAnimation.value),
                              child: Icon(
                                Icons.reply,
                                color: Colors.white,
                                size: 24,
                              ),
                            );
                          },
                        ),
                      ),
                    ] else ...[
                      // أيقونات الحذف والأرشفة (يسار)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: AnimatedBuilder(
                          animation: _actionAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 0.8 + (0.4 * _actionAnimation.value),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.delete,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                  const SizedBox(width: 16),
                                  Icon(
                                    Icons.archive,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          
          // الرسالة نفسها
          Transform.translate(
            offset: Offset(_dragExtent, 0),
            child: widget.child,
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    if (_dragExtent > 0) {
      // سحب يمين - أزرق للرد
      return Colors.blue.withValues(alpha: 0.8);
    } else {
      // سحب يسار - أحمر للحذف
      return Colors.red.withValues(alpha: 0.8);
    }
  }
}
