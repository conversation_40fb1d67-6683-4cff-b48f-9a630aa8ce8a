import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../models/group.dart';
import '../theme/app_theme.dart';

class GroupAboutTab extends StatelessWidget {
  final Group group;

  const GroupAboutTab({
    super.key,
    required this.group,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // وصف المجموعة
          _buildDescriptionSection(),
          
          const SizedBox(height: 24),
          
          // معلومات المجموعة
          _buildInfoSection(),
          
          const SizedBox(height: 24),
          
          // التصنيفات والتاغات
          if (group.tags.isNotEmpty) ...[
            _buildTagsSection(),
            const SizedBox(height: 24),
          ],
          
          // قواعد المجموعة
          _buildRulesSection(),
          
          const SizedBox(height: 24),
          
          // المشرفون
          _buildAdminsSection(),
          
          const SizedBox(height: 24),
          
          // إحصائيات المجموعة
          _buildStatsSection(),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'حول المجموعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Text(
              group.description,
              style: const TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'معلومات المجموعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // نوع الخصوصية
            _buildInfoRow(
              icon: _getPrivacyIcon(),
              title: 'الخصوصية',
              value: _getPrivacyText(),
              color: _getPrivacyColor(),
            ),
            
            const Divider(),
            
            // تاريخ الإنشاء
            _buildInfoRow(
              icon: Icons.calendar_today,
              title: 'تاريخ الإنشاء',
              value: _formatDate(group.createdAt),
            ),
            
            const Divider(),
            
            // عدد الأعضاء
            _buildInfoRow(
              icon: Icons.people,
              title: 'الأعضاء',
              value: '${group.memberCount} عضو',
            ),
            
            const Divider(),
            
            // عدد المنشورات
            _buildInfoRow(
              icon: Icons.forum,
              title: 'المنشورات',
              value: '${group.postCount} منشور',
            ),
            
            if (group.location != null) ...[
              const Divider(),
              
              // الموقع
              _buildInfoRow(
                icon: Icons.location_on,
                title: 'الموقع',
                value: group.location!,
              ),
            ],
            
            const Divider(),
            
            // آخر نشاط
            _buildInfoRow(
              icon: Icons.access_time,
              title: 'آخر نشاط',
              value: timeago.format(group.lastActivity, locale: 'ar'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String value,
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: color ?? Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: color ?? Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tag,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'التصنيفات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: group.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    '#$tag',
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRulesSection() {
    final rules = [
      'احترم جميع الأعضاء وتجنب التنمر أو التحرش',
      'لا تنشر محتوى غير لائق أو مسيء',
      'تجنب الرسائل المتكررة أو الإعلانات غير المرغوب فيها',
      'استخدم لغة مهذبة ومناسبة للجميع',
      'لا تشارك معلومات شخصية للآخرين بدون إذن',
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.rule,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'قواعد المجموعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            ...rules.asMap().entries.map((entry) {
              final index = entry.key;
              final rule = entry.value;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        rule,
                        style: const TextStyle(
                          fontSize: 14,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminsSection() {
    final admins = group.admins;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.admin_panel_settings,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'المشرفون',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            if (admins.isEmpty)
              const Text(
                'لا يوجد مشرفون',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              )
            else
              ...admins.map((admin) {
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: CircleAvatar(
                    backgroundColor: AppTheme.primaryColor,
                    child: Text(
                      _getUserName(admin.userId)[0].toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(_getUserName(admin.userId)),
                  subtitle: Text(
                    admin.role == GroupMemberRole.admin ? 'مدير' : 'مشرف',
                  ),
                  trailing: Icon(
                    Icons.verified,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات المجموعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    icon: Icons.people,
                    title: 'الأعضاء',
                    value: '${group.memberCount}',
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    icon: Icons.forum,
                    title: 'المنشورات',
                    value: '${group.postCount}',
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPrivacyIcon() {
    switch (group.privacy) {
      case GroupPrivacy.public:
        return Icons.public;
      case GroupPrivacy.private:
        return Icons.lock_outline;
      case GroupPrivacy.secret:
        return Icons.lock;
    }
  }

  String _getPrivacyText() {
    switch (group.privacy) {
      case GroupPrivacy.public:
        return 'مجموعة عامة';
      case GroupPrivacy.private:
        return 'مجموعة خاصة';
      case GroupPrivacy.secret:
        return 'مجموعة سرية';
    }
  }

  Color _getPrivacyColor() {
    switch (group.privacy) {
      case GroupPrivacy.public:
        return Colors.green;
      case GroupPrivacy.private:
        return Colors.orange;
      case GroupPrivacy.secret:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _getUserName(String userId) {
    switch (userId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }
}
