import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/comment.dart';
import '../models/user.dart';
import '../models/reaction_types.dart';
import '../services/comments_service.dart';

class CommentsProvider with ChangeNotifier {
  final CommentsService _commentsService = CommentsService();
  
  final Map<String, List<Comment>> _postComments = {};
  bool _isLoading = false;
  bool _isSubmitting = false;
  String? _errorMessage;
  
  // Getters
  List<Comment> getComments(String postId) => _postComments[postId] ?? [];
  bool get isLoading => _isLoading;
  bool get isSubmitting => _isSubmitting;
  String? get errorMessage => _errorMessage;
  
  // إحصائيات التعليقات
  int getCommentsCount(String postId) {
    final comments = getComments(postId);
    int total = comments.length;
    
    // إضافة عدد الردود
    for (final comment in comments) {
      total += comment.repliesCount;
    }
    
    return total;
  }
  
  // تحميل التعليقات
  Future<void> loadComments(String postId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      final comments = await _commentsService.getComments(postId);
      _postComments[postId] = comments;
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'خطأ في تحميل التعليقات: $e';
      notifyListeners();
    }
  }
  
  // إضافة تعليق جديد
  Future<Comment?> addComment({
    required String postId,
    required String authorId,
    required User author,
    required String content,
    CommentType type = CommentType.text,
    File? mediaFile,
    String? linkUrl,
    String? parentCommentId,
  }) async {
    try {
      _isSubmitting = true;
      _errorMessage = null;
      notifyListeners();
      
      String? mediaUrl;
      if (mediaFile != null) {
        mediaUrl = await _commentsService.uploadMedia(mediaFile);
      }
      
      final comment = await _commentsService.addComment(
        postId: postId,
        authorId: authorId,
        author: author,
        content: content,
        type: type,
        mediaUrl: mediaUrl,
        linkUrl: linkUrl,
        parentCommentId: parentCommentId,
      );
      
      // تحديث التعليقات محلياً
      await loadComments(postId);
      
      _isSubmitting = false;
      notifyListeners();
      
      return comment;
    } catch (e) {
      _isSubmitting = false;
      _errorMessage = 'خطأ في إضافة التعليق: $e';
      notifyListeners();
      return null;
    }
  }
  
  // تعديل تعليق
  Future<bool> editComment({
    required String postId,
    required String commentId,
    required String newContent,
    File? newMediaFile,
  }) async {
    try {
      _isSubmitting = true;
      _errorMessage = null;
      notifyListeners();
      
      String? newMediaUrl;
      if (newMediaFile != null) {
        newMediaUrl = await _commentsService.uploadMedia(newMediaFile);
      }
      
      await _commentsService.editComment(
        postId: postId,
        commentId: commentId,
        newContent: newContent,
        newMediaUrl: newMediaUrl,
      );
      
      // تحديث التعليقات محلياً
      await loadComments(postId);
      
      _isSubmitting = false;
      notifyListeners();
      
      return true;
    } catch (e) {
      _isSubmitting = false;
      _errorMessage = 'خطأ في تعديل التعليق: $e';
      notifyListeners();
      return false;
    }
  }
  
  // حذف تعليق
  Future<bool> deleteComment({
    required String postId,
    required String commentId,
    String? reason,
  }) async {
    try {
      _isSubmitting = true;
      _errorMessage = null;
      notifyListeners();
      
      await _commentsService.deleteComment(
        postId: postId,
        commentId: commentId,
        reason: reason,
      );
      
      // تحديث التعليقات محلياً
      await loadComments(postId);
      
      _isSubmitting = false;
      notifyListeners();
      
      return true;
    } catch (e) {
      _isSubmitting = false;
      _errorMessage = 'خطأ في حذف التعليق: $e';
      notifyListeners();
      return false;
    }
  }
  
  // إضافة تفاعل
  Future<void> addReaction({
    required String postId,
    required String commentId,
    required String userId,
    required String userName,
    required ReactionType reactionType,
  }) async {
    try {
      await _commentsService.addReaction(
        postId: postId,
        commentId: commentId,
        userId: userId,
        userName: userName,
        reactionType: reactionType,
      );
      
      // تحديث التعليقات محلياً
      await loadComments(postId);
    } catch (e) {
      _errorMessage = 'خطأ في إضافة التفاعل: $e';
      notifyListeners();
    }
  }
  
  // إزالة تفاعل
  Future<void> removeReaction({
    required String postId,
    required String commentId,
    required String userId,
  }) async {
    try {
      await _commentsService.removeReaction(
        postId: postId,
        commentId: commentId,
        userId: userId,
      );
      
      // تحديث التعليقات محلياً
      await loadComments(postId);
    } catch (e) {
      _errorMessage = 'خطأ في إزالة التفاعل: $e';
      notifyListeners();
    }
  }
  
  // تبديل التفاعل
  Future<void> toggleReaction({
    required String postId,
    required String commentId,
    required String userId,
    required String userName,
    required ReactionType reactionType,
  }) async {
    final comments = getComments(postId);
    Comment? targetComment;
    
    // البحث عن التعليق
    for (final comment in comments) {
      if (comment.id == commentId) {
        targetComment = comment;
        break;
      }
      
      // البحث في الردود
      for (final reply in comment.replies) {
        if (reply.id == commentId) {
          targetComment = reply;
          break;
        }
      }
      
      if (targetComment != null) break;
    }
    
    if (targetComment == null) return;
    
    final userReaction = targetComment.getUserReaction(userId);
    
    if (userReaction != null && userReaction.type == reactionType) {
      // إزالة التفاعل إذا كان نفس النوع
      await removeReaction(
        postId: postId,
        commentId: commentId,
        userId: userId,
      );
    } else {
      // إضافة أو تغيير التفاعل
      await addReaction(
        postId: postId,
        commentId: commentId,
        userId: userId,
        userName: userName,
        reactionType: reactionType,
      );
    }
  }
  
  // استخراج معلومات الرابط
  Future<Map<String, String?>> extractLinkInfo(String url) async {
    try {
      return await _commentsService.extractLinkInfo(url);
    } catch (e) {
      return {};
    }
  }
  
  // تحديث فوري للتعليقات
  Future<void> refreshComments(String postId) async {
    await loadComments(postId);
  }
  
  // مسح الأخطاء
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // مسح تعليقات منشور معين
  void clearPostComments(String postId) {
    _postComments.remove(postId);
    notifyListeners();
  }
  
  // مسح جميع التعليقات
  void clearAllComments() {
    _postComments.clear();
    notifyListeners();
  }
  
  @override
  void dispose() {
    _commentsService.dispose();
    super.dispose();
  }
}
