import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification.dart';
import '../models/user.dart';

class NotificationService {
  static const String _notificationsKey = 'notifications';
  static const String _settingsKey = 'notification_settings';
  
  // Stream للتحديث الفوري
  final StreamController<List<AppNotification>> _notificationsController = 
      StreamController<List<AppNotification>>.broadcast();
  
  Stream<List<AppNotification>> get notificationsStream => 
      _notificationsController.stream;
  
  // إعدادات الإشعارات
  Map<String, bool> _notificationSettings = {
    'likes': true,
    'comments': true,
    'follows': true,
    'mentions': true,
    'groups': true,
    'messages': true,
    'birthdays': true,
    'events': true,
  };
  
  // تحميل الإشعارات
  Future<List<AppNotification>> getNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];
      
      final notifications = notificationsJson
          .map((json) => AppNotification.fromJson(jsonDecode(json)))
          .toList();
      
      // ترتيب حسب التاريخ (الأحدث أولاً)
      notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      return notifications;
    } catch (e) {
      print('خطأ في تحميل الإشعارات: $e');
      return _generateSampleNotifications();
    }
  }
  
  // حفظ الإشعارات
  Future<void> _saveNotifications(List<AppNotification> notifications) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = notifications
          .map((notification) => jsonEncode(notification.toJson()))
          .toList();
      
      await prefs.setStringList(_notificationsKey, notificationsJson);
      
      // إرسال التحديث للمستمعين
      _notificationsController.add(notifications);
    } catch (e) {
      print('خطأ في حفظ الإشعارات: $e');
    }
  }
  
  // إضافة إشعار جديد
  Future<void> addNotification(AppNotification notification) async {
    final notifications = await getNotifications();
    notifications.insert(0, notification);
    
    // الاحتفاظ بآخر 100 إشعار فقط
    if (notifications.length > 100) {
      notifications.removeRange(100, notifications.length);
    }
    
    await _saveNotifications(notifications);
  }
  
  // تحديد إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    final notifications = await getNotifications();
    final index = notifications.indexWhere((n) => n.id == notificationId);
    
    if (index != -1) {
      notifications[index] = notifications[index].copyWith(
        status: NotificationStatus.read,
      );
      await _saveNotifications(notifications);
    }
  }
  
  // تحديد جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    final notifications = await getNotifications();
    final updatedNotifications = notifications.map((n) => n.copyWith(
      status: NotificationStatus.read,
    )).toList();
    
    await _saveNotifications(updatedNotifications);
  }
  
  // حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    final notifications = await getNotifications();
    notifications.removeWhere((n) => n.id == notificationId);
    await _saveNotifications(notifications);
  }
  
  // أرشفة إشعار
  Future<void> archiveNotification(String notificationId) async {
    final notifications = await getNotifications();
    final index = notifications.indexWhere((n) => n.id == notificationId);
    
    if (index != -1) {
      notifications[index] = notifications[index].copyWith(
        status: NotificationStatus.archived,
      );
      await _saveNotifications(notifications);
    }
  }
  
  // إنشاء إشعارات مختلفة
  Future<void> createLikeNotification({
    required String postId,
    required String fromUserId,
    required String toUserId,
  }) async {
    if (!_notificationSettings['likes']!) return;
    
    final notification = AppNotification(
      id: 'like_${DateTime.now().millisecondsSinceEpoch}',
      userId: toUserId,
      fromUserId: fromUserId,
      type: NotificationType.like,
      title: 'إعجاب جديد',
      message: 'أعجب بمنشورك',
      postId: postId,
      timestamp: DateTime.now(),
      actionUrl: '/post/$postId',
    );
    
    await addNotification(notification);
  }
  
  Future<void> createCommentNotification({
    required String postId,
    required String commentId,
    required String fromUserId,
    required String toUserId,
    required String commentText,
  }) async {
    if (!_notificationSettings['comments']!) return;
    
    final notification = AppNotification(
      id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
      userId: toUserId,
      fromUserId: fromUserId,
      type: NotificationType.comment,
      title: 'تعليق جديد',
      message: 'علق على منشورك: "$commentText"',
      postId: postId,
      commentId: commentId,
      timestamp: DateTime.now(),
      actionUrl: '/post/$postId/comment/$commentId',
    );
    
    await addNotification(notification);
  }
  
  Future<void> createFollowNotification({
    required String fromUserId,
    required String toUserId,
  }) async {
    if (!_notificationSettings['follows']!) return;
    
    final notification = AppNotification(
      id: 'follow_${DateTime.now().millisecondsSinceEpoch}',
      userId: toUserId,
      fromUserId: fromUserId,
      type: NotificationType.follow,
      title: 'متابع جديد',
      message: 'بدأ بمتابعتك',
      timestamp: DateTime.now(),
      actionUrl: '/profile/$fromUserId',
    );
    
    await addNotification(notification);
  }
  
  Future<void> createGroupInviteNotification({
    required String groupId,
    required String fromUserId,
    required String toUserId,
    required String groupName,
  }) async {
    if (!_notificationSettings['groups']!) return;
    
    final notification = AppNotification(
      id: 'group_invite_${DateTime.now().millisecondsSinceEpoch}',
      userId: toUserId,
      fromUserId: fromUserId,
      type: NotificationType.groupInvite,
      title: 'دعوة لمجموعة',
      message: 'دعاك للانضمام إلى مجموعة "$groupName"',
      groupId: groupId,
      timestamp: DateTime.now(),
      actionUrl: '/group/$groupId',
    );
    
    await addNotification(notification);
  }
  
  Future<void> createMentionNotification({
    required String postId,
    String? commentId,
    required String fromUserId,
    required String toUserId,
    required String content,
  }) async {
    if (!_notificationSettings['mentions']!) return;
    
    final notification = AppNotification(
      id: 'mention_${DateTime.now().millisecondsSinceEpoch}',
      userId: toUserId,
      fromUserId: fromUserId,
      type: NotificationType.mention,
      title: 'تم ذكرك',
      message: 'ذكرك في ${commentId != null ? 'تعليق' : 'منشور'}',
      postId: postId,
      commentId: commentId,
      timestamp: DateTime.now(),
      actionUrl: commentId != null 
          ? '/post/$postId/comment/$commentId'
          : '/post/$postId',
    );
    
    await addNotification(notification);
  }
  
  // إعدادات الإشعارات
  Future<Map<String, bool>> getNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settings = jsonDecode(settingsJson) as Map<String, dynamic>;
        return settings.cast<String, bool>();
      }
    } catch (e) {
      print('خطأ في تحميل إعدادات الإشعارات: $e');
    }
    
    return _notificationSettings;
  }
  
  Future<void> updateNotificationSettings(Map<String, bool> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_settingsKey, jsonEncode(settings));
      _notificationSettings = settings;
    } catch (e) {
      print('خطأ في حفظ إعدادات الإشعارات: $e');
    }
  }
  
  // إنشاء إشعارات تجريبية
  List<AppNotification> _generateSampleNotifications() {
    final now = DateTime.now();
    
    return [
      AppNotification(
        id: '1',
        userId: 'current_user',
        fromUserId: 'user_1',
        type: NotificationType.like,
        title: 'إعجاب جديد',
        message: 'أحمد أعجب بمنشورك',
        postId: 'post_1',
        timestamp: now.subtract(const Duration(minutes: 5)),
        actionUrl: '/post/post_1',
      ),
      AppNotification(
        id: '2',
        userId: 'current_user',
        fromUserId: 'user_2',
        type: NotificationType.comment,
        title: 'تعليق جديد',
        message: 'فاطمة علقت على منشورك: "رائع!"',
        postId: 'post_1',
        commentId: 'comment_1',
        timestamp: now.subtract(const Duration(hours: 1)),
        actionUrl: '/post/post_1/comment/comment_1',
      ),
      AppNotification(
        id: '3',
        userId: 'current_user',
        fromUserId: 'user_3',
        type: NotificationType.follow,
        title: 'متابع جديد',
        message: 'سعيد بدأ بمتابعتك',
        timestamp: now.subtract(const Duration(hours: 2)),
        actionUrl: '/profile/user_3',
      ),
      AppNotification(
        id: '4',
        userId: 'current_user',
        fromUserId: 'user_4',
        type: NotificationType.groupInvite,
        title: 'دعوة لمجموعة',
        message: 'أسماء دعتك للانضمام إلى مجموعة "مطورو Flutter"',
        groupId: 'group_1',
        timestamp: now.subtract(const Duration(days: 1)),
        actionUrl: '/group/group_1',
      ),
      AppNotification(
        id: '5',
        userId: 'current_user',
        fromUserId: 'user_5',
        type: NotificationType.mention,
        title: 'تم ذكرك',
        message: 'محمد ذكرك في تعليق',
        postId: 'post_2',
        commentId: 'comment_2',
        timestamp: now.subtract(const Duration(days: 2)),
        actionUrl: '/post/post_2/comment/comment_2',
      ),
    ];
  }
  
  void dispose() {
    _notificationsController.close();
  }
}
