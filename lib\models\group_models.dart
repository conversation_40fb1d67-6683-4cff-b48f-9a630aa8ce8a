import 'user.dart';

enum GroupType {
  public,    // عامة - يمكن لأي شخص العثور عليها والانضمام
  private,   // خاصة - لا يمكن الانضمام إلا بدعوة أو موافقة
  hidden,    // مخفية - لا تظهر في البحث، فقط من يملك الرابط
}

// للتوافق مع الكود الموجود
enum GroupPrivacy {
  public,
  private,
  secret, // نفس hidden
}

enum GroupMemberRole {
  member,     // عضو عادي
  moderator,  // مشرف
  admin,      // مدير
  owner,      // مالك المجموعة
}

enum GroupPostApproval {
  none,       // لا توجد موافقة مسبقة
  moderator,  // موافقة المشرف
  admin,      // موافقة المدير
}

enum GroupPostPermission {
  everyone,   // الجميع يمكنه النشر
  moderators, // المشرفين فقط
  admins,     // المديرين فقط
}

enum GroupJoinRequestStatus {
  pending,    // في انتظار الموافقة
  approved,   // تمت الموافقة
  rejected,   // مرفوض
}

enum GroupActivityType {
  joined,           // انضم للمجموعة
  left,             // غادر المجموعة
  posted,           // نشر منشور
  commented,        // علق على منشور
  reacted,          // تفاعل مع منشور
  roleChanged,      // تغيير الدور
  banned,           // تم حظره
  unbanned,         // تم إلغاء حظره
  postApproved,     // تمت موافقة على منشور
  postRejected,     // تم رفض منشور
}

class GroupRule {
  final String id;
  final String title;
  final String description;
  final int order;
  final DateTime createdAt;
  final String createdBy;

  const GroupRule({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    required this.createdAt,
    required this.createdBy,
  });

  factory GroupRule.fromJson(Map<String, dynamic> json) {
    return GroupRule(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      order: json['order'],
      createdAt: DateTime.parse(json['createdAt']),
      createdBy: json['createdBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'order': order,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
    };
  }
}

class GroupJoinQuestion {
  final String id;
  final String question;
  final bool isRequired;
  final int order;

  const GroupJoinQuestion({
    required this.id,
    required this.question,
    required this.isRequired,
    required this.order,
  });

  factory GroupJoinQuestion.fromJson(Map<String, dynamic> json) {
    return GroupJoinQuestion(
      id: json['id'],
      question: json['question'],
      isRequired: json['isRequired'],
      order: json['order'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'isRequired': isRequired,
      'order': order,
    };
  }
}

class GroupJoinRequest {
  final String id;
  final String groupId;
  final String userId;
  final User user;
  final GroupJoinRequestStatus status;
  final DateTime requestDate;
  final DateTime? responseDate;
  final String? respondedBy;
  final String? rejectionReason;
  final Map<String, String> answers; // إجابات أسئلة الانضمام

  const GroupJoinRequest({
    required this.id,
    required this.groupId,
    required this.userId,
    required this.user,
    required this.status,
    required this.requestDate,
    this.responseDate,
    this.respondedBy,
    this.rejectionReason,
    this.answers = const {},
  });

  factory GroupJoinRequest.fromJson(Map<String, dynamic> json) {
    return GroupJoinRequest(
      id: json['id'],
      groupId: json['groupId'],
      userId: json['userId'],
      user: User.fromJson(json['user']),
      status: GroupJoinRequestStatus.values.firstWhere(
        (e) => e.name == json['status'],
      ),
      requestDate: DateTime.parse(json['requestDate']),
      responseDate: json['responseDate'] != null 
          ? DateTime.parse(json['responseDate']) 
          : null,
      respondedBy: json['respondedBy'],
      rejectionReason: json['rejectionReason'],
      answers: Map<String, String>.from(json['answers'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'userId': userId,
      'user': user.toJson(),
      'status': status.name,
      'requestDate': requestDate.toIso8601String(),
      'responseDate': responseDate?.toIso8601String(),
      'respondedBy': respondedBy,
      'rejectionReason': rejectionReason,
      'answers': answers,
    };
  }
}

class GroupMember {
  final String id;
  final String groupId;
  final String userId;
  final User user;
  final GroupMemberRole role;
  final DateTime joinDate;
  final DateTime? lastActivity;
  final bool isBanned;
  final DateTime? bannedUntil;
  final String? bannedBy;
  final String? banReason;
  final int postsCount;
  final int commentsCount;

  const GroupMember({
    required this.id,
    required this.groupId,
    required this.userId,
    required this.user,
    required this.role,
    required this.joinDate,
    this.lastActivity,
    this.isBanned = false,
    this.bannedUntil,
    this.bannedBy,
    this.banReason,
    this.postsCount = 0,
    this.commentsCount = 0,
  });

  factory GroupMember.fromJson(Map<String, dynamic> json) {
    return GroupMember(
      id: json['id'],
      groupId: json['groupId'],
      userId: json['userId'],
      user: User.fromJson(json['user']),
      role: GroupMemberRole.values.firstWhere(
        (e) => e.name == json['role'],
      ),
      joinDate: DateTime.parse(json['joinDate']),
      lastActivity: json['lastActivity'] != null 
          ? DateTime.parse(json['lastActivity']) 
          : null,
      isBanned: json['isBanned'] ?? false,
      bannedUntil: json['bannedUntil'] != null 
          ? DateTime.parse(json['bannedUntil']) 
          : null,
      bannedBy: json['bannedBy'],
      banReason: json['banReason'],
      postsCount: json['postsCount'] ?? 0,
      commentsCount: json['commentsCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'userId': userId,
      'user': user.toJson(),
      'role': role.name,
      'joinDate': joinDate.toIso8601String(),
      'lastActivity': lastActivity?.toIso8601String(),
      'isBanned': isBanned,
      'bannedUntil': bannedUntil?.toIso8601String(),
      'bannedBy': bannedBy,
      'banReason': banReason,
      'postsCount': postsCount,
      'commentsCount': commentsCount,
    };
  }

  bool get isAdmin => role == GroupMemberRole.admin || role == GroupMemberRole.owner;
  bool get isModerator => role == GroupMemberRole.moderator || isAdmin;
  bool get canManageMembers => isAdmin;
  bool get canManagePosts => isModerator;
  bool get canManageSettings => isAdmin;

  String get roleText {
    switch (role) {
      case GroupMemberRole.member:
        return 'عضو';
      case GroupMemberRole.moderator:
        return 'مشرف';
      case GroupMemberRole.admin:
        return 'مدير';
      case GroupMemberRole.owner:
        return 'مالك المجموعة';
    }
  }
}

class GroupActivity {
  final String id;
  final String groupId;
  final String userId;
  final User user;
  final GroupActivityType type;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const GroupActivity({
    required this.id,
    required this.groupId,
    required this.userId,
    required this.user,
    required this.type,
    required this.timestamp,
    this.metadata = const {},
  });

  factory GroupActivity.fromJson(Map<String, dynamic> json) {
    return GroupActivity(
      id: json['id'],
      groupId: json['groupId'],
      userId: json['userId'],
      user: User.fromJson(json['user']),
      type: GroupActivityType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'userId': userId,
      'user': user.toJson(),
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  String get description {
    switch (type) {
      case GroupActivityType.joined:
        return 'انضم للمجموعة';
      case GroupActivityType.left:
        return 'غادر المجموعة';
      case GroupActivityType.posted:
        return 'نشر منشوراً';
      case GroupActivityType.commented:
        return 'علق على منشور';
      case GroupActivityType.reacted:
        return 'تفاعل مع منشور';
      case GroupActivityType.roleChanged:
        return 'تم تغيير دوره';
      case GroupActivityType.banned:
        return 'تم حظره';
      case GroupActivityType.unbanned:
        return 'تم إلغاء حظره';
      case GroupActivityType.postApproved:
        return 'تمت الموافقة على منشوره';
      case GroupActivityType.postRejected:
        return 'تم رفض منشوره';
    }
  }
}
