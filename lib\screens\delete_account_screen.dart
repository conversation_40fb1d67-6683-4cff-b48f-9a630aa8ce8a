import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../widgets/facebook_form_field.dart';
import '../screens/facebook_login_screen.dart';

class DeleteAccountScreen extends StatefulWidget {
  const DeleteAccountScreen({super.key});

  @override
  State<DeleteAccountScreen> createState() => _DeleteAccountScreenState();
}

class _DeleteAccountScreenState extends State<DeleteAccountScreen> {
  final AuthService _authService = AuthService();
  final _passwordController = TextEditingController();
  final _confirmController = TextEditingController();
  
  String? _selectedReason;
  bool _isProcessing = false;
  String? _errorMessage;
  bool _hasReadWarning = false;

  final List<String> _reasons = [
    'لا أستخدم التطبيق بعد الآن',
    'أريد إنشاء حساب جديد',
    'مشاكل في الخصوصية والأمان',
    'تجربة سيئة مع التطبيق',
    'أفضل تطبيقات أخرى',
    'سبب شخصي',
  ];

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'حذف الحساب',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // تحذير شديد
            _buildSevereWarningSection(),
            
            const SizedBox(height: 30),
            
            // ما سيحدث عند الحذف
            _buildWhatHappensSection(),
            
            const SizedBox(height: 30),
            
            // سبب الحذف
            _buildReasonSection(),
            
            const SizedBox(height: 30),
            
            // تأكيد كلمة المرور
            _buildPasswordSection(),
            
            const SizedBox(height: 30),
            
            // تأكيد الحذف
            _buildConfirmationSection(),
            
            const SizedBox(height: 20),
            
            // رسالة الخطأ
            if (_errorMessage != null) _buildErrorMessage(),
            
            const SizedBox(height: 30),
            
            // أزرار التحكم
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSevereWarningSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[300]!, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.warning, color: Colors.red[600], size: 24),
              const SizedBox(width: 8),
              Text(
                'تحذير: حذف نهائي',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'هذا الإجراء لا يمكن التراجع عنه!\n'
            'سيتم جدولة حسابك للحذف النهائي بعد 30 يوماً.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.red[700],
              fontWeight: FontWeight.w600,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWhatHappensSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ما سيحدث عند حذف حسابك:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            '• سيتم حذف جميع منشوراتك وصورك\n'
            '• ستفقد جميع رسائلك ومحادثاتك\n'
            '• سيتم حذف قائمة أصدقائك ومتابعيك\n'
            '• لن تتمكن من استرداد أي بيانات\n'
            '• سيتم إلغاء جميع اشتراكاتك\n'
            '• لديك 30 يوماً لإلغاء طلب الحذف',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReasonSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'سبب حذف الحساب (اختياري)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedReason,
              hint: const Text('اختر السبب (اختياري)'),
              isExpanded: true,
              items: _reasons.map((reason) {
                return DropdownMenuItem<String>(
                  value: reason,
                  child: Text(reason),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedReason = value;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تأكيد كلمة المرور',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        FacebookFormField(
          controller: _passwordController,
          label: 'كلمة المرور',
          isPassword: true,
          prefixIcon: Icons.lock_outline,
          onChanged: (value) {
            setState(() {
              _errorMessage = null;
            });
          },
        ),
      ],
    );
  }

  Widget _buildConfirmationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تأكيد الحذف',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        FacebookFormField(
          controller: _confirmController,
          label: 'اكتب "حذف حسابي" للتأكيد',
          prefixIcon: Icons.warning_outlined,
          onChanged: (value) {
            setState(() {
              _errorMessage = null;
            });
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Checkbox(
              value: _hasReadWarning,
              onChanged: (value) {
                setState(() {
                  _hasReadWarning = value ?? false;
                });
              },
              activeColor: Colors.red,
            ),
            const Expanded(
              child: Text(
                'أفهم أن هذا الإجراء نهائي ولا يمكن التراجع عنه',
                style: TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر الحذف النهائي
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _canDelete() ? _deleteAccount : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              disabledBackgroundColor: Colors.grey[300],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isProcessing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'حذف حسابي نهائياً',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // زر الإلغاء
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Colors.grey[400]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool _canDelete() {
    return _passwordController.text.isNotEmpty &&
           _confirmController.text.trim() == 'حذف حسابي' &&
           _hasReadWarning &&
           !_isProcessing;
  }

  Future<void> _deleteAccount() async {
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      // التحقق من كلمة المرور (محاكاة)
      await Future.delayed(const Duration(seconds: 2));
      
      // في التطبيق الحقيقي، هنا سيتم إرسال طلب حذف الحساب للخادم
      
      // حذف الحساب
      await _authService.logout();
      
      if (mounted) {
        // إظهار رسالة تأكيد الحذف
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('تم جدولة حسابك للحذف'),
            content: const Text(
              'تم جدولة حسابك للحذف النهائي بعد 30 يوماً من اليوم.\n\n'
              'يمكنك إلغاء طلب الحذف بتسجيل الدخول خلال هذه الفترة.\n\n'
              'بعد انتهاء الـ 30 يوماً، سيتم حذف حسابك وجميع بياناتك نهائياً.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => const FacebookLoginScreen()),
                    (route) => false,
                  );
                },
                child: const Text('فهمت'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'كلمة المرور غير صحيحة';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }
}
