import 'user.dart';
import 'reaction_types.dart';

enum LiveStreamStatus {
  preparing,
  live,
  ended,
  paused,
  error,
}

enum LiveStreamQuality {
  low,     // 480p
  medium,  // 720p
  high,    // 1080p
  auto,    // تكيف تلقائي
}

enum LiveStreamPrivacy {
  public,    // عام
  friends,   // الأصدقاء فقط
  custom,    // مخصص
}

class LiveStreamViewer {
  final String userId;
  final String userName;
  final String? userAvatar;
  final DateTime joinedAt;
  final bool isActive;

  const LiveStreamViewer({
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.joinedAt,
    this.isActive = true,
  });

  factory LiveStreamViewer.fromJson(Map<String, dynamic> json) {
    return LiveStreamViewer(
      userId: json['userId'],
      userName: json['userName'],
      userAvatar: json['userAvatar'],
      joinedAt: DateTime.parse(json['joinedAt']),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'joinedAt': joinedAt.toIso8601String(),
      'isActive': isActive,
    };
  }
}

class LiveStreamReaction {
  final String id;
  final String userId;
  final String userName;
  final ReactionType type;
  final DateTime timestamp;

  const LiveStreamReaction({
    required this.id,
    required this.userId,
    required this.userName,
    required this.type,
    required this.timestamp,
  });

  factory LiveStreamReaction.fromJson(Map<String, dynamic> json) {
    return LiveStreamReaction(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      type: ReactionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  String get emoji {
    switch (type) {
      case ReactionType.like:
        return '👍';
      case ReactionType.love:
        return '❤️';
      case ReactionType.haha:
        return '😂';
      case ReactionType.wow:
        return '😮';
      case ReactionType.sad:
        return '😢';
      case ReactionType.angry:
        return '😡';
    }
  }
}

class LiveStreamComment {
  final String id;
  final String userId;
  final String userName;
  final String? userAvatar;
  final String content;
  final DateTime timestamp;
  final List<LiveStreamReaction> reactions;

  const LiveStreamComment({
    required this.id,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.content,
    required this.timestamp,
    this.reactions = const [],
  });

  factory LiveStreamComment.fromJson(Map<String, dynamic> json) {
    return LiveStreamComment(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      userAvatar: json['userAvatar'],
      content: json['content'],
      timestamp: DateTime.parse(json['timestamp']),
      reactions: (json['reactions'] as List<dynamic>?)
          ?.map((r) => LiveStreamReaction.fromJson(r))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'reactions': reactions.map((r) => r.toJson()).toList(),
    };
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inSeconds < 60) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} د';
    } else {
      return 'منذ ${difference.inHours} س';
    }
  }
}

class LiveStream {
  final String id;
  final String streamerId;
  final User streamer;
  final String title;
  final String? description;
  final LiveStreamStatus status;
  final LiveStreamQuality quality;
  final LiveStreamPrivacy privacy;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? duration;
  final List<LiveStreamViewer> viewers;
  final List<LiveStreamComment> comments;
  final List<LiveStreamReaction> reactions;
  final Map<ReactionType, int> reactionCounts;
  final int viewerCount;
  final int totalViewers;
  final int peakViewers;
  final String? streamUrl;
  final String? thumbnailUrl;
  final bool isRecording;
  final bool isCameraEnabled;
  final bool isMicEnabled;
  final bool isFrontCamera;

  const LiveStream({
    required this.id,
    required this.streamerId,
    required this.streamer,
    required this.title,
    this.description,
    required this.status,
    this.quality = LiveStreamQuality.auto,
    this.privacy = LiveStreamPrivacy.public,
    required this.startTime,
    this.endTime,
    this.duration,
    this.viewers = const [],
    this.comments = const [],
    this.reactions = const [],
    this.reactionCounts = const {},
    this.viewerCount = 0,
    this.totalViewers = 0,
    this.peakViewers = 0,
    this.streamUrl,
    this.thumbnailUrl,
    this.isRecording = false,
    this.isCameraEnabled = true,
    this.isMicEnabled = true,
    this.isFrontCamera = true,
  });

  factory LiveStream.fromJson(Map<String, dynamic> json) {
    return LiveStream(
      id: json['id'],
      streamerId: json['streamerId'],
      streamer: User.fromJson(json['streamer']),
      title: json['title'],
      description: json['description'],
      status: LiveStreamStatus.values.firstWhere(
        (e) => e.name == json['status'],
      ),
      quality: LiveStreamQuality.values.firstWhere(
        (e) => e.name == json['quality'],
        orElse: () => LiveStreamQuality.auto,
      ),
      privacy: LiveStreamPrivacy.values.firstWhere(
        (e) => e.name == json['privacy'],
        orElse: () => LiveStreamPrivacy.public,
      ),
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      duration: json['duration'] != null 
          ? Duration(seconds: json['duration']) 
          : null,
      viewers: (json['viewers'] as List<dynamic>?)
          ?.map((v) => LiveStreamViewer.fromJson(v))
          .toList() ?? [],
      comments: (json['comments'] as List<dynamic>?)
          ?.map((c) => LiveStreamComment.fromJson(c))
          .toList() ?? [],
      reactions: (json['reactions'] as List<dynamic>?)
          ?.map((r) => LiveStreamReaction.fromJson(r))
          .toList() ?? [],
      reactionCounts: Map<ReactionType, int>.from(
        (json['reactionCounts'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(
            ReactionType.values.firstWhere((e) => e.name == key),
            value as int,
          ),
        ) ?? {},
      ),
      viewerCount: json['viewerCount'] ?? 0,
      totalViewers: json['totalViewers'] ?? 0,
      peakViewers: json['peakViewers'] ?? 0,
      streamUrl: json['streamUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      isRecording: json['isRecording'] ?? false,
      isCameraEnabled: json['isCameraEnabled'] ?? true,
      isMicEnabled: json['isMicEnabled'] ?? true,
      isFrontCamera: json['isFrontCamera'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'streamerId': streamerId,
      'streamer': streamer.toJson(),
      'title': title,
      'description': description,
      'status': status.name,
      'quality': quality.name,
      'privacy': privacy.name,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'duration': duration?.inSeconds,
      'viewers': viewers.map((v) => v.toJson()).toList(),
      'comments': comments.map((c) => c.toJson()).toList(),
      'reactions': reactions.map((r) => r.toJson()).toList(),
      'reactionCounts': reactionCounts.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'viewerCount': viewerCount,
      'totalViewers': totalViewers,
      'peakViewers': peakViewers,
      'streamUrl': streamUrl,
      'thumbnailUrl': thumbnailUrl,
      'isRecording': isRecording,
      'isCameraEnabled': isCameraEnabled,
      'isMicEnabled': isMicEnabled,
      'isFrontCamera': isFrontCamera,
    };
  }

  LiveStream copyWith({
    String? id,
    String? streamerId,
    User? streamer,
    String? title,
    String? description,
    LiveStreamStatus? status,
    LiveStreamQuality? quality,
    LiveStreamPrivacy? privacy,
    DateTime? startTime,
    DateTime? endTime,
    Duration? duration,
    List<LiveStreamViewer>? viewers,
    List<LiveStreamComment>? comments,
    List<LiveStreamReaction>? reactions,
    Map<ReactionType, int>? reactionCounts,
    int? viewerCount,
    int? totalViewers,
    int? peakViewers,
    String? streamUrl,
    String? thumbnailUrl,
    bool? isRecording,
    bool? isCameraEnabled,
    bool? isMicEnabled,
    bool? isFrontCamera,
  }) {
    return LiveStream(
      id: id ?? this.id,
      streamerId: streamerId ?? this.streamerId,
      streamer: streamer ?? this.streamer,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      quality: quality ?? this.quality,
      privacy: privacy ?? this.privacy,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      viewers: viewers ?? this.viewers,
      comments: comments ?? this.comments,
      reactions: reactions ?? this.reactions,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      viewerCount: viewerCount ?? this.viewerCount,
      totalViewers: totalViewers ?? this.totalViewers,
      peakViewers: peakViewers ?? this.peakViewers,
      streamUrl: streamUrl ?? this.streamUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      isRecording: isRecording ?? this.isRecording,
      isCameraEnabled: isCameraEnabled ?? this.isCameraEnabled,
      isMicEnabled: isMicEnabled ?? this.isMicEnabled,
      isFrontCamera: isFrontCamera ?? this.isFrontCamera,
    );
  }

  // دوال مساعدة
  bool get isLive => status == LiveStreamStatus.live;
  bool get isEnded => status == LiveStreamStatus.ended;
  bool get isPreparing => status == LiveStreamStatus.preparing;
  bool get hasError => status == LiveStreamStatus.error;

  String get statusText {
    switch (status) {
      case LiveStreamStatus.preparing:
        return 'جاري التحضير...';
      case LiveStreamStatus.live:
        return 'مباشر';
      case LiveStreamStatus.ended:
        return 'انتهى';
      case LiveStreamStatus.paused:
        return 'متوقف مؤقتاً';
      case LiveStreamStatus.error:
        return 'خطأ';
    }
  }

  String get durationText {
    if (duration == null) return '00:00';
    
    final hours = duration!.inHours;
    final minutes = duration!.inMinutes % 60;
    final seconds = duration!.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }

  String get qualityText {
    switch (quality) {
      case LiveStreamQuality.low:
        return '480p';
      case LiveStreamQuality.medium:
        return '720p';
      case LiveStreamQuality.high:
        return '1080p';
      case LiveStreamQuality.auto:
        return 'تلقائي';
    }
  }

  String get privacyText {
    switch (privacy) {
      case LiveStreamPrivacy.public:
        return 'عام';
      case LiveStreamPrivacy.friends:
        return 'الأصدقاء';
      case LiveStreamPrivacy.custom:
        return 'مخصص';
    }
  }
}
