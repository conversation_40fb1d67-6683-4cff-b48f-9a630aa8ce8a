import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'dart:io';
import '../providers/social_provider.dart';
import '../models/post.dart';
import '../models/link_preview.dart';
import '../models/reaction_types.dart';
import '../screens/facebook_profile_screen.dart';
import '../widgets/smart_avatar.dart';
import '../services/mock_data_service.dart';
import '../theme/app_theme.dart';
import 'comments_dialog.dart';
import 'real_video_player.dart';

import 'image_viewer.dart';
import 'link_preview_widget.dart';
import 'reaction_picker.dart';
import 'comments_section.dart';

import 'post_share_helpers.dart';

class PostCard extends StatelessWidget {
  final Post post;

  const PostCard({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المنشور
          _PostHeader(post: post),

          // محتوى المنشور
          _PostContent(post: post),

          // تم حذف الإحصائيات القديمة - الآن نعرض الأرقام بجانب الأيقونات مباشرة

          // أزرار التفاعل
          _PostActions(post: post),
        ],
      ),
    );
  }
}

class _PostHeader extends StatelessWidget {
  final Post post;

  const _PostHeader({required this.post});

  // الحصول على اسم المؤلف
  String _getAuthorName(String authorId) {
    switch (authorId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  // الحصول على جنس المؤلف
  String? _getUserGender(String authorId) {
    switch (authorId) {
      case '1': return 'male'; // أحمد محمد
      case '2': return 'female'; // فاطمة علي
      case '3': return 'male'; // محمد حسن
      case '4': return 'female'; // عائشة أحمد
      case '5': return 'male'; // عمر خالد
      case 'current_user': return 'male'; // يمكن تخصيصه
      default: return null; // سيتم التخمين من الاسم
    }
  }

  // استخراج معرف صاحب المنشور الأصلي من المحتوى
  String? _extractOriginalAuthorId() {
    final lines = post.content.split('\n');
    for (final line in lines) {
      if (line.contains('--- إعادة نشر ---')) {
        final authorIds = ['1', '2', '3', '4', '5'];
        return authorIds[post.id.hashCode % authorIds.length];
      }
    }
    return null;
  }

  // التنقل إلى الملف الشخصي
  void _navigateToProfile(BuildContext context, String userId) {
    final authorName = _getAuthorName(userId);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FacebookProfileScreen(
          userId: userId,
          userName: authorName,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // كشف إعادة النشر
    final isRepost = post.content.contains('--- إعادة نشر ---');
    final originalAuthorId = isRepost ? _extractOriginalAuthorId() : null;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض معلومات المستخدم الحالي (الذي أعاد النشر أو كتب المنشور)
          Row(
            children: [
              // صورة المستخدم الحالي
              GestureDetector(
                onTap: () => _navigateToProfile(context, post.authorId),
                child: SmartAvatarWithText(
                  name: _getAuthorName(post.authorId),
                  gender: _getUserGender(post.authorId),
                  radius: 20,
                ),
              ),

              const SizedBox(width: 12),

              // معلومات المستخدم الحالي
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () => _navigateToProfile(context, post.authorId),
                          child: Text(
                            _getAuthorName(post.authorId),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        // أيقونة إعادة النشر إذا كان منشور معاد
                        if (isRepost) ...[
                          const SizedBox(width: 4),
                          const Icon(Icons.repeat, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          const Text(
                            'أعاد نشر',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),
                    // المشاعر والأنشطة والموقع
                    if (post.feelingDisplay != null || post.activityDisplay != null || post.locationDisplay != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (post.feelingDisplay != null) ...[
                            Text(
                              post.feelingDisplay!,
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                          ],
                          if (post.activityDisplay != null) ...[
                            if (post.feelingDisplay != null) const Text(' • ', style: TextStyle(color: Colors.grey)),
                            Text(
                              post.activityDisplay!,
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                          ],
                          if (post.locationDisplay != null) ...[
                            if (post.feelingDisplay != null || post.activityDisplay != null) const Text(' • ', style: TextStyle(color: Colors.grey)),
                            Text(
                              post.locationDisplay!,
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                    Text(
                      timeago.format(post.timestamp, locale: 'ar'),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),

              // قائمة الثلاث نقاط
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_horiz, color: Colors.grey),
                onSelected: (value) => _handleMenuAction(context, value, post),
                itemBuilder: (context) => _buildMenuItems(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(BuildContext context, String action, Post post) {
    switch (action) {
      case 'delete':
        _deletePost(context, post);
        break;
      case 'save':
        _savePost(context, post);
        break;
      case 'hide':
        _hidePost(context, post);
        break;
      case 'report':
        _reportPost(context, post);
        break;
      case 'copy_link':
        _copyPostLink(context, post);
        break;
      case 'block_user':
        _blockUser(context, post);
        break;
    }
  }

  void _deletePost(BuildContext context, Post post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنشور'),
        content: const Text('هل تريد حذف هذا المنشور نهائياً؟ لن تتمكن من استرداده بعد الحذف.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              final socialProvider = Provider.of<SocialProvider>(context, listen: false);
              socialProvider.deletePost(post.id);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.delete, color: Colors.white, size: 20),
                      const SizedBox(width: 8),
                      const Text('تم حذف المنشور نهائياً! 🗑️'),
                    ],
                  ),
                  duration: const Duration(seconds: 2),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _savePost(BuildContext context, Post post) {
    // حفظ المنشور
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    socialProvider.savePost(post.id);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.bookmark, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('تم حفظ المنشور! 📌'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _hidePost(BuildContext context, Post post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إخفاء المنشور'),
        content: const Text('هل تريد إخفاء هذا المنشور؟ لن تراه مرة أخرى وسنعرض عليك منشورات أقل مشابهة له.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              final socialProvider = Provider.of<SocialProvider>(context, listen: false);
              socialProvider.hidePost(post.id);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.visibility_off, color: Colors.white, size: 20),
                      const SizedBox(width: 8),
                      const Text('تم إخفاء المنشور 👁️'),
                    ],
                  ),
                  duration: const Duration(seconds: 2),
                  backgroundColor: Colors.orange,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('إخفاء', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _reportPost(BuildContext context, Post post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن المنشور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('لماذا تريد الإبلاغ عن هذا المنشور؟'),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'سبب الإبلاغ',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'spam', child: Text('محتوى مزعج')),
                DropdownMenuItem(value: 'inappropriate', child: Text('محتوى غير لائق')),
                DropdownMenuItem(value: 'harassment', child: Text('تحرش أو تنمر')),
                DropdownMenuItem(value: 'fake_news', child: Text('أخبار كاذبة')),
                DropdownMenuItem(value: 'violence', child: Text('عنف أو تهديد')),
                DropdownMenuItem(value: 'other', child: Text('أخرى')),
              ],
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              final socialProvider = Provider.of<SocialProvider>(context, listen: false);
              socialProvider.reportPost(post.id, 'spam', 'تم الإبلاغ عن المنشور');

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.report, color: Colors.white, size: 20),
                      const SizedBox(width: 8),
                      const Text('تم الإبلاغ عن المنشور! 🚨'),
                    ],
                  ),
                  duration: const Duration(seconds: 2),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إبلاغ', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _copyPostLink(BuildContext context, Post post) async {
    try {
      final postLink = 'https://arzawo.app/post/${post.id}';
      await Clipboard.setData(ClipboardData(text: postLink));

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('تم نسخ رابط المنشور! 🔗'),
                      Text(
                        postLink,
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في نسخ الرابط: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _blockUser(BuildContext context, Post post) {
    final authorName = _getAuthorName(post.authorId);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حظر $authorName'),
        content: Text('هل تريد حظر $authorName؟ لن تتمكن من رؤية منشوراته أو ملفه الشخصي.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              final socialProvider = Provider.of<SocialProvider>(context, listen: false);
              socialProvider.blockUser(post.authorId);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.block, color: Colors.white, size: 20),
                      const SizedBox(width: 8),
                      Text('تم حظر $authorName! 🚫'),
                    ],
                  ),
                  duration: const Duration(seconds: 2),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حظر', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }



  // بناء عناصر القائمة
  List<PopupMenuEntry<String>> _buildMenuItems() {
    List<PopupMenuEntry<String>> items = [];

    // إذا كان المنشور للمستخدم الحالي، أضف خيار الحذف
    if (post.authorId == 'current_user') {
      items.add(
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 12),
              Text('حذف المنشور', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      );
      items.add(const PopupMenuDivider());
    }

    // باقي الخيارات للجميع
    items.addAll([
      const PopupMenuItem(
        value: 'save',
        child: Row(
          children: [
            Icon(Icons.bookmark_border, color: Colors.blue),
            SizedBox(width: 12),
            Text('حفظ المنشور'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'hide',
        child: Row(
          children: [
            Icon(Icons.visibility_off, color: Colors.orange),
            SizedBox(width: 12),
            Text('إخفاء المنشور'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'report',
        child: Row(
          children: [
            Icon(Icons.report, color: Colors.red),
            SizedBox(width: 12),
            Text('الإبلاغ عن المنشور'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'copy_link',
        child: Row(
          children: [
            Icon(Icons.link, color: Colors.green),
            SizedBox(width: 12),
            Text('نسخ الرابط'),
          ],
        ),
      ),
    ]);

    // إذا لم يكن المنشور للمستخدم الحالي، أضف خيار الحظر
    if (post.authorId != 'current_user') {
      items.add(
        const PopupMenuItem(
          value: 'block_user',
          child: Row(
            children: [
              Icon(Icons.block, color: Colors.red),
              SizedBox(width: 12),
              Text('حظر هذا الشخص'),
            ],
          ),
        ),
      );
    }

    return items;
  }
}

class _PostContent extends StatelessWidget {
  final Post post;

  const _PostContent({required this.post});

  // استخراج معرف صاحب المنشور الأصلي من المحتوى
  String? _extractOriginalAuthorId() {
    final lines = post.content.split('\n');
    for (final line in lines) {
      if (line.contains('--- إعادة نشر ---')) {
        final authorIds = ['1', '2', '3', '4', '5'];
        return authorIds[post.id.hashCode % authorIds.length];
      }
    }
    return null;
  }

  // الحصول على اسم المؤلف
  String _getAuthorName(String authorId) {
    switch (authorId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  // الحصول على جنس المؤلف
  String? _getUserGender(String authorId) {
    switch (authorId) {
      case '1': return 'male'; // أحمد محمد
      case '2': return 'female'; // فاطمة علي
      case '3': return 'male'; // محمد حسن
      case '4': return 'female'; // عائشة أحمد
      case '5': return 'male'; // عمر خالد
      case 'current_user': return 'male'; // يمكن تخصيصه
      default: return null; // سيتم التخمين من الاسم
    }
  }

  // التنقل إلى الملف الشخصي
  void _navigateToProfile(BuildContext context, String userId) {
    final authorName = _getAuthorName(userId);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FacebookProfileScreen(
          userId: userId,
          userName: authorName,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // كشف إعادة النشر
    final isRepost = post.content.contains('--- إعادة نشر ---');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // النص
        if (post.content.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              gradient: _getBackgroundGradient(post.background),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                vertical: post.background != PostBackground.none ? 24 : 0,
              ),
              child: isRepost ? _buildRepostContent(context) : _buildNormalContent(),
            ),
          ),
        
        // الوسائط (فقط للمنشورات العادية، ليس لإعادة النشر)
        if (post.media.isNotEmpty && !isRepost)
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: _buildMediaContent(context),
          ),
        
        // المستخدمون المذكورون
        if (post.taggedUsers.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Wrap(
              children: post.taggedUsers.map((userId) {
                return Container(
                  margin: const EdgeInsets.only(left: 4),
                  child: Chip(
                    label: Text(
                      _getAuthorName(userId),
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: AppTheme.primaryLightColor.withValues(alpha: 0.1),
                    side: BorderSide(color: AppTheme.primaryLightColor),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  // بناء محتوى إعادة النشر مثل Facebook
  Widget _buildRepostContent(BuildContext context) {
    final parts = post.content.split('--- إعادة نشر ---');
    final userComment = parts.isNotEmpty ? parts[0].trim() : '';
    final originalContent = parts.length > 1 ? parts[1].trim() : '';
    final originalAuthorId = _extractOriginalAuthorId();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تعليق المستخدم (إذا وجد)
        if (userComment.isNotEmpty) ...[
          Text(
            userComment,
            style: TextStyle(
              fontSize: post.background != PostBackground.none ? 20 : 16,
              fontWeight: post.background != PostBackground.none
                  ? FontWeight.bold
                  : FontWeight.normal,
              color: post.background != PostBackground.none
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
        ],

        // المنشور الأصلي في إطار مثل Facebook
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات صاحب المنشور الأصلي
              if (originalAuthorId != null)
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      // صورة صاحب المنشور الأصلي
                      GestureDetector(
                        onTap: () => _navigateToProfile(context, originalAuthorId),
                        child: SmartAvatarWithText(
                          name: _getAuthorName(originalAuthorId),
                          gender: _getUserGender(originalAuthorId),
                          radius: 16,
                        ),
                      ),

                      const SizedBox(width: 8),

                      // اسم صاحب المنشور الأصلي
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _navigateToProfile(context, originalAuthorId),
                          child: Text(
                            _getAuthorName(originalAuthorId),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // المحتوى الأصلي
              if (originalContent.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Text(
                    originalContent,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ),

              // وسائط المنشور الأصلي (إذا وجدت)
              if (post.media.isNotEmpty) ...[
                const SizedBox(height: 12),
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                  child: _buildMediaContent(context),
                ),
              ] else ...[
                const SizedBox(height: 12),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // بناء المحتوى العادي
  Widget _buildNormalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // النص
        Text(
          post.content,
          style: TextStyle(
            fontSize: post.background != PostBackground.none ? 20 : 16,
            fontWeight: post.background != PostBackground.none
                ? FontWeight.bold
                : FontWeight.normal,
            color: post.background != PostBackground.none
                ? Colors.white
                : Colors.black87,
          ),
        ),

        // معاينة الروابط
        if (post.linkPreview != null) ...[
          const SizedBox(height: 12),
          _buildLinkPreview(post.linkPreview!),
        ],
      ],
    );
  }

  // بناء معاينة الرابط
  Widget _buildLinkPreview(dynamic linkPreview) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المعاينة
          if (linkPreview.imageUrl != null)
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Image.network(
                linkPreview.imageUrl!,
                width: double.infinity,
                height: 200,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    color: Colors.grey[200],
                    child: const Center(
                      child: Icon(Icons.image_not_supported, color: Colors.grey),
                    ),
                  );
                },
              ),
            ),

          // معلومات الرابط
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان الرابط
                if (linkPreview.title != null)
                  Text(
                    linkPreview.title!,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                // وصف الرابط
                if (linkPreview.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    linkPreview.description!,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // رابط الموقع
                if (linkPreview.url != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.link, size: 16, color: Colors.blue[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          linkPreview.url!,
                          style: TextStyle(
                            color: Colors.blue[600],
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaContent(BuildContext context) {
    if (post.media.length == 1) {
      final media = post.media.first;

      // للصور: عرض كامل مثل فيسبوك
      if (media.type == PostType.image) {
        return Container(
          width: double.infinity,
          constraints: const BoxConstraints(
            maxHeight: 400, // حد أقصى للارتفاع
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: _buildMediaWidget(media, context),
          ),
        );
      }

      // للفيديوهات: نسبة عرض ثابتة
      return AspectRatio(
        aspectRatio: 16 / 9,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _buildMediaWidget(media, context),
        ),
      );
    }

    // عدة صور - شبكة
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: post.media.length,
      itemBuilder: (context, index) {
        final media = post.media[index];
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _buildMediaWidget(media, context),
        );
      },
    );
  }

  Widget _buildMediaWidget(PostMedia media, BuildContext context) {
    // التحقق من نوع الملف (محلي أم شبكة)
    final isLocalFile = media.url.startsWith('/') || media.url.contains('\\');

    if (media.type == PostType.image) {
      return GestureDetector(
        onTap: () => _openImageViewer(context, media),
        child: Stack(
          children: [
            if (isLocalFile) ...[
              // صورة محلية - عرض كامل مثل فيسبوك
              Image.file(
                File(media.url),
                width: double.infinity,
                fit: BoxFit.contain, // تغيير من cover إلى contain لعرض الصورة كاملة
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(Icons.broken_image, size: 50),
                    ),
                  );
                },
              ),
            ] else ...[
              // صورة من الشبكة - عرض كامل مثل فيسبوك
              Image.network(
                media.url,
                width: double.infinity,
                fit: BoxFit.contain, // تغيير من cover إلى contain لعرض الصورة كاملة
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(Icons.broken_image, size: 50),
                    ),
                  );
                },
              ),
            ],

            // أيقونة التكبير
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.zoom_in,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      );
    } else if (media.type == PostType.video) {
      // فيديو - مشغل حقيقي جديد
      return RealVideoPlayer(
        videoUrl: media.url,
        isLocalFile: isLocalFile,
        autoPlay: false, // عدم التشغيل التلقائي في المنشورات
        authorId: post.authorId,
        onDelete: () {
          // يمكن إضافة منطق حذف الفيديو هنا
          debugPrint('تم حذف الفيديو');
        },
      );
    }

    // نوع غير مدعوم
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: Icon(Icons.error, size: 50),
      ),
    );
  }



  LinearGradient? _getBackgroundGradient(PostBackground background) {
    switch (background) {
      case PostBackground.none:
        return null;
      case PostBackground.gradient1:
        return const LinearGradient(
          colors: [Colors.blue, Colors.purple],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case PostBackground.gradient2:
        return const LinearGradient(
          colors: [Colors.pink, Colors.orange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case PostBackground.gradient3:
        return const LinearGradient(
          colors: [Colors.green, Colors.teal],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case PostBackground.gradient4:
        return const LinearGradient(
          colors: [Colors.orange, Colors.red],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case PostBackground.gradient5:
        return const LinearGradient(
          colors: [Colors.purple, Colors.indigo],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  void _openImageViewer(BuildContext context, PostMedia selectedMedia) {
    // جمع جميع الصور من المنشور
    final images = post.media.where((media) => media.type == PostType.image).toList();
    final initialIndex = images.indexOf(selectedMedia);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ImageViewer(
          images: images,
          initialIndex: initialIndex >= 0 ? initialIndex : 0,
          postId: post.id,
          authorName: _getAuthorName(post.authorId),
        ),
      ),
    );
  }

}

// تم حذف _PostStats - لم نعد نحتاج الإحصائيات القديمة

class _PostActions extends StatelessWidget {
  final Post post;

  const _PostActions({required this.post});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // زر الإعجاب (مثل يوتيوب)
          Consumer<SocialProvider>(
            builder: (context, socialProvider, child) {
              final likeCount = post.reactions.where((r) => r.type == 'like').length;
              final userLiked = post.reactions.any((r) => r.type == 'like' && r.userId == 'current_user');

              return _YouTubeActionButton(
                icon: userLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                count: likeCount,
                isActive: userLiked,
                activeColor: Colors.blue,
                onTap: () {
                  if (userLiked) {
                    socialProvider.removeReaction(post.id);
                  } else {
                    socialProvider.addPostReaction(post.id, PostReaction(
                      userId: 'current_user',
                      type: 'like',
                      timestamp: DateTime.now(),
                    ));
                  }
                },
              );
            },
          ),

          // زر عدم الإعجاب (مثل يوتيوب)
          Consumer<SocialProvider>(
            builder: (context, socialProvider, child) {
              final dislikeCount = post.reactions.where((r) => r.type == 'dislike').length;
              final userDisliked = post.reactions.any((r) => r.type == 'dislike' && r.userId == 'current_user');

              return _YouTubeActionButton(
                icon: userDisliked ? Icons.thumb_down : Icons.thumb_down_outlined,
                count: dislikeCount,
                isActive: userDisliked,
                activeColor: Colors.red,
                onTap: () {
                  if (userDisliked) {
                    socialProvider.removeReaction(post.id);
                  } else {
                    socialProvider.addPostReaction(post.id, PostReaction(
                      userId: 'current_user',
                      type: 'dislike',
                      timestamp: DateTime.now(),
                    ));
                  }
                },
              );
            },
          ),

          // زر التعليق (مثل تويتر)
          _TwitterActionButton(
            icon: Icons.chat_bubble_outline,
            count: post.comments.length,
            onTap: () => _showCommentsDialog(context),
          ),

          // زر المشاركة (مثل تويتر)
          _TwitterActionButton(
            icon: Icons.share_outlined,
            count: post.shareCount,
            onTap: () => _sharePostSimple(context),
          ),

          // زر إعادة النشر (مثل تويتر)
          _TwitterActionButton(
            icon: Icons.repeat_outlined,
            count: post.reposts.length,
            onTap: () => _repostPostSimple(context),
          ),
        ],
      ),
    );
  }

  void _showCommentsDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CommentsSection(post: post),
    );
  }

  void _sharePostSimple(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            // العنوان
            const Text(
              'مشاركة المنشور',
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // خيارات المشاركة بتصميم فيسبوك
            _buildFacebookStyleOption(
              context: context,
              icon: Icons.share,
              title: 'مشاركة خارجية',
              subtitle: 'مشاركة عبر التطبيقات الأخرى',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                PostShareHelpers.performExternalShare(context, post);
              },
            ),

            _buildFacebookStyleOption(
              context: context,
              icon: Icons.post_add,
              title: 'مشاركة كمنشور',
              subtitle: 'نشر في الصفحة الرئيسية',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                PostShareHelpers.shareAsNewPost(context, post);
              },
            ),

            _buildFacebookStyleOption(
              context: context,
              icon: Icons.message,
              title: 'إرسال في رسالة',
              subtitle: 'إرسال لصديق في المحادثات',
              color: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                PostShareHelpers.sendInMessage(context, post);
              },
            ),

            _buildFacebookStyleOption(
              context: context,
              icon: Icons.group,
              title: 'مشاركة في مجموعة',
              subtitle: 'نشر في إحدى المجموعات',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                PostShareHelpers.shareInGroup(context, post);
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _repostPostSimple(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            // العنوان
            const Text(
              'إعادة نشر المنشور',
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // خيارات إعادة النشر بتصميم فيسبوك
            _buildFacebookStyleOption(
              context: context,
              icon: Icons.repeat,
              title: 'إعادة نشر فوري',
              subtitle: 'نشر المنشور على حسابك مباشرة',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                PostShareHelpers.repostInstantly(context, post);
              },
            ),

            _buildFacebookStyleOption(
              context: context,
              icon: Icons.edit,
              title: 'إعادة نشر مع أفكارك',
              subtitle: 'أضف تعليقك على المنشور',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                PostShareHelpers.repostWithComment(context, post);
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // بناء خيار بتصميم فيسبوك
  Widget _buildFacebookStyleOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// زر يوتيوب للإعجاب/عدم الإعجاب
class _YouTubeActionButton extends StatelessWidget {
  final IconData icon;
  final int count;
  final bool isActive;
  final Color activeColor;
  final VoidCallback onTap;

  const _YouTubeActionButton({
    required this.icon,
    required this.count,
    required this.isActive,
    required this.activeColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? activeColor : Colors.grey[600],
              size: 22,
            ),
            if (count > 0) ...[
              const SizedBox(width: 6),
              Text(
                count.toString(),
                style: TextStyle(
                  color: isActive ? activeColor : Colors.grey[600],
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// زر تويتر للتعليق/المشاركة/إعادة النشر
class _TwitterActionButton extends StatelessWidget {
  final IconData icon;
  final int count;
  final VoidCallback onTap;

  const _TwitterActionButton({
    required this.icon,
    required this.count,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.grey[600],
              size: 22,
            ),
            if (count > 0) ...[
              const SizedBox(width: 6),
              Text(
                count.toString(),
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
