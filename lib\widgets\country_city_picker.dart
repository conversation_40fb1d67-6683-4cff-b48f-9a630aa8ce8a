import 'package:flutter/material.dart';

class CountryCityPicker extends StatelessWidget {
  final String selectedCountry;
  final String selectedCity;
  final Function(String) onCountryChanged;
  final Function(String) onCityChanged;

  const CountryCityPicker({
    super.key,
    required this.selectedCountry,
    required this.selectedCity,
    required this.onCountryChanged,
    required this.onCityChanged,
  });

  static const Map<String, List<String>> _countriesAndCities = {
    'السعودية': [
      'الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 
      'الخبر', 'تبوك', 'بريدة', 'خميس مشيط', 'حائل'
    ],
    'الإمارات': [
      'دبي', 'أبوظبي', 'الشارقة', 'عجمان', 'الفجيرة', 
      'رأس الخيمة', 'أم القيوين'
    ],
    'مصر': [
      'القاهرة', 'الإسكندرية', 'الجيزة', 'شبرا الخيمة', 'بورسعيد',
      'السويس', 'الأقصر', 'أسوان', 'المنصورة', 'طنطا'
    ],
    'الأردن': [
      'عمان', 'إربد', 'الزرقاء', 'الرصيفة', 'وادي السير',
      'العقبة', 'السلط', 'المفرق', 'جرش', 'مادبا'
    ],
    'لبنان': [
      'بيروت', 'طرابلس', 'صيدا', 'صور', 'جونيه',
      'زحلة', 'بعلبك', 'النبطية', 'جبيل', 'بشري'
    ],
    'الكويت': [
      'مدينة الكويت', 'حولي', 'الفروانية', 'الأحمدي', 'الجهراء',
      'مبارك الكبير'
    ],
    'قطر': [
      'الدوحة', 'الريان', 'أم صلال', 'الوكرة', 'الخور',
      'دخان', 'مسيعيد', 'الشمال'
    ],
    'البحرين': [
      'المنامة', 'المحرق', 'الرفاع', 'حمد', 'عيسى',
      'سترة', 'جدحفص', 'الدراز'
    ],
    'عمان': [
      'مسقط', 'صلالة', 'نزوى', 'صور', 'البريمي',
      'خصب', 'الرستاق', 'عبري', 'بهلاء', 'إبراء'
    ],
    'العراق': [
      'بغداد', 'البصرة', 'الموصل', 'أربيل', 'النجف',
      'كربلاء', 'السليمانية', 'كركوك', 'الحلة', 'الرمادي'
    ],
    'سوريا': [
      'دمشق', 'حلب', 'حمص', 'اللاذقية', 'حماة',
      'دير الزور', 'الرقة', 'درعا', 'السويداء', 'طرطوس'
    ],
    'المغرب': [
      'الدار البيضاء', 'الرباط', 'فاس', 'مراكش', 'أغادير',
      'مكناس', 'وجدة', 'القنيطرة', 'تطوان', 'سلا'
    ],
    'الجزائر': [
      'الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'باتنة',
      'سطيف', 'سيدي بلعباس', 'بسكرة', 'تلمسان', 'بجاية'
    ],
    'تونس': [
      'تونس', 'صفاقس', 'سوسة', 'القيروان', 'بنزرت',
      'قابس', 'أريانة', 'قفصة', 'المنستير', 'مدنين'
    ],
    'ليبيا': [
      'طرابلس', 'بنغازي', 'مصراتة', 'الزاوية', 'البيضاء',
      'طبرق', 'سبها', 'الخمس', 'زليتن', 'درنة'
    ],
    'السودان': [
      'الخرطوم', 'أم درمان', 'بحري', 'بورتسودان', 'كسلا',
      'القضارف', 'الأبيض', 'نيالا', 'الفاشر', 'كوستي'
    ],
    'اليمن': [
      'صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب',
      'ذمار', 'المكلا', 'سيئون', 'زنجبار', 'الباب'
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // منتقي البلد
        _buildDropdown(
          label: 'البلد',
          value: selectedCountry.isEmpty ? null : selectedCountry,
          items: _countriesAndCities.keys.toList(),
          onChanged: (value) {
            if (value != null) {
              onCountryChanged(value);
            }
          },
        ),
        
        const SizedBox(height: 16),
        
        // منتقي المدينة
        _buildDropdown(
          label: 'المدينة',
          value: selectedCity.isEmpty ? null : selectedCity,
          items: selectedCountry.isEmpty 
              ? []
              : _countriesAndCities[selectedCountry] ?? [],
          onChanged: (value) {
            if (value != null) {
              onCityChanged(value);
            }
          },
          enabled: selectedCountry.isNotEmpty,
        ),
      ],
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(
              color: value != null && enabled
                  ? const Color(0xFF1877F2)
                  : Colors.grey[300]!,
              width: value != null && enabled ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: enabled ? Colors.white : Colors.grey[50],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              hint: Text(
                'اختر $label',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              isExpanded: true,
              icon: Icon(
                Icons.arrow_drop_down,
                color: enabled ? Colors.grey[600] : Colors.grey[400],
              ),
              items: items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                );
              }).toList(),
              onChanged: enabled ? onChanged : null,
              dropdownColor: Colors.white,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
