import 'package:flutter/material.dart';
import 'dart:ui' as ui;

class StoryDrawingTool extends StatefulWidget {
  final Function(List<DrawingPoint>) onDrawingChanged;

  const StoryDrawingTool({
    super.key,
    required this.onDrawingChanged,
  });

  @override
  State<StoryDrawingTool> createState() => _StoryDrawingToolState();
}

class _StoryDrawingToolState extends State<StoryDrawingTool> {
  List<DrawingPoint> _points = [];
  Color _selectedColor = Colors.red;
  double _strokeWidth = 3.0;
  bool _isDrawing = false;

  final List<Color> _colors = [
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
    Colors.white,
    Colors.black,
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // أدوات الرسم
        _buildDrawingTools(),
        
        // منطقة الرسم
        Expanded(
          child: Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: GestureDetector(
                onPanStart: _onPanStart,
                onPanUpdate: _onPanUpdate,
                onPanEnd: _onPanEnd,
                child: CustomPaint(
                  painter: DrawingPainter(_points),
                  size: Size.infinite,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDrawingTools() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // منتقي الألوان
          Row(
            children: [
              const Text(
                'اللون:',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _colors.map((color) => _buildColorOption(color)).toList(),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // شريط تمرير سمك الخط
          Row(
            children: [
              const Text(
                'السمك:',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Slider(
                  value: _strokeWidth,
                  min: 1.0,
                  max: 10.0,
                  divisions: 9,
                  activeColor: _selectedColor,
                  inactiveColor: Colors.grey,
                  onChanged: (value) {
                    setState(() {
                      _strokeWidth = value;
                    });
                  },
                ),
              ),
              Text(
                '${_strokeWidth.round()}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // أزرار التحكم
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon: Icons.undo,
                label: 'تراجع',
                onTap: _undo,
              ),
              _buildControlButton(
                icon: Icons.clear,
                label: 'مسح الكل',
                onTap: _clearAll,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorOption(Color color) {
    final isSelected = _selectedColor == color;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedColor = color;
        });
      },
      child: Container(
        width: 30,
        height: 30,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? Colors.white : Colors.transparent,
            width: 2,
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDrawing = true;
      _points.add(DrawingPoint(
        offset: details.localPosition,
        paint: Paint()
          ..color = _selectedColor
          ..strokeWidth = _strokeWidth
          ..strokeCap = StrokeCap.round,
      ));
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_isDrawing) {
      setState(() {
        _points.add(DrawingPoint(
          offset: details.localPosition,
          paint: Paint()
            ..color = _selectedColor
            ..strokeWidth = _strokeWidth
            ..strokeCap = StrokeCap.round,
        ));
      });
      widget.onDrawingChanged(_points);
    }
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDrawing = false;
      _points.add(DrawingPoint(offset: null, paint: Paint()));
    });
  }

  void _undo() {
    if (_points.isNotEmpty) {
      setState(() {
        // إزالة النقاط حتى الوصول لنقطة فاصلة (null)
        while (_points.isNotEmpty && _points.last.offset != null) {
          _points.removeLast();
        }
        // إزالة النقطة الفاصلة أيضاً
        if (_points.isNotEmpty) {
          _points.removeLast();
        }
      });
      widget.onDrawingChanged(_points);
    }
  }

  void _clearAll() {
    setState(() {
      _points.clear();
    });
    widget.onDrawingChanged(_points);
  }
}

class DrawingPoint {
  final Offset? offset;
  final Paint paint;

  DrawingPoint({required this.offset, required this.paint});
}

class DrawingPainter extends CustomPainter {
  final List<DrawingPoint> points;

  DrawingPainter(this.points);

  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < points.length - 1; i++) {
      if (points[i].offset != null && points[i + 1].offset != null) {
        canvas.drawLine(
          points[i].offset!,
          points[i + 1].offset!,
          points[i].paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
