import 'package:flutter/material.dart';
import '../models/app_settings.dart' as app_settings;
import '../services/app_settings_service.dart';

class AppSettingsProvider with ChangeNotifier {
  final AppSettingsService _settingsService = AppSettingsService();
  
  app_settings.AppSettings _settings = const app_settings.AppSettings();
  bool _isLoading = false;
  String? _errorMessage;
  double _cacheSize = 0.0;

  // Getters
  app_settings.AppSettings get settings => _settings;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  double get cacheSize => _cacheSize;

  // إعدادات المظهر
  app_settings.ThemeMode get themeMode => _settings.themeMode;
  app_settings.FontSize get fontSize => _settings.fontSize;
  bool get isDarkMode => _settings.themeMode == app_settings.ThemeMode.dark;

  // إعدادات الخصوصية
  app_settings.PrivacySetting get lastSeenPrivacy => _settings.lastSeenPrivacy;
  app_settings.PrivacySetting get profilePhotoPrivacy => _settings.profilePhotoPrivacy;
  app_settings.PrivacySetting get statusPrivacy => _settings.statusPrivacy;
  bool get readReceiptsEnabled => _settings.readReceiptsEnabled;
  List<String> get blockedUsers => _settings.blockedUsers;

  // إعدادات التحميل
  app_settings.AutoDownloadSetting get autoDownloadPhotos => _settings.autoDownloadPhotos;
  app_settings.AutoDownloadSetting get autoDownloadVideos => _settings.autoDownloadVideos;
  app_settings.AutoDownloadSetting get autoDownloadDocuments => _settings.autoDownloadDocuments;
  
  // إعدادات التخزين
  bool get cacheEnabled => _settings.cacheEnabled;
  int get maxCacheSize => _settings.maxCacheSize;
  
  // إعدادات النسخ الاحتياطي
  bool get backupEnabled => _settings.backupEnabled;
  String? get backupEmail => _settings.backupEmail;
  DateTime? get lastBackupDate => _settings.lastBackupDate;
  
  AppSettingsProvider() {
    loadSettings();
  }
  
  // تحميل الإعدادات
  Future<void> loadSettings() async {
    try {
      _setLoading(true);
      _settings = await _settingsService.loadSettings();
      await _loadCacheSize();
      _clearError();
    } catch (e) {
      _setError('خطأ في تحميل الإعدادات: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      await _settingsService.saveSettings(_settings);
      _clearError();
    } catch (e) {
      _setError('خطأ في حفظ الإعدادات: $e');
    }
  }
  
  // تحديث وضع المظهر
  Future<void> updateThemeMode(app_settings.ThemeMode themeMode) async {
    try {
      _settings = _settings.copyWith(themeMode: themeMode);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث وضع المظهر: $e');
    }
  }

  // تحديث حجم الخط
  Future<void> updateFontSize(app_settings.FontSize fontSize) async {
    try {
      _settings = _settings.copyWith(fontSize: fontSize);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث حجم الخط: $e');
    }
  }
  
  // تحديث إعدادات الخصوصية
  Future<void> updateLastSeenPrivacy(app_settings.PrivacySetting privacy) async {
    try {
      _settings = _settings.copyWith(lastSeenPrivacy: privacy);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات آخر ظهور: $e');
    }
  }

  Future<void> updateProfilePhotoPrivacy(app_settings.PrivacySetting privacy) async {
    try {
      _settings = _settings.copyWith(profilePhotoPrivacy: privacy);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات صورة الملف الشخصي: $e');
    }
  }

  Future<void> updateStatusPrivacy(app_settings.PrivacySetting privacy) async {
    try {
      _settings = _settings.copyWith(statusPrivacy: privacy);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات الحالة: $e');
    }
  }
  
  Future<void> updateReadReceipts(bool enabled) async {
    try {
      _settings = _settings.copyWith(readReceiptsEnabled: enabled);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات إيصالات القراءة: $e');
    }
  }
  
  // حظر مستخدم
  Future<void> blockUser(String userId) async {
    try {
      await _settingsService.blockUser(userId);
      await loadSettings(); // إعادة تحميل الإعدادات
    } catch (e) {
      _setError('خطأ في حظر المستخدم: $e');
    }
  }
  
  // إلغاء حظر مستخدم
  Future<void> unblockUser(String userId) async {
    try {
      await _settingsService.unblockUser(userId);
      await loadSettings(); // إعادة تحميل الإعدادات
    } catch (e) {
      _setError('خطأ في إلغاء حظر المستخدم: $e');
    }
  }
  
  // التحقق من كون المستخدم محظور
  Future<bool> isUserBlocked(String userId) async {
    try {
      return await _settingsService.isUserBlocked(userId);
    } catch (e) {
      _setError('خطأ في التحقق من حظر المستخدم: $e');
      return false;
    }
  }
  
  // تحديث إعدادات التحميل التلقائي
  Future<void> updateAutoDownloadPhotos(app_settings.AutoDownloadSetting setting) async {
    try {
      _settings = _settings.copyWith(autoDownloadPhotos: setting);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات تحميل الصور: $e');
    }
  }

  Future<void> updateAutoDownloadVideos(app_settings.AutoDownloadSetting setting) async {
    try {
      _settings = _settings.copyWith(autoDownloadVideos: setting);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات تحميل الفيديوهات: $e');
    }
  }

  Future<void> updateAutoDownloadDocuments(app_settings.AutoDownloadSetting setting) async {
    try {
      _settings = _settings.copyWith(autoDownloadDocuments: setting);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات تحميل المستندات: $e');
    }
  }
  
  // إدارة ذاكرة التخزين المؤقت
  Future<void> _loadCacheSize() async {
    try {
      _cacheSize = await _settingsService.getCacheSize();
    } catch (e) {
      print('خطأ في تحميل حجم ذاكرة التخزين المؤقت: $e');
    }
  }
  
  Future<void> clearCache() async {
    try {
      _setLoading(true);
      await _settingsService.clearCache();
      await _loadCacheSize();
      _clearError();
    } catch (e) {
      _setError('خطأ في مسح ذاكرة التخزين المؤقت: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  Future<void> updateCacheEnabled(bool enabled) async {
    try {
      _settings = _settings.copyWith(cacheEnabled: enabled);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات ذاكرة التخزين المؤقت: $e');
    }
  }
  
  Future<void> updateMaxCacheSize(int size) async {
    try {
      _settings = _settings.copyWith(maxCacheSize: size);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث حجم ذاكرة التخزين المؤقت: $e');
    }
  }
  
  // إدارة النسخ الاحتياطي
  Future<void> createBackup(String email) async {
    try {
      _setLoading(true);
      await _settingsService.createBackup(email);
      await loadSettings(); // إعادة تحميل الإعدادات
      _clearError();
    } catch (e) {
      _setError('خطأ في إنشاء النسخة الاحتياطية: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  Future<void> restoreBackup(String email) async {
    try {
      _setLoading(true);
      await _settingsService.restoreBackup(email);
      await loadSettings(); // إعادة تحميل الإعدادات
      _clearError();
    } catch (e) {
      _setError('خطأ في استعادة النسخة الاحتياطية: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  Future<void> updateBackupEnabled(bool enabled) async {
    try {
      _settings = _settings.copyWith(backupEnabled: enabled);
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات النسخ الاحتياطي: $e');
    }
  }
  
  // تصدير واستيراد البيانات
  Future<String> exportData() async {
    try {
      _setLoading(true);
      final filePath = await _settingsService.exportData();
      _clearError();
      return filePath;
    } catch (e) {
      _setError('خطأ في تصدير البيانات: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }
  
  Future<void> importData(String filePath) async {
    try {
      _setLoading(true);
      await _settingsService.importData(filePath);
      await loadSettings(); // إعادة تحميل الإعدادات
      _clearError();
    } catch (e) {
      _setError('خطأ في استيراد البيانات: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // إعادة تعيين الإعدادات
  Future<void> resetToDefaults() async {
    try {
      _setLoading(true);
      await _settingsService.resetToDefaults();
      await loadSettings(); // إعادة تحميل الإعدادات
      _clearError();
    } catch (e) {
      _setError('خطأ في إعادة تعيين الإعدادات: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // إرسال تقرير مشكلة
  Future<void> reportProblem({
    required String title,
    required String description,
    required String category,
    List<String>? attachments,
  }) async {
    try {
      _setLoading(true);
      await _settingsService.reportProblem(
        title: title,
        description: description,
        category: category,
        attachments: attachments,
      );
      _clearError();
    } catch (e) {
      _setError('خطأ في إرسال تقرير المشكلة: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // دوال مساعدة لإدارة الحالة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  
  // الحصول على TextStyle بناءً على حجم الخط المحدد
  TextStyle getTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * _settings.fontSizeMultiplier,
    );
  }
  
  // الحصول على ThemeData بناءً على الإعدادات
  ThemeData getThemeData() {
    final isDark = _settings.themeMode == app_settings.ThemeMode.dark;
    
    return ThemeData(
      brightness: isDark ? Brightness.dark : Brightness.light,
      primarySwatch: Colors.blue,
      fontFamily: 'Cairo', // يمكن تخصيص الخط
      textTheme: TextTheme(
        bodyLarge: TextStyle(
          fontSize: 16 * _settings.fontSizeMultiplier,
        ),
        bodyMedium: TextStyle(
          fontSize: 14 * _settings.fontSizeMultiplier,
        ),
        bodySmall: TextStyle(
          fontSize: 12 * _settings.fontSizeMultiplier,
        ),
        headlineLarge: TextStyle(
          fontSize: 32 * _settings.fontSizeMultiplier,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          fontSize: 28 * _settings.fontSizeMultiplier,
          fontWeight: FontWeight.bold,
        ),
        headlineSmall: TextStyle(
          fontSize: 24 * _settings.fontSizeMultiplier,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
