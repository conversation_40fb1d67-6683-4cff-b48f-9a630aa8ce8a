import 'link_preview.dart';

enum PostType {
  text,
  image,
  video,
  audio,
  liveStream,
}

enum PostBackground {
  none,
  gradient1,
  gradient2,
  gradient3,
  gradient4,
  gradient5,
}

class PostMedia {
  final String id;
  final String url;
  final PostType type;
  final String? thumbnail;
  final Duration? duration;

  const PostMedia({
    required this.id,
    required this.url,
    required this.type,
    this.thumbnail,
    this.duration,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'type': type.name,
      'thumbnail': thumbnail,
      'duration': duration?.inSeconds,
    };
  }

  factory PostMedia.fromJson(Map<String, dynamic> json) {
    return PostMedia(
      id: json['id'],
      url: json['url'],
      type: PostType.values.firstWhere((e) => e.name == json['type']),
      thumbnail: json['thumbnail'],
      duration: json['duration'] != null 
          ? Duration(seconds: json['duration']) 
          : null,
    );
  }
}

class PostReaction {
  final String userId;
  final String type; // 'like' أو 'dislike' فقط (مثل يوتيوب)
  final DateTime timestamp;

  const PostReaction({
    required this.userId,
    required this.type,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'type': type,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PostReaction.fromJson(Map<String, dynamic> json) {
    return PostReaction(
      userId: json['userId'],
      type: json['type'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class PostComment {
  final String id;
  final String userId;
  final String content;
  final DateTime timestamp;
  final List<PostReaction> reactions;

  const PostComment({
    required this.id,
    required this.userId,
    required this.content,
    required this.timestamp,
    this.reactions = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'reactions': reactions.map((r) => r.toJson()).toList(),
    };
  }

  factory PostComment.fromJson(Map<String, dynamic> json) {
    return PostComment(
      id: json['id'],
      userId: json['userId'],
      content: json['content'],
      timestamp: DateTime.parse(json['timestamp']),
      reactions: (json['reactions'] as List?)
          ?.map((r) => PostReaction.fromJson(r))
          .toList() ?? [],
    );
  }
}

class Post {
  final String id;
  final String authorId;
  final String content;
  final PostType type;
  final List<PostMedia> media;
  final PostBackground background;
  final List<String> taggedUsers;
  final String? feeling;
  final String? activity;
  final String? location;
  final String? groupId; // معرف المجموعة (إذا كان المنشور في مجموعة)

  // معلومات إضافية للمشاعر والأنشطة
  final String? feelingDisplay; // النص المعروض مع الإيموجي
  final String? activityDisplay; // النص المعروض مع الأيقونة
  final String? locationDisplay; // النص المعروض مع الموقع
  final DateTime timestamp;
  final List<PostReaction> reactions;
  final List<PostComment> comments;
  final int shareCount;
  final List<PostShare> shares;
  final List<PostRepost> reposts;
  final bool isPublic;
  final LinkPreview? linkPreview;

  const Post({
    required this.id,
    required this.authorId,
    required this.content,
    this.type = PostType.text,
    this.media = const [],
    this.background = PostBackground.none,
    this.taggedUsers = const [],
    this.feeling,
    this.activity,
    this.location,
    this.groupId,
    this.feelingDisplay,
    this.activityDisplay,
    this.locationDisplay,
    required this.timestamp,
    this.reactions = const [],
    this.comments = const [],
    this.shareCount = 0,
    this.shares = const [],
    this.reposts = const [],
    this.isPublic = true,
    this.linkPreview,
  });

  Post copyWith({
    String? id,
    String? authorId,
    String? content,
    PostType? type,
    List<PostMedia>? media,
    PostBackground? background,
    List<String>? taggedUsers,
    String? feeling,
    String? activity,
    String? location,
    String? groupId,
    String? feelingDisplay,
    String? activityDisplay,
    String? locationDisplay,
    DateTime? timestamp,
    List<PostReaction>? reactions,
    List<PostComment>? comments,
    int? shareCount,
    List<PostShare>? shares,
    List<PostRepost>? reposts,
    bool? isPublic,
    LinkPreview? linkPreview,
  }) {
    return Post(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      content: content ?? this.content,
      type: type ?? this.type,
      media: media ?? this.media,
      background: background ?? this.background,
      taggedUsers: taggedUsers ?? this.taggedUsers,
      feeling: feeling ?? this.feeling,
      activity: activity ?? this.activity,
      location: location ?? this.location,
      groupId: groupId ?? this.groupId,
      feelingDisplay: feelingDisplay ?? this.feelingDisplay,
      activityDisplay: activityDisplay ?? this.activityDisplay,
      locationDisplay: locationDisplay ?? this.locationDisplay,
      timestamp: timestamp ?? this.timestamp,
      reactions: reactions ?? this.reactions,
      comments: comments ?? this.comments,
      shareCount: shareCount ?? this.shareCount,
      shares: shares ?? this.shares,
      reposts: reposts ?? this.reposts,
      isPublic: isPublic ?? this.isPublic,
      linkPreview: linkPreview ?? this.linkPreview,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'authorId': authorId,
      'content': content,
      'type': type.name,
      'media': media.map((m) => m.toJson()).toList(),
      'background': background.name,
      'taggedUsers': taggedUsers,
      'feeling': feeling,
      'activity': activity,
      'location': location,
      'groupId': groupId,
      'feelingDisplay': feelingDisplay,
      'activityDisplay': activityDisplay,
      'locationDisplay': locationDisplay,
      'timestamp': timestamp.toIso8601String(),
      'reactions': reactions.map((r) => r.toJson()).toList(),
      'comments': comments.map((c) => c.toJson()).toList(),
      'shareCount': shareCount,
      'isPublic': isPublic,
    };
  }

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'],
      authorId: json['authorId'],
      content: json['content'],
      type: PostType.values.firstWhere((e) => e.name == json['type']),
      media: (json['media'] as List?)
          ?.map((m) => PostMedia.fromJson(m))
          .toList() ?? [],
      background: PostBackground.values.firstWhere(
        (e) => e.name == json['background'],
        orElse: () => PostBackground.none,
      ),
      taggedUsers: List<String>.from(json['taggedUsers'] ?? []),
      feeling: json['feeling'],
      activity: json['activity'],
      location: json['location'],
      groupId: json['groupId'],
      feelingDisplay: json['feelingDisplay'],
      activityDisplay: json['activityDisplay'],
      locationDisplay: json['locationDisplay'],
      timestamp: DateTime.parse(json['timestamp']),
      reactions: (json['reactions'] as List?)
          ?.map((r) => PostReaction.fromJson(r))
          .toList() ?? [],
      comments: (json['comments'] as List?)
          ?.map((c) => PostComment.fromJson(c))
          .toList() ?? [],
      shareCount: json['shareCount'] ?? 0,
      isPublic: json['isPublic'] ?? true,
    );
  }
}

class PostShare {
  final String userId;
  final String shareType; // 'external', 'message', 'group', 'story'
  final DateTime timestamp;
  final String? targetId; // معرف المجموعة أو المحادثة إذا كانت مشاركة داخلية

  const PostShare({
    required this.userId,
    required this.shareType,
    required this.timestamp,
    this.targetId,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'shareType': shareType,
      'timestamp': timestamp.toIso8601String(),
      'targetId': targetId,
    };
  }

  factory PostShare.fromJson(Map<String, dynamic> json) {
    return PostShare(
      userId: json['userId'],
      shareType: json['shareType'],
      timestamp: DateTime.parse(json['timestamp']),
      targetId: json['targetId'],
    );
  }
}

class PostRepost {
  final String userId;
  final String? customComment; // تعليق مخصص مع إعادة النشر
  final DateTime timestamp;

  const PostRepost({
    required this.userId,
    required this.timestamp,
    this.customComment,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'customComment': customComment,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PostRepost.fromJson(Map<String, dynamic> json) {
    return PostRepost(
      userId: json['userId'],
      customComment: json['customComment'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
