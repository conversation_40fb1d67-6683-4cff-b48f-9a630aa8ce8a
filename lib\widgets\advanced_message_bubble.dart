import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'dart:io';
import '../models/message.dart';
import '../models/user.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import '../widgets/voice_message_widget.dart';
import 'message_context_menu.dart';
import 'swipeable_message.dart';
import '../widgets/full_screen_image_viewer.dart';

class AdvancedMessageBubble extends StatefulWidget {
  final Message message;
  final bool isMe;
  final User? otherUser;
  final VoidCallback? onReply;
  final VoidCallback? onDelete;
  final VoidCallback? onForward;
  final VoidCallback? onShare;
  final VoidCallback? onPin;
  final VoidCallback? onEdit;
  final Function(String emoji)? onReact;
  final VoidCallback? onArchive;
  final VoidCallback? onMarkUnread;
  final bool showAvatar;
  final bool showTimestamp;

  const AdvancedMessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.otherUser,
    this.onReply,
    this.onDelete,
    this.onForward,
    this.onShare,
    this.onPin,
    this.onEdit,
    this.onReact,
    this.onArchive,
    this.onMarkUnread,
    this.showAvatar = true,
    this.showTimestamp = true,
  });

  @override
  State<AdvancedMessageBubble> createState() => _AdvancedMessageBubbleState();
}

class _AdvancedMessageBubbleState extends State<AdvancedMessageBubble>
    with SingleTickerProviderStateMixin {
  bool _showActions = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SwipeableMessage(
      message: widget.message,
      isMe: widget.isMe,
      onReply: widget.onReply,
      onDelete: widget.onDelete,
      onArchive: widget.onArchive,
      onMarkUnread: widget.onMarkUnread,
      child: GestureDetector(
        onLongPress: _showMessageActions,
        onTap: () {
          if (_showActions) {
            setState(() {
              _showActions = false;
            });
          }
        },
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                child: Column(
                  children: [
                    // الرسالة المرد عليها (إن وجدت)
                    if (widget.message.replyToMessage != null)
                      _buildReplyPreview(),

                    // الرسالة الأساسية
                    Row(
                      mainAxisAlignment: widget.isMe
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        // أفاتار المرسل (للرسائل الواردة)
                        if (!widget.isMe && widget.showAvatar && widget.otherUser != null) ...[
                          SmartAvatarWithText(
                            user: widget.otherUser!,
                            radius: 16,
                          ),
                          const SizedBox(width: 8),
                        ],

                        // فقاعة الرسالة
                        Flexible(
                          child: Container(
                            constraints: BoxConstraints(
                              maxWidth: MediaQuery.of(context).size.width * 0.75,
                            ),
                            child: Column(
                              crossAxisAlignment: widget.isMe
                                  ? CrossAxisAlignment.end
                                  : CrossAxisAlignment.start,
                              children: [
                                _buildMessageContent(),
                                if (widget.message.reactions.isNotEmpty)
                                  _buildReactionsDisplay(),
                                if (widget.showTimestamp)
                                  _buildTimestamp(),
                              ],
                            ),
                          ),
                        ),

                        // أفاتار المرسل (للرسائل المرسلة)
                        if (widget.isMe && widget.showAvatar) ...[
                          const SizedBox(width: 8),
                          SmartAvatarWithText(
                            name: 'أنت',
                            gender: 'male',
                            radius: 16,
                          ),
                        ],
                      ],
                    ),

                    // أزرار الإجراءات
                    if (_showActions)
                      _buildActionButtons(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildReplyPreview() {
    final replyMessage = widget.message.replyToMessage!;
    return Container(
      margin: EdgeInsets.only(
        left: widget.isMe ? 50 : 0,
        right: widget.isMe ? 0 : 50,
        bottom: 4,
      ),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(
            color: widget.isMe ? AppTheme.primaryColor : Colors.grey,
            width: 3,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.isMe ? 'أنت' : (widget.otherUser?.name ?? 'مستخدم'),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: widget.isMe ? AppTheme.primaryColor : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 2),
          Text(
            replyMessage.content,
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black87,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContent() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: widget.isMe 
            ? AppTheme.primaryColor 
            : Colors.grey[200],
        borderRadius: BorderRadius.circular(20).copyWith(
          bottomLeft: widget.isMe 
              ? const Radius.circular(20) 
              : const Radius.circular(4),
          bottomRight: widget.isMe 
              ? const Radius.circular(4) 
              : const Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMessageByType(),
          if (widget.isMe)
            _buildMessageStatus(),
        ],
      ),
    );
  }

  Widget _buildMessageByType() {
    switch (widget.message.type) {
      case MessageType.text:
        return _buildTextMessage();
      case MessageType.image:
        return _buildImageMessage();
      case MessageType.video:
        return _buildVideoMessage();
      case MessageType.audio:
        return _buildAudioMessage();
      case MessageType.file:
        return _buildFileMessage();
      case MessageType.location:
        return _buildLocationMessage();
      case MessageType.emoji:
        return _buildEmojiMessage();
      default:
        return _buildTextMessage();
    }
  }

  Widget _buildTextMessage() {
    return Text(
      widget.message.content,
      style: TextStyle(
        color: widget.isMe ? Colors.white : Colors.black87,
        fontSize: 16,
      ),
    );
  }

  Widget _buildImageMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.message.content.isNotEmpty) ...[
          Text(
            widget.message.content,
            style: TextStyle(
              color: widget.isMe ? Colors.white : Colors.black87,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
        ],
        GestureDetector(
          onTap: () => _showFullScreenImage(),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: widget.message.mediaUrl != null
                ? _buildImageWidget()
                : Container(
                    width: 200,
                    height: 200,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image, size: 50),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.message.content.isNotEmpty) ...[
          Text(
            widget.message.content,
            style: TextStyle(
              color: widget.isMe ? Colors.white : Colors.black87,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
        ],
        GestureDetector(
          onTap: () => _playVideo(),
          child: Stack(
            alignment: Alignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildVideoThumbnail(),
              ),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 30,
                ),
              ),
              // مدة الفيديو
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    widget.message.videoDuration != null
                        ? _formatDuration(widget.message.videoDuration!)
                        : '0:00',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAudioMessage() {
    return VoiceMessageWidget(
      duration: widget.message.audioDuration ?? const Duration(seconds: 30),
      isMe: widget.isMe,
      audioUrl: widget.message.mediaUrl,
      onPlay: () {
        // تشغيل الرسالة الصوتية
      },
      onPause: () {
        // إيقاف الرسالة الصوتية
      },
    );
  }

  Widget _buildFileMessage() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.attach_file,
          color: widget.isMe ? Colors.white : AppTheme.primaryColor,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.message.fileName ?? 'ملف',
                style: TextStyle(
                  color: widget.isMe ? Colors.white : Colors.black87,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (widget.message.fileSize != null)
                Text(
                  _formatFileSize(widget.message.fileSize!),
                  style: TextStyle(
                    color: widget.isMe 
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLocationMessage() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.location_on,
          color: widget.isMe ? Colors.white : Colors.red,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            widget.message.locationName ?? 'موقع',
            style: TextStyle(
              color: widget.isMe ? Colors.white : Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmojiMessage() {
    return Text(
      widget.message.content,
      style: const TextStyle(fontSize: 32),
    );
  }

  Widget _buildMessageStatus() {
    IconData statusIcon;
    Color statusColor;

    switch (widget.message.status) {
      case MessageStatus.sending:
        statusIcon = Icons.access_time;
        statusColor = Colors.white.withValues(alpha: 0.7);
        break;
      case MessageStatus.sent:
        statusIcon = Icons.check;
        statusColor = Colors.white.withValues(alpha: 0.7);
        break;
      case MessageStatus.delivered:
        statusIcon = Icons.done_all;
        statusColor = Colors.white.withValues(alpha: 0.7);
        break;
      case MessageStatus.read:
        statusIcon = Icons.done_all;
        statusColor = Colors.blue[300]!;
        break;
      case MessageStatus.failed:
        statusIcon = Icons.error_outline;
        statusColor = Colors.red[300]!;
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: 14,
            color: statusColor,
          ),
        ],
      ),
    );
  }

  Widget _buildReactionsDisplay() {
    final reactions = widget.message.reactions;
    if (reactions.isEmpty) return const SizedBox.shrink();

    // تجميع التفاعلات المتشابهة
    final reactionCounts = <String, int>{};
    for (final emoji in reactions.values) {
      reactionCounts[emoji] = (reactionCounts[emoji] ?? 0) + 1;
    }

    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Wrap(
        spacing: 4,
        children: reactionCounts.entries.map((entry) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(entry.key, style: const TextStyle(fontSize: 14)),
                if (entry.value > 1) ...[
                  const SizedBox(width: 4),
                  Text(
                    '${entry.value}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTimestamp() {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        timeago.format(widget.message.timestamp, locale: 'ar'),
        style: TextStyle(
          fontSize: 11,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.onReply != null)
            _buildActionButton(
              icon: Icons.reply,
              label: 'رد',
              onTap: widget.onReply!,
            ),
          if (widget.onForward != null)
            _buildActionButton(
              icon: Icons.forward,
              label: 'إعادة توجيه',
              onTap: widget.onForward!,
            ),
          if (widget.onDelete != null)
            _buildActionButton(
              icon: Icons.delete,
              label: 'حذف',
              onTap: widget.onDelete!,
              color: Colors.red,
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    return GestureDetector(
      onTap: () {
        onTap();
        setState(() {
          _showActions = false;
        });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color ?? AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: Colors.white),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMessageActions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => MessageContextMenu(
        message: widget.message,
        isMe: widget.isMe,
        onReply: widget.onReply,
        onForward: widget.onForward,
        onShare: widget.onShare,
        onPin: widget.onPin,
        onEdit: widget.onEdit,
        onDelete: widget.onDelete,
        onReact: widget.onReact,
      ),
    );
  }

  Widget _buildImageWidget() {
    final mediaUrl = widget.message.mediaUrl!;

    // التحقق من نوع المسار (محلي أم شبكة)
    if (mediaUrl.startsWith('http')) {
      // صورة من الإنترنت
      return Image.network(
        mediaUrl,
        width: 200,
        height: 200,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 200,
            color: Colors.grey[300],
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.broken_image, size: 50, color: Colors.grey),
                Text('فشل في تحميل الصورة', style: TextStyle(color: Colors.grey)),
              ],
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: 200,
            height: 200,
            color: Colors.grey[200],
            child: const Center(child: CircularProgressIndicator()),
          );
        },
      );
    } else {
      // صورة محلية
      return Image.file(
        File(mediaUrl),
        width: 200,
        height: 200,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 200,
            color: Colors.grey[300],
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.broken_image, size: 50, color: Colors.grey),
                Text('فشل في تحميل الصورة', style: TextStyle(color: Colors.grey)),
              ],
            ),
          );
        },
      );
    }
  }

  Widget _buildVideoThumbnail() {
    final mediaUrl = widget.message.mediaUrl;

    if (mediaUrl != null) {
      if (mediaUrl.startsWith('http')) {
        // فيديو من الإنترنت - استخدام صورة مصغرة
        return widget.message.thumbnailUrl != null
            ? Image.network(
                widget.message.thumbnailUrl!,
                width: 200,
                height: 150,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildVideoPlaceholder();
                },
              )
            : _buildVideoPlaceholder();
      } else {
        // فيديو محلي - إنشاء صورة مصغرة
        return Container(
          width: 200,
          height: 150,
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.videocam, size: 40, color: Colors.white),
                SizedBox(height: 4),
                Text(
                  'فيديو',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
          ),
        );
      }
    } else {
      return _buildVideoPlaceholder();
    }
  }

  Widget _buildVideoPlaceholder() {
    return Container(
      width: 200,
      height: 150,
      color: Colors.grey[300],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.videocam, size: 50, color: Colors.grey),
            SizedBox(height: 4),
            Text('فيديو', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  void _playVideo() {
    if (widget.message.mediaUrl == null) return;

    // محاكاة تشغيل الفيديو
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تشغيل الفيديو: ${widget.message.fileName ?? 'فيديو'}'),
        action: SnackBarAction(
          label: 'إغلاق',
          onPressed: () {},
        ),
      ),
    );
  }

  void _showFullScreenImage() {
    if (widget.message.mediaUrl == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullScreenImageViewer(
          imageUrl: widget.message.mediaUrl!,
          heroTag: 'image_${widget.message.id}',
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
