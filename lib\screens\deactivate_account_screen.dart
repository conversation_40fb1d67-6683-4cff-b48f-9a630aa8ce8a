import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../widgets/facebook_form_field.dart';
import '../screens/facebook_login_screen.dart';

class DeactivateAccountScreen extends StatefulWidget {
  const DeactivateAccountScreen({super.key});

  @override
  State<DeactivateAccountScreen> createState() => _DeactivateAccountScreenState();
}

class _DeactivateAccountScreenState extends State<DeactivateAccountScreen> {
  final AuthService _authService = AuthService();
  final _passwordController = TextEditingController();
  
  String? _selectedReason;
  String? _selectedDuration;
  bool _isProcessing = false;
  String? _errorMessage;

  final List<String> _reasons = [
    'أحتاج استراحة من وسائل التواصل الاجتماعي',
    'أقضي وقتاً كثيراً على التطبيق',
    'لا أجد المحتوى مفيداً',
    'مشاكل في الخصوصية',
    'أريد إنشاء حساب جديد',
    'سبب آخر',
  ];

  final List<Map<String, dynamic>> _durations = [
    {'label': '7 أيام', 'days': 7},
    {'label': '30 يوماً', 'days': 30},
    {'label': '3 أشهر', 'days': 90},
    {'label': '6 أشهر', 'days': 180},
    {'label': 'سنة واحدة', 'days': 365},
    {'label': 'إلى أجل غير مسمى', 'days': -1},
  ];

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'إلغاء تنشيط الحساب',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // شرح إلغاء التنشيط
            _buildExplanationSection(),
            
            const SizedBox(height: 30),
            
            // سبب إلغاء التنشيط
            _buildReasonSection(),
            
            const SizedBox(height: 30),
            
            // مدة إلغاء التنشيط
            _buildDurationSection(),
            
            const SizedBox(height: 30),
            
            // تأكيد كلمة المرور
            _buildPasswordSection(),
            
            const SizedBox(height: 20),
            
            // رسالة الخطأ
            if (_errorMessage != null) _buildErrorMessage(),
            
            const SizedBox(height: 30),
            
            // أزرار التحكم
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildExplanationSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.orange[600]),
              const SizedBox(width: 8),
              Text(
                'ما هو إلغاء التنشيط؟',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '• سيتم إخفاء ملفك الشخصي ومنشوراتك\n'
            '• لن يتمكن الآخرون من العثور عليك أو مراسلتك\n'
            '• ستبقى بياناتك محفوظة\n'
            '• يمكنك العودة في أي وقت بتسجيل الدخول',
            style: TextStyle(
              fontSize: 14,
              color: Colors.orange[700],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReasonSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'سبب إلغاء التنشيط',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        ..._reasons.map((reason) => _buildReasonOption(reason)),
      ],
    );
  }

  Widget _buildReasonOption(String reason) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: RadioListTile<String>(
        value: reason,
        groupValue: _selectedReason,
        onChanged: (value) {
          setState(() {
            _selectedReason = value;
            _errorMessage = null;
          });
        },
        title: Text(
          reason,
          style: const TextStyle(fontSize: 14),
        ),
        activeColor: Colors.orange,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildDurationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مدة إلغاء التنشيط',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedDuration,
              hint: const Text('اختر المدة'),
              isExpanded: true,
              items: _durations.map((duration) {
                return DropdownMenuItem<String>(
                  value: duration['label'],
                  child: Text(duration['label']),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedDuration = value;
                  _errorMessage = null;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تأكيد كلمة المرور',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        FacebookFormField(
          controller: _passwordController,
          label: 'كلمة المرور',
          isPassword: true,
          prefixIcon: Icons.lock_outline,
          onChanged: (value) {
            setState(() {
              _errorMessage = null;
            });
          },
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر إلغاء التنشيط
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _canDeactivate() ? _deactivateAccount : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              disabledBackgroundColor: Colors.grey[300],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isProcessing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'إلغاء تنشيط الحساب',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // زر الإلغاء
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Colors.grey[400]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool _canDeactivate() {
    return _selectedReason != null &&
           _selectedDuration != null &&
           _passwordController.text.isNotEmpty &&
           !_isProcessing;
  }

  Future<void> _deactivateAccount() async {
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      // التحقق من كلمة المرور (محاكاة)
      await Future.delayed(const Duration(seconds: 2));
      
      // في التطبيق الحقيقي، هنا سيتم إرسال طلب إلغاء التنشيط للخادم
      
      // إلغاء تنشيط الحساب
      await _authService.logout();
      
      if (mounted) {
        // إظهار رسالة نجاح
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('تم إلغاء تنشيط الحساب'),
            content: Text(
              'تم إلغاء تنشيط حسابك لمدة $_selectedDuration.\n'
              'يمكنك العودة في أي وقت بتسجيل الدخول.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => const FacebookLoginScreen()),
                    (route) => false,
                  );
                },
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'كلمة المرور غير صحيحة';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }
}
