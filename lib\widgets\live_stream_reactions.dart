import 'package:flutter/material.dart';
import 'dart:math';
import '../models/live_stream.dart';

class LiveStreamReactions extends StatefulWidget {
  final List<LiveStreamReaction> reactions;

  const LiveStreamReactions({
    super.key,
    required this.reactions,
  });

  @override
  State<LiveStreamReactions> createState() => _LiveStreamReactionsState();
}

class _LiveStreamReactionsState extends State<LiveStreamReactions>
    with TickerProviderStateMixin {
  final List<AnimationController> _controllers = [];
  final List<Animation<double>> _animations = [];
  final List<Animation<Offset>> _slideAnimations = [];
  final Random _random = Random();

  @override
  void didUpdateWidget(LiveStreamReactions oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // إضافة أنيميشن للتفاعلات الجديدة
    if (widget.reactions.length > oldWidget.reactions.length) {
      final newReactionsCount = widget.reactions.length - oldWidget.reactions.length;
      for (int i = 0; i < newReactionsCount; i++) {
        _addReactionAnimation();
      }
    }
  }

  void _addReactionAnimation() {
    final controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    final fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
    ));

    final slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(
        (_random.nextDouble() - 0.5) * 2, // حركة أفقية عشوائية
        -3.0, // حركة عمودية للأعلى
      ),
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeOut,
    ));

    _controllers.add(controller);
    _animations.add(fadeAnimation);
    _slideAnimations.add(slideAnimation);

    controller.forward().then((_) {
      // إزالة الأنيميشن بعد الانتهاء
      if (mounted) {
        setState(() {
          final index = _controllers.indexOf(controller);
          if (index != -1) {
            _controllers[index].dispose();
            _controllers.removeAt(index);
            _animations.removeAt(index);
            _slideAnimations.removeAt(index);
          }
        });
      }
    });

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 100,
      height: 200,
      child: Stack(
        children: [
          // عرض آخر التفاعلات المتحركة
          ...List.generate(_controllers.length, (index) {
            if (index >= widget.reactions.length) return const SizedBox.shrink();
            
            final reaction = widget.reactions[widget.reactions.length - _controllers.length + index];
            
            return AnimatedBuilder(
              animation: _controllers[index],
              builder: (context, child) {
                return Transform.translate(
                  offset: _slideAnimations[index].value * 50,
                  child: Opacity(
                    opacity: _animations[index].value,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          reaction.emoji,
                          style: const TextStyle(fontSize: 20),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }),
          
          // عداد التفاعلات الثابت
          if (widget.reactions.isNotEmpty)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عرض أكثر التفاعلات شيوعاً
                    ..._getMostCommonReactions().take(3).map((reaction) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 2),
                        child: Text(
                          reaction.emoji,
                          style: const TextStyle(fontSize: 16),
                        ),
                      );
                    }),
                    
                    const SizedBox(width: 4),
                    
                    Text(
                      '${widget.reactions.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<LiveStreamReaction> _getMostCommonReactions() {
    final reactionCounts = <String, LiveStreamReaction>{};
    final counts = <String, int>{};

    for (final reaction in widget.reactions) {
      final emoji = reaction.emoji;
      counts[emoji] = (counts[emoji] ?? 0) + 1;
      reactionCounts[emoji] = reaction;
    }

    final sortedEntries = counts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedEntries
        .map((entry) => reactionCounts[entry.key]!)
        .toList();
  }
}
