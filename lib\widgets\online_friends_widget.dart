import 'package:flutter/material.dart';
import '../models/online_friend.dart';


class OnlineFriendsWidget extends StatefulWidget {
  final OnlineFriendsData friendsData;
  final Function(OnlineFriend)? onFriendTap;
  final Function(OnlineFriend)? onCallTap;
  final Function(OnlineFriend)? onVideoCallTap;

  const OnlineFriendsWidget({
    super.key,
    required this.friendsData,
    this.onFriendTap,
    this.onCallTap,
    this.onVideoCallTap,
  });

  @override
  State<OnlineFriendsWidget> createState() => _OnlineFriendsWidgetState();
}

class _OnlineFriendsWidgetState extends State<OnlineFriendsWidget> {
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس القسم
          _buildHeader(),
          
          // قائمة الأصدقاء
          if (_isExpanded) _buildFriendsList(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return InkWell(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // أيقونة الأصدقاء المتصلين
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.people,
                color: Colors.red,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // العنوان والعدد
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الأصدقاء المتصلين',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '${widget.friendsData.totalOnlineCount} متصل الآن',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            
            // مؤشر التوسع
            AnimatedRotation(
              turns: _isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 200),
              child: Icon(
                Icons.keyboard_arrow_down,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFriendsList() {
    return ConstrainedBox(
      constraints: const BoxConstraints(
        maxHeight: 400, // تحديد ارتفاع أقصى
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // الأصدقاء المتصلين
            if (widget.friendsData.onlineFriends.isNotEmpty) ...[
              _buildSectionTitle('متصل الآن', widget.friendsData.totalOnlineCount),
              ...widget.friendsData.onlineFriends.take(5).map((friend) => _buildFriendItem(friend)),
              if (widget.friendsData.onlineFriends.length > 5)
                _buildShowMoreButton('عرض المزيد من المتصلين'),
            ],

            // النشطين مؤخراً
            if (widget.friendsData.recentlyActive.isNotEmpty) ...[
              _buildSectionTitle('نشط مؤخراً', widget.friendsData.recentlyActive.length),
              ...widget.friendsData.recentlyActive.take(3).map((friend) => _buildFriendItem(friend)),
              if (widget.friendsData.recentlyActive.length > 3)
                _buildShowMoreButton('عرض المزيد من النشطين'),
            ],

            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, int count) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        '$title ($count)',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildShowMoreButton(String text) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          // يمكن إضافة الانتقال لشاشة الأصدقاء المتصلين الكاملة
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(text)),
          );
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.expand_more,
                color: Colors.grey[600],
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                text,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFriendItem(OnlineFriend friend) {
    return InkWell(
      onTap: () => _handleFriendTap(friend),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // صورة الملف الشخصي مع مؤشر الحالة
            Stack(
              children: [
                CircleAvatar(
                  radius: 22,
                  backgroundColor: Colors.grey[300],
                  backgroundImage: friend.profileImageUrl != null
                      ? NetworkImage(friend.profileImageUrl!)
                      : null,
                  child: friend.profileImageUrl == null
                      ? Icon(
                          Icons.person,
                          color: Colors.grey[600],
                          size: 24,
                        )
                      : null,
                ),
                
                // مؤشر الحالة
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 14,
                    height: 14,
                    decoration: BoxDecoration(
                      color: _getStatusColor(friend.status),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(width: 12),
            
            // معلومات الصديق
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    friend.name,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      if (friend.isTyping) ...[
                        SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                          ),
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          'يكتب...',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ] else ...[
                        Text(
                          friend.currentActivity ?? friend.statusText,
                          style: TextStyle(
                            fontSize: 12,
                            color: friend.isOnline ? Colors.red : Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            
            // أزرار الإجراءات
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // زر الاتصال الصوتي
                IconButton(
                  onPressed: () => _handleCallTap(friend),
                  icon: Icon(
                    Icons.phone,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
                
                // زر الاتصال المرئي
                IconButton(
                  onPressed: () => _handleVideoCallTap(friend),
                  icon: Icon(
                    Icons.videocam,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(OnlineStatus status) {
    switch (status) {
      case OnlineStatus.online:
        return Colors.red;
      case OnlineStatus.away:
        return Colors.orange;
      case OnlineStatus.busy:
        return Colors.red;
      case OnlineStatus.offline:
        return Colors.grey;
    }
  }

  void _handleFriendTap(OnlineFriend friend) {
    if (widget.onFriendTap != null) {
      widget.onFriendTap!(friend);
    } else {
      // إظهار رسالة أن الدردشة ستبدأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('بدء محادثة مع ${friend.name}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _handleCallTap(OnlineFriend friend) {
    if (widget.onCallTap != null) {
      widget.onCallTap!(friend);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('اتصال صوتي مع ${friend.name}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _handleVideoCallTap(OnlineFriend friend) {
    if (widget.onVideoCallTap != null) {
      widget.onVideoCallTap!(friend);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('اتصال مرئي مع ${friend.name}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
