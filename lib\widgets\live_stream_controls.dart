import 'package:flutter/material.dart';

class LiveStreamControls extends StatelessWidget {
  final bool isCameraEnabled;
  final bool isMicEnabled;
  final bool isFrontCamera;
  final VoidCallback onToggleCamera;
  final VoidCallback onToggleMicrophone;
  final VoidCallback onSwitchCamera;
  final VoidCallback onEndStream;

  const LiveStreamControls({
    super.key,
    required this.isCameraEnabled,
    required this.isMicEnabled,
    required this.isFrontCamera,
    required this.onToggleCamera,
    required this.onToggleMicrophone,
    required this.onSwitchCamera,
    required this.onEndStream,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر تبديل الكاميرا
          _buildControlButton(
            icon: isCameraEnabled ? Icons.videocam : Icons.videocam_off,
            isActive: isCameraEnabled,
            onTap: onToggleCamera,
            tooltip: isCameraEnabled ? 'إيقاف الكاميرا' : 'تشغيل الكاميرا',
          ),
          
          const SizedBox(width: 12),
          
          // زر تبديل المايكروفون
          _buildControlButton(
            icon: isMicEnabled ? Icons.mic : Icons.mic_off,
            isActive: isMicEnabled,
            onTap: onToggleMicrophone,
            tooltip: isMicEnabled ? 'كتم المايكروفون' : 'تشغيل المايكروفون',
          ),
          
          const SizedBox(width: 12),
          
          // زر تبديل الكاميرا الأمامية/الخلفية
          _buildControlButton(
            icon: Icons.flip_camera_ios,
            isActive: true,
            onTap: onSwitchCamera,
            tooltip: isFrontCamera ? 'الكاميرا الخلفية' : 'الكاميرا الأمامية',
          ),
          
          const SizedBox(width: 16),
          
          // زر إنهاء البث
          GestureDetector(
            onTap: onEndStream,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.stop,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 6),
                  Text(
                    'إنهاء البث',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isActive 
                ? Colors.white.withValues(alpha: 0.2)
                : Colors.red.withValues(alpha: 0.8),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }
}
