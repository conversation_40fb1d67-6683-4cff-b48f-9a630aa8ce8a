import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/saved_item.dart';
import '../models/post.dart';

class SavedItemsService {
  static const String _savedItemsKey = 'saved_items';
  
  // Stream controller للتحديث الفوري
  final StreamController<List<SavedItem>> _savedItemsController = 
      StreamController<List<SavedItem>>.broadcast();
  
  Stream<List<SavedItem>> get savedItemsStream => _savedItemsController.stream;
  
  // حفظ عنصر جديد
  Future<void> saveItem({
    required SavedItemType type,
    required String itemId,
    required String title,
    String? description,
    String? thumbnailUrl,
    String? authorName,
    String? authorId,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final savedItems = await getSavedItems();
      
      // التحقق من عدم وجود العنصر مسبقاً
      final existingIndex = savedItems.indexWhere(
        (item) => item.itemId == itemId && item.type == type,
      );
      
      if (existingIndex != -1) {
        // العنصر موجود مسبقاً، لا نحفظه مرة أخرى
        return;
      }
      
      final savedItem = SavedItem(
        id: 'saved_${DateTime.now().millisecondsSinceEpoch}',
        type: type,
        itemId: itemId,
        title: title,
        description: description,
        thumbnailUrl: thumbnailUrl,
        authorName: authorName,
        authorId: authorId,
        savedAt: DateTime.now(),
        metadata: metadata,
      );
      
      savedItems.insert(0, savedItem);
      await _saveSavedItems(savedItems);
      _savedItemsController.add(savedItems);
    } catch (e) {
      print('خطأ في حفظ العنصر: $e');
    }
  }
  
  // إزالة عنصر محفوظ
  Future<void> removeSavedItem(String savedItemId) async {
    try {
      final savedItems = await getSavedItems();
      savedItems.removeWhere((item) => item.id == savedItemId);
      await _saveSavedItems(savedItems);
      _savedItemsController.add(savedItems);
    } catch (e) {
      print('خطأ في إزالة العنصر المحفوظ: $e');
    }
  }
  
  // إزالة عنصر بناءً على نوعه ومعرفه
  Future<void> removeSavedItemByTypeAndId(SavedItemType type, String itemId) async {
    try {
      final savedItems = await getSavedItems();
      savedItems.removeWhere(
        (item) => item.type == type && item.itemId == itemId,
      );
      await _saveSavedItems(savedItems);
      _savedItemsController.add(savedItems);
    } catch (e) {
      print('خطأ في إزالة العنصر المحفوظ: $e');
    }
  }
  
  // التحقق من كون العنصر محفوظ
  Future<bool> isItemSaved(SavedItemType type, String itemId) async {
    try {
      final savedItems = await getSavedItems();
      return savedItems.any(
        (item) => item.type == type && item.itemId == itemId,
      );
    } catch (e) {
      print('خطأ في التحقق من العنصر المحفوظ: $e');
      return false;
    }
  }
  
  // الحصول على جميع العناصر المحفوظة
  Future<List<SavedItem>> getSavedItems() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedItemsJson = prefs.getStringList(_savedItemsKey) ?? [];
      
      return savedItemsJson
          .map((json) => SavedItem.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      print('خطأ في تحميل العناصر المحفوظة: $e');
      return [];
    }
  }
  
  // الحصول على العناصر المحفوظة بنوع معين
  Future<List<SavedItem>> getSavedItemsByType(SavedItemType type) async {
    try {
      final allItems = await getSavedItems();
      return allItems.where((item) => item.type == type).toList();
    } catch (e) {
      print('خطأ في تحميل العناصر المحفوظة بالنوع: $e');
      return [];
    }
  }
  
  // البحث في العناصر المحفوظة
  Future<List<SavedItem>> searchSavedItems(String query) async {
    try {
      final allItems = await getSavedItems();
      
      if (query.trim().isEmpty) {
        return allItems;
      }
      
      return allItems.where((item) {
        return item.title.toLowerCase().contains(query.toLowerCase()) ||
               (item.description?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
               (item.authorName?.toLowerCase().contains(query.toLowerCase()) ?? false);
      }).toList();
    } catch (e) {
      print('خطأ في البحث في العناصر المحفوظة: $e');
      return [];
    }
  }
  
  // مسح جميع العناصر المحفوظة
  Future<void> clearAllSavedItems() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_savedItemsKey);
      _savedItemsController.add([]);
    } catch (e) {
      print('خطأ في مسح العناصر المحفوظة: $e');
    }
  }
  
  // مسح العناصر المحفوظة بنوع معين
  Future<void> clearSavedItemsByType(SavedItemType type) async {
    try {
      final savedItems = await getSavedItems();
      savedItems.removeWhere((item) => item.type == type);
      await _saveSavedItems(savedItems);
      _savedItemsController.add(savedItems);
    } catch (e) {
      print('خطأ في مسح العناصر المحفوظة بالنوع: $e');
    }
  }
  
  // الحصول على إحصائيات العناصر المحفوظة
  Future<Map<SavedItemType, int>> getSavedItemsStats() async {
    try {
      final savedItems = await getSavedItems();
      final stats = <SavedItemType, int>{};
      
      for (final type in SavedItemType.values) {
        stats[type] = savedItems.where((item) => item.type == type).length;
      }
      
      return stats;
    } catch (e) {
      print('خطأ في تحميل إحصائيات العناصر المحفوظة: $e');
      return {};
    }
  }
  
  // حفظ منشور
  Future<void> savePost(Post post) async {
    await saveItem(
      type: SavedItemType.post,
      itemId: post.id,
      title: post.content.length > 50 
          ? '${post.content.substring(0, 50)}...' 
          : post.content,
      description: post.content,
      thumbnailUrl: post.media.isNotEmpty ? post.media.first.url : null,
      authorName: 'المؤلف', // يمكن تحسينه لاحقاً
      authorId: post.authorId,
      metadata: {
        'postType': post.type.name,
        'hasMedia': post.media.isNotEmpty,
        'mediaCount': post.media.length,
        'reactionsCount': post.reactions.length,
        'commentsCount': post.comments.length,
      },
    );
  }
  
  // حفظ صورة
  Future<void> saveImage({
    required String imageId,
    required String imageUrl,
    String? title,
    String? authorName,
    String? authorId,
  }) async {
    await saveItem(
      type: SavedItemType.image,
      itemId: imageId,
      title: title ?? 'صورة محفوظة',
      thumbnailUrl: imageUrl,
      authorName: authorName,
      authorId: authorId,
      metadata: {
        'imageUrl': imageUrl,
      },
    );
  }
  
  // حفظ فيديو
  Future<void> saveVideo({
    required String videoId,
    required String videoUrl,
    String? title,
    String? thumbnailUrl,
    String? authorName,
    String? authorId,
    int? duration,
  }) async {
    await saveItem(
      type: SavedItemType.video,
      itemId: videoId,
      title: title ?? 'فيديو محفوظ',
      thumbnailUrl: thumbnailUrl,
      authorName: authorName,
      authorId: authorId,
      metadata: {
        'videoUrl': videoUrl,
        'duration': duration,
      },
    );
  }
  
  // دالة مساعدة لحفظ البيانات
  Future<void> _saveSavedItems(List<SavedItem> savedItems) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedItemsJson = savedItems.map((item) => jsonEncode(item.toJson())).toList();
      await prefs.setStringList(_savedItemsKey, savedItemsJson);
    } catch (e) {
      print('خطأ في حفظ العناصر المحفوظة: $e');
    }
  }
  
  void dispose() {
    _savedItemsController.close();
  }
}
