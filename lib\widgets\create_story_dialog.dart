import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/social_provider.dart';
import '../models/story.dart';
import '../theme/app_theme.dart';

class CreateStoryDialog extends StatefulWidget {
  const CreateStoryDialog({super.key});

  @override
  State<CreateStoryDialog> createState() => _CreateStoryDialogState();
}

class _CreateStoryDialogState extends State<CreateStoryDialog> {
  final TextEditingController _contentController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  StoryType _selectedType = StoryType.text;
  StoryBackground _selectedBackground = StoryBackground.gradient1;
  bool _isCreating = false;
  File? _selectedMedia;
  String? _mediaPath;

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // شريط العنوان
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
                const Expanded(
                  child: Text(
                    'إنشاء قصة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                ElevatedButton(
                  onPressed: _isCreating ? null : _createStory,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: _isCreating
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('نشر'),
                ),
              ],
            ),
          ),

          // معاينة القصة
          Expanded(
            flex: 3,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: _getBackgroundGradient(_selectedBackground),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 2,
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // محتوى القصة
                  if (_selectedType == StoryType.text)
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: TextField(
                          controller: _contentController,
                          maxLines: null,
                          textAlign: TextAlign.center,
                          decoration: const InputDecoration(
                            hintText: 'اكتب شيئاً...',
                            hintStyle: TextStyle(
                              color: Colors.white70,
                              fontSize: 24,
                            ),
                            border: InputBorder.none,
                          ),
                          style: const TextStyle(
                            color: Colors.black,  // تغيير لون النص إلى أسود
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                offset: Offset(1, 1),
                                blurRadius: 2,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                  // عرض الصورة المختارة
                  if (_selectedType == StoryType.image && _selectedMedia != null)
                    Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        image: DecorationImage(
                          image: FileImage(_selectedMedia!),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.3),
                            ],
                          ),
                        ),
                        child: _contentController.text.isNotEmpty
                            ? Align(
                                alignment: Alignment.bottomCenter,
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Text(
                                    _contentController.text,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      shadows: [
                                        Shadow(
                                          offset: Offset(0, 1),
                                          blurRadius: 2,
                                          color: Colors.black54,
                                        ),
                                      ],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              )
                            : null,
                      ),
                    ),

                  // عرض الفيديو المختار
                  if (_selectedType == StoryType.video && _selectedMedia != null)
                    Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: Colors.black,
                      ),
                      child: Stack(
                        children: [
                          Center(
                            child: Icon(
                              Icons.play_circle_filled,
                              size: 64,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                          if (_contentController.text.isNotEmpty)
                            Align(
                              alignment: Alignment.bottomCenter,
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Text(
                                  _contentController.text,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    shadows: [
                                      Shadow(
                                        offset: Offset(0, 1),
                                        blurRadius: 2,
                                        color: Colors.black54,
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),

                  // رسالة اختيار الوسائط
                  if ((_selectedType == StoryType.image || _selectedType == StoryType.video) && _selectedMedia == null)
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _selectedType == StoryType.image ? Icons.photo_library : Icons.videocam,
                            size: 64,
                            color: Colors.white.withValues(alpha: 0.7),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _selectedType == StoryType.image ? 'اختر صورة' : 'اختر فيديو',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: _pickMedia,
                            icon: Icon(_selectedType == StoryType.image ? Icons.photo : Icons.video_library),
                            label: Text(_selectedType == StoryType.image ? 'اختر صورة' : 'اختر فيديو'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white.withValues(alpha: 0.2),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  // معلومات المستخدم
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 16,
                          backgroundColor: Colors.white.withOpacity(0.3),
                          child: const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'قصتك',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // خيارات التخصيص
          Expanded(
            flex: 2,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // خيارات النوع
                  const Text(
                    'نوع القصة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _TypeOption(
                        icon: Icons.text_fields,
                        label: 'نص',
                        isSelected: _selectedType == StoryType.text,
                        onTap: () {
                          setState(() {
                            _selectedType = StoryType.text;
                          });
                        },
                      ),
                      const SizedBox(width: 16),
                      _TypeOption(
                        icon: Icons.photo_library,
                        label: 'صورة',
                        isSelected: _selectedType == StoryType.image,
                        onTap: () {
                          setState(() {
                            _selectedType = StoryType.image;
                            _selectedMedia = null;
                            _mediaPath = null;
                          });
                        },
                      ),
                      const SizedBox(width: 16),
                      _TypeOption(
                        icon: Icons.videocam,
                        label: 'فيديو',
                        isSelected: _selectedType == StoryType.video,
                        onTap: () {
                          setState(() {
                            _selectedType = StoryType.video;
                            _selectedMedia = null;
                            _mediaPath = null;
                          });
                        },
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // خيارات الخلفية (للنص فقط)
                  if (_selectedType == StoryType.text) ...[
                    const Text(
                      'خلفية القصة:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 60,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: StoryBackground.values.length - 1, // استثناء none
                        itemBuilder: (context, index) {
                          final background = StoryBackground.values[index + 1];
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedBackground = background;
                              });
                            },
                            child: Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.only(left: 8),
                              decoration: BoxDecoration(
                                gradient: _getBackgroundGradient(background),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: _selectedBackground == background
                                      ? Colors.white
                                      : Colors.transparent,
                                  width: 3,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],

                  // حقل النص للصور والفيديوهات
                  if (_selectedType != StoryType.text) ...[
                    const SizedBox(height: 24),
                    const Text(
                      'إضافة نص (اختياري):',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _contentController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: 'اكتب تعليقاً على ${_selectedType == StoryType.image ? 'الصورة' : 'الفيديو'}...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  LinearGradient _getBackgroundGradient(StoryBackground background) {
    switch (background) {
      case StoryBackground.none:
        return const LinearGradient(colors: [Colors.grey, Colors.grey]);
      case StoryBackground.gradient1:
        return const LinearGradient(
          colors: [Colors.blue, Colors.purple],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient2:
        return const LinearGradient(
          colors: [Colors.pink, Colors.orange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient3:
        return const LinearGradient(
          colors: [Colors.green, Colors.teal],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient4:
        return const LinearGradient(
          colors: [Colors.orange, Colors.red],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient5:
        return const LinearGradient(
          colors: [Colors.purple, Colors.indigo],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient6:
        return const LinearGradient(
          colors: [Colors.teal, Colors.blue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  Future<void> _pickMedia() async {
    try {
      XFile? pickedFile;

      if (_selectedType == StoryType.image) {
        pickedFile = await _picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1080,
          maxHeight: 1920,
          imageQuality: 85,
        );
      } else if (_selectedType == StoryType.video) {
        pickedFile = await _picker.pickVideo(
          source: ImageSource.gallery,
          maxDuration: const Duration(seconds: 30), // حد أقصى 30 ثانية مثل Instagram
        );
      }

      if (pickedFile != null) {
        setState(() {
          _selectedMedia = File(pickedFile!.path);
          _mediaPath = pickedFile.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في اختيار الملف: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _createStory() async {
    // التحقق من المحتوى
    if (_selectedType == StoryType.text && _contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة محتوى القصة')),
      );
      return;
    }

    if ((_selectedType == StoryType.image || _selectedType == StoryType.video) && _selectedMedia == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى اختيار ${_selectedType == StoryType.image ? 'صورة' : 'فيديو'}')),
      );
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      await Provider.of<SocialProvider>(context, listen: false).createStory(
        content: _contentController.text.trim(),
        type: _selectedType,
        background: _selectedBackground,
        mediaUrl: _mediaPath,
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم نشر ${_getStoryTypeText()} بنجاح! 🎉'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في نشر القصة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  String _getStoryTypeText() {
    switch (_selectedType) {
      case StoryType.text:
        return 'القصة النصية';
      case StoryType.image:
        return 'قصة الصورة';
      case StoryType.video:
        return 'قصة الفيديو';
    }
  }
}

class _TypeOption extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _TypeOption({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey[600],
              size: 20,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[600],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
