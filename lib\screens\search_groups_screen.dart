import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../providers/social_provider.dart';
import 'group_detail_screen.dart';

class SearchGroupsScreen extends StatefulWidget {
  const SearchGroupsScreen({super.key});

  @override
  State<SearchGroupsScreen> createState() => _SearchGroupsScreenState();
}

class _SearchGroupsScreenState extends State<SearchGroupsScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Group> _searchResults = [];
  bool _isSearching = false;
  String _selectedCategory = 'الكل';
  GroupPrivacy? _selectedPrivacy;

  final List<String> _categories = [
    'الكل',
    'التكنولوجيا',
    'الرياضة',
    'الطبخ',
    'السفر',
    'التعليم',
    'الفن',
    'الموسيقى',
    'الألعاب',
    'الأعمال',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'البحث عن المجموعات',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          _buildSearchSection(),
          
          // النتائج
          Expanded(
            child: _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث عن المجموعات...',
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, color: Colors.grey),
                      onPressed: () {
                        _searchController.clear();
                        _clearSearch();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[100],
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
            onChanged: _performSearch,
            onSubmitted: _performSearch,
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر البحث
          Row(
            children: [
              // فلتر الفئة
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'الفئة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: _categories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                    _performSearch(_searchController.text);
                  },
                ),
              ),
              
              const SizedBox(width: 12),
              
              // فلتر الخصوصية
              Expanded(
                child: DropdownButtonFormField<GroupPrivacy?>(
                  value: _selectedPrivacy,
                  decoration: InputDecoration(
                    labelText: 'النوع',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('الكل'),
                    ),
                    const DropdownMenuItem(
                      value: GroupPrivacy.public,
                      child: Text('عامة'),
                    ),
                    const DropdownMenuItem(
                      value: GroupPrivacy.private,
                      child: Text('خاصة'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedPrivacy = value;
                    });
                    _performSearch(_searchController.text);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_searchController.text.isEmpty) {
      return _buildPopularGroups();
    }

    if (_searchResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'جرب البحث بكلمات مختلفة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final group = _searchResults[index];
        return _GroupSearchCard(
          group: group,
          onTap: () => _openGroup(group),
        );
      },
    );
  }

  Widget _buildPopularGroups() {
    return Consumer<SocialProvider>(
      builder: (context, socialProvider, child) {
        final popularGroups = socialProvider.groups.take(10).toList();
        
        if (popularGroups.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.groups_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد مجموعات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'ابدأ البحث للعثور على مجموعات',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'المجموعات الشائعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: popularGroups.length,
                itemBuilder: (context, index) {
                  final group = popularGroups[index];
                  return _GroupSearchCard(
                    group: group,
                    onTap: () => _openGroup(group),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  void _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // محاكاة البحث
    await Future.delayed(const Duration(milliseconds: 500));

    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    final allGroups = socialProvider.groups;

    final results = allGroups.where((group) {
      // فلتر النص
      final matchesText = group.name.toLowerCase().contains(query.toLowerCase()) ||
                         group.description.toLowerCase().contains(query.toLowerCase());

      // فلتر الفئة
      final matchesCategory = _selectedCategory == 'الكل' ||
                             group.tags.contains(_selectedCategory.toLowerCase());

      // فلتر الخصوصية
      final matchesPrivacy = _selectedPrivacy == null ||
                            group.privacy == _selectedPrivacy;

      return matchesText && matchesCategory && matchesPrivacy;
    }).toList();

    setState(() {
      _searchResults = results;
      _isSearching = false;
    });
  }

  void _clearSearch() {
    setState(() {
      _searchResults.clear();
      _isSearching = false;
    });
  }

  void _openGroup(Group group) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupDetailScreen(group: group),
      ),
    );
  }
}

class _GroupSearchCard extends StatelessWidget {
  final Group group;
  final VoidCallback onTap;

  const _GroupSearchCard({
    required this.group,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة المجموعة
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.groups,
                  color: Colors.blue,
                  size: 30,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // معلومات المجموعة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            group.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _PrivacyIcon(privacy: group.privacy),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      group.description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${group.memberCount} عضو',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              
              // زر الانضمام
              if (!group.isUserMember('current_user'))
                ElevatedButton(
                  onPressed: () => _joinGroup(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(80, 36),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18),
                    ),
                  ),
                  child: const Text(
                    'انضمام',
                    style: TextStyle(fontSize: 12),
                  ),
                )
              else
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(color: Colors.green),
                  ),
                  child: const Text(
                    'عضو',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _joinGroup(BuildContext context) {
    Provider.of<SocialProvider>(context, listen: false).joinGroup(group.id);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم الانضمام لمجموعة ${group.name}')),
    );
  }
}

class _PrivacyIcon extends StatelessWidget {
  final GroupPrivacy privacy;

  const _PrivacyIcon({required this.privacy});

  @override
  Widget build(BuildContext context) {
    IconData icon;
    Color color;

    switch (privacy) {
      case GroupPrivacy.public:
        icon = Icons.public;
        color = Colors.green;
        break;
      case GroupPrivacy.private:
        icon = Icons.lock_outline;
        color = Colors.orange;
        break;
      case GroupPrivacy.secret:
        icon = Icons.lock;
        color = Colors.red;
        break;
    }

    return Icon(
      icon,
      color: color,
      size: 16,
    );
  }
}
