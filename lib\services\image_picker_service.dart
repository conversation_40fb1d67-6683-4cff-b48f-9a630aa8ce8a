import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerService {
  static final ImagePicker _picker = ImagePicker();

  // اختيار صورة من الاستوديو أو الكاميرا
  static Future<File?> pickImage({
    required BuildContext context,
    required String title,
  }) async {
    return await showModalBottomSheet<File?>(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // العنوان
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // خيارات الاختيار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // الكاميرا
                _buildPickerOption(
                  context: context,
                  icon: Icons.camera_alt,
                  label: 'الكاميرا',
                  onTap: () async {
                    Navigator.pop(context);
                    final image = await _picker.pickImage(
                      source: ImageSource.camera,
                      imageQuality: 80,
                      maxWidth: 1024,
                      maxHeight: 1024,
                    );
                    if (image != null) {
                      Navigator.pop(context, File(image.path));
                    }
                  },
                ),
                
                // الاستوديو
                _buildPickerOption(
                  context: context,
                  icon: Icons.photo_library,
                  label: 'الاستوديو',
                  onTap: () async {
                    Navigator.pop(context);
                    final image = await _picker.pickImage(
                      source: ImageSource.gallery,
                      imageQuality: 80,
                      maxWidth: 1024,
                      maxHeight: 1024,
                    );
                    if (image != null) {
                      Navigator.pop(context, File(image.path));
                    }
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // زر الإلغاء
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildPickerOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: Colors.blue,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // حفظ الصورة وإرجاع URL وهمي
  static Future<String> saveImage(File imageFile, String type) async {
    // محاكاة رفع الصورة
    await Future.delayed(const Duration(seconds: 1));
    
    // إرجاع URL وهمي
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'https://example.com/images/${type}_$timestamp.jpg';
  }

  // اختيار صورة الملف الشخصي
  static Future<String?> pickProfileImage(BuildContext context) async {
    final imageFile = await pickImage(
      context: context,
      title: 'تغيير صورة الملف الشخصي',
    );
    
    if (imageFile != null) {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
      
      try {
        // حفظ الصورة
        final imageUrl = await saveImage(imageFile, 'profile');
        
        // إغلاق مؤشر التحميل
        Navigator.pop(context);
        
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث صورة الملف الشخصي بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        
        return imageUrl;
      } catch (e) {
        // إغلاق مؤشر التحميل
        Navigator.pop(context);
        
        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تحديث الصورة'),
            backgroundColor: Colors.red,
          ),
        );
        
        return null;
      }
    }
    
    return null;
  }

  // اختيار صورة الغلاف
  static Future<String?> pickCoverImage(BuildContext context) async {
    final imageFile = await pickImage(
      context: context,
      title: 'تغيير صورة الغلاف',
    );
    
    if (imageFile != null) {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
      
      try {
        // حفظ الصورة
        final imageUrl = await saveImage(imageFile, 'cover');
        
        // إغلاق مؤشر التحميل
        Navigator.pop(context);
        
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث صورة الغلاف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        
        return imageUrl;
      } catch (e) {
        // إغلاق مؤشر التحميل
        Navigator.pop(context);
        
        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تحديث الصورة'),
            backgroundColor: Colors.red,
          ),
        );
        
        return null;
      }
    }
    
    return null;
  }
}
