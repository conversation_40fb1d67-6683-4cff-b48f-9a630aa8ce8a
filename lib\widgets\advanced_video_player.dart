import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../providers/social_provider.dart';
import '../widgets/comments_section.dart';
import '../widgets/reaction_picker.dart';
import '../screens/comments_screen.dart';

class AdvancedVideoPlayer extends StatefulWidget {
  final Post post;
  final PostMedia videoMedia;

  const AdvancedVideoPlayer({
    super.key,
    required this.post,
    required this.videoMedia,
  });

  @override
  State<AdvancedVideoPlayer> createState() => _AdvancedVideoPlayerState();
}

class _AdvancedVideoPlayerState extends State<AdvancedVideoPlayer> {
  bool _isPlaying = false;
  bool _isMuted = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  double _currentPosition = 0.0;
  final double _totalDuration = 100.0;
  double _playbackSpeed = 1.0;
  String _videoQuality = 'HD';

  @override
  Widget build(BuildContext context) {
    return Consumer<SocialProvider>(
      builder: (context, socialProvider, child) {
        // البحث عن المنشور المحدث
        final updatedPost = socialProvider.posts.firstWhere(
          (post) => post.id == widget.post.id,
          orElse: () => widget.post,
        );

        return Container(
          width: double.infinity,
          height: _isFullscreen ? MediaQuery.of(context).size.height : 250,
          margin: _isFullscreen ? EdgeInsets.zero : const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: _isFullscreen ? BorderRadius.zero : BorderRadius.circular(8),
          ),
          child: Stack(
            children: [
              // الفيديو
              _buildVideoContent(),

              // أدوات التحكم
              if (_showControls) _buildVideoControls(),

              // أزرار التفاعل (فقط في الوضع العادي)
              if (!_isFullscreen) _buildInteractionButtons(updatedPost),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVideoContent() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
        // إخفاء الأدوات بعد 3 ثوان
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _showControls = false;
            });
          }
        });
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: _isFullscreen ? BorderRadius.zero : BorderRadius.circular(8),
        ),
        child: Stack(
          children: [
            // خلفية الفيديو
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.blue.withValues(alpha: 0.3),
                    Colors.purple.withValues(alpha: 0.3),
                    Colors.red.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: _isFullscreen ? BorderRadius.zero : BorderRadius.circular(8),
              ),
            ),

            // محاكاة محتوى الفيديو
            if (_isPlaying) ...[
              // تأثير متحرك عند التشغيل
              AnimatedContainer(
                duration: const Duration(seconds: 2),
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.blue.withValues(alpha: 0.4),
                      Colors.green.withValues(alpha: 0.4),
                      Colors.orange.withValues(alpha: 0.4),
                    ],
                  ),
                  borderRadius: _isFullscreen ? BorderRadius.zero : BorderRadius.circular(8),
                ),
              ),

              // نص يحاكي محتوى الفيديو
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.play_circle_filled,
                      size: 80,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'الفيديو قيد التشغيل...',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.post.content,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // صورة مصغرة عند الإيقاف
              if (widget.videoMedia.thumbnail != null)
                ClipRRect(
                  borderRadius: _isFullscreen ? BorderRadius.zero : BorderRadius.circular(8),
                  child: Image.network(
                    widget.videoMedia.thumbnail!,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.grey[800]!,
                              Colors.grey[900]!,
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.video_library,
                            size: 60,
                            color: Colors.white54,
                          ),
                        ),
                      );
                    },
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVideoControls() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.3),
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: _isFullscreen ? BorderRadius.zero : BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // الشريط العلوي
          _buildTopControls(),
          
          // المنطقة الوسطى مع زر التشغيل
          Expanded(child: _buildCenterControls()),
          
          // الشريط السفلي
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // معلومات الفيديو
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getAuthorName(widget.post.authorId),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  _formatDuration(widget.videoMedia.duration ?? const Duration(seconds: 30)),
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // أزرار التحكم العلوية
          Row(
            children: [
              // زر كتم الصوت
              IconButton(
                onPressed: () {
                  setState(() {
                    _isMuted = !_isMuted;
                  });
                },
                icon: Icon(
                  _isMuted ? Icons.volume_off : Icons.volume_up,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              
              // زر الشاشة الكاملة
              IconButton(
                onPressed: _toggleFullscreen,
                icon: Icon(
                  _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              
              // زر الخيارات
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.white),
                onSelected: _handleVideoMenuAction,
                itemBuilder: (context) => _buildVideoMenuItems(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // زر الترجيع 10 ثوان
          IconButton(
            onPressed: () {
              setState(() {
                _currentPosition = (_currentPosition - 10).clamp(0, _totalDuration);
              });
            },
            icon: const Icon(
              Icons.replay_10,
              color: Colors.white,
              size: 32,
            ),
          ),
          
          // زر التشغيل/الإيقاف
          GestureDetector(
            onTap: () {
              setState(() {
                _isPlaying = !_isPlaying;
              });
            },
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
          
          // زر التقديم 10 ثوان
          IconButton(
            onPressed: () {
              setState(() {
                _currentPosition = (_currentPosition + 10).clamp(0, _totalDuration);
              });
            },
            icon: const Icon(
              Icons.forward_10,
              color: Colors.white,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          // شريط التقدم
          Row(
            children: [
              Text(
                _formatTime(_currentPosition),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
              Expanded(
                child: Slider(
                  value: _currentPosition,
                  max: _totalDuration,
                  onChanged: (value) {
                    setState(() {
                      _currentPosition = value;
                    });
                  },
                  activeColor: Colors.red,
                  inactiveColor: Colors.white30,
                ),
              ),
              Text(
                _formatTime(_totalDuration),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ),
          
          // معلومات إضافية
          Row(
            children: [
              Text(
                'السرعة: ${_playbackSpeed}x',
                style: const TextStyle(color: Colors.white70, fontSize: 10),
              ),
              const SizedBox(width: 16),
              Text(
                'الجودة: $_videoQuality',
                style: const TextStyle(color: Colors.white70, fontSize: 10),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionButtons(Post currentPost) {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Column(
        children: [
          // زر الإعجاب
          GestureDetector(
            onLongPress: _showReactionsOverlay,
            onTap: _toggleLike,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.thumb_up,
                color: _isLiked(currentPost) ? Colors.blue : Colors.white,
                size: 24,
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // زر التعليقات
          GestureDetector(
            onTap: _showComments,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.comment,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // زر المشاركة
          GestureDetector(
            onTap: _showShareOptions,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.share,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دوال المساعدة والتفاعل
  String _getAuthorName(String authorId) {
    switch (authorId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final remainingSeconds = (seconds % 60).floor();
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  bool _isLiked(Post? post) {
    final currentPost = post ?? widget.post;
    return currentPost.reactions.any((reaction) =>
        reaction.userId == 'current_user');
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    if (_isFullscreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  void _toggleLike() async {
    try {
      final socialProvider = Provider.of<SocialProvider>(context, listen: false);

      // تطبيق التفاعل في الـ provider
      await socialProvider.likePost(widget.post.id);

      // رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isLiked(null) ? 'تم الإعجاب! 👍' : 'تم إلغاء الإعجاب! 👍'),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التفاعل: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showReactionsOverlay() {
    final renderBox = context.findRenderObject() as RenderBox;
    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // حساب الموضع المناسب لإظهار التفاعلات
    double left = buttonPosition.dx - 150;
    double bottom = screenHeight - buttonPosition.dy + 60;

    // التأكد من عدم الخروج من حدود الشاشة
    if (left < 10) left = 10;
    if (left + 350 > screenWidth) left = screenWidth - 360;

    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: left,
        bottom: bottom,
        child: Material(
          color: Colors.transparent,
          child: ReactionPicker(
            onClose: () {
              overlayEntry.remove();
            },
            onReactionSelected: (reactionType) async {
              try {
                final socialProvider = Provider.of<SocialProvider>(context, listen: false);

                // تطبيق التفاعل في الـ provider
                await socialProvider.likePost(widget.post.id);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تفاعل: ${_getReactionText(reactionType)} 🎉'),
                      backgroundColor: Colors.blue,
                      duration: const Duration(seconds: 1),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في التفاعل: $e'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              }
              // إزالة الـ overlay
              overlayEntry.remove();
            },
          ),
        ),
      ),
    );

    Overlay.of(context).insert(overlayEntry);

    // إخفاء المنتقي بعد 4 ثوان
    Future.delayed(const Duration(seconds: 4), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  String _getReactionText(dynamic reactionType) {
    return 'تفاعل 🎉';
  }



  void _showComments() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommentsScreen(
          postId: widget.post.id,
          postTitle: 'فيديو ${_getAuthorName(widget.post.authorId)}',
        ),
      ),
    );
  }

  void _showShareOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'مشاركة الفيديو',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // المشاركة الخارجية
            const Text(
              'مشاركة خارجية',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 12),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShareButton(
                  icon: Icons.facebook,
                  label: 'Facebook',
                  color: Colors.blue[800]!,
                  onTap: () => _shareToExternal('Facebook'),
                ),
                _buildShareButton(
                  icon: Icons.chat,
                  label: 'WhatsApp',
                  color: Colors.green[600]!,
                  onTap: () => _shareToExternal('WhatsApp'),
                ),
                _buildShareButton(
                  icon: Icons.alternate_email,
                  label: 'Twitter',
                  color: Colors.blue[400]!,
                  onTap: () => _shareToExternal('Twitter'),
                ),
                _buildShareButton(
                  icon: Icons.camera_alt,
                  label: 'Instagram',
                  color: Colors.purple[400]!,
                  onTap: () => _shareToExternal('Instagram'),
                ),
              ],
            ),

            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 12),

            // خيارات المشاركة الداخلية
            ListTile(
              leading: const Icon(Icons.public, color: Colors.blue),
              title: const Text('مشاركة عامة'),
              subtitle: const Text('مشاركة كمنشور في التطبيق'),
              onTap: () => _shareAsPost(),
            ),
            ListTile(
              leading: const Icon(Icons.message, color: Colors.green),
              title: const Text('إرسال في رسالة'),
              subtitle: const Text('إرسال لصديق في الدردشة'),
              onTap: () => _sendInMessage(),
            ),
            ListTile(
              leading: const Icon(Icons.group, color: Colors.orange),
              title: const Text('مشاركة في مجموعة'),
              subtitle: const Text('مشاركة في إحدى مجموعاتك'),
              onTap: () => _shareInGroup(),
            ),
            ListTile(
              leading: const Icon(Icons.link, color: Colors.grey),
              title: const Text('نسخ الرابط'),
              subtitle: const Text('نسخ رابط الفيديو للمشاركة'),
              onTap: () => _copyVideoLink(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _shareToExternal(String platform) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مشاركة الفيديو عبر $platform! 🔗'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareAsPost() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مشاركة الفيديو كمنشور عام! 📢'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _sendInMessage() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال الفيديو في رسالة! 💬'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareInGroup() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مشاركة الفيديو في المجموعة! 👥'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _copyVideoLink() async {
    final videoLink = 'https://arzawo.app/video/${widget.post.id}';
    await Clipboard.setData(ClipboardData(text: videoLink));

    if (mounted) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ رابط الفيديو! 🔗'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _handleVideoMenuAction(String action) {
    switch (action) {
      case 'speed':
        _showSpeedOptions();
        break;
      case 'quality':
        _showQualityOptions();
        break;
      case 'download':
        _downloadVideo();
        break;
      case 'share':
        _showShareOptions();
        break;
      case 'report':
        _reportVideo();
        break;
      case 'delete':
        _deleteVideo();
        break;
    }
  }

  List<PopupMenuEntry<String>> _buildVideoMenuItems() {
    final isOwner = widget.post.authorId == 'current_user';

    return [
      const PopupMenuItem(
        value: 'speed',
        child: Row(
          children: [
            Icon(Icons.speed, color: Colors.blue),
            SizedBox(width: 12),
            Text('سرعة التشغيل'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'quality',
        child: Row(
          children: [
            Icon(Icons.high_quality, color: Colors.green),
            SizedBox(width: 12),
            Text('جودة الفيديو'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'download',
        child: Row(
          children: [
            Icon(Icons.download, color: Colors.orange),
            SizedBox(width: 12),
            Text('تحميل الفيديو'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'share',
        child: Row(
          children: [
            Icon(Icons.share, color: Colors.purple),
            SizedBox(width: 12),
            Text('مشاركة'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'report',
        child: Row(
          children: [
            Icon(Icons.report, color: Colors.red),
            SizedBox(width: 12),
            Text('الإبلاغ'),
          ],
        ),
      ),
      if (isOwner)
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 12),
              Text('حذف الفيديو'),
            ],
          ),
        ),
    ];
  }

  void _showSpeedOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سرعة التشغيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSpeedOption(0.5),
            _buildSpeedOption(0.75),
            _buildSpeedOption(1.0),
            _buildSpeedOption(1.25),
            _buildSpeedOption(1.5),
            _buildSpeedOption(2.0),
          ],
        ),
      ),
    );
  }

  Widget _buildSpeedOption(double speed) {
    return ListTile(
      title: Text('${speed}x'),
      trailing: _playbackSpeed == speed ? const Icon(Icons.check, color: Colors.blue) : null,
      onTap: () {
        setState(() {
          _playbackSpeed = speed;
        });
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تغيير السرعة إلى ${speed}x')),
        );
      },
    );
  }

  void _showQualityOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('جودة الفيديو'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildQualityOption('4K'),
            _buildQualityOption('HD'),
            _buildQualityOption('SD'),
            _buildQualityOption('Auto'),
          ],
        ),
      ),
    );
  }

  Widget _buildQualityOption(String quality) {
    return ListTile(
      title: Text(quality),
      trailing: _videoQuality == quality ? const Icon(Icons.check, color: Colors.blue) : null,
      onTap: () {
        setState(() {
          _videoQuality = quality;
        });
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تغيير الجودة إلى $quality')),
        );
      },
    );
  }

  void _downloadVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تحميل الفيديو... ⬇️'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _reportVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم الإبلاغ عن الفيديو! 🚨'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _deleteVideo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الفيديو'),
        content: const Text('هل أنت متأكد من حذف هذا الفيديو؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف الفيديو! 🗑️'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}