import 'package:flutter/material.dart';

enum ReactionType {
  like,
  love,
  haha,
  wow,
  sad,
  angry,
}

class ReactionData {
  final ReactionType type;
  final String name;
  final String emoji;
  final Color color;
  final IconData icon;

  const ReactionData({
    required this.type,
    required this.name,
    required this.emoji,
    required this.color,
    required this.icon,
  });

  static const Map<ReactionType, ReactionData> reactions = {
    ReactionType.like: ReactionData(
      type: ReactionType.like,
      name: 'إعجاب',
      emoji: '👍🏻',
      color: Color(0xFF0A66C2), // أزرق LinkedIn
      icon: Icons.thumb_up_alt,
    ),
    ReactionType.love: ReactionData(
      type: ReactionType.love,
      name: 'أحببته',
      emoji: '❤️',
      color: Color(0xFFDD5143), // أحمر LinkedIn
      icon: Icons.favorite,
    ),
    ReactionType.haha: ReactionData(
      type: ReactionType.haha,
      name: 'مضح<PERSON>',
      emoji: '😂',
      color: Color(0xFFF5C75D), // أصفر LinkedIn
      icon: Icons.emoji_emotions,
    ),
    ReactionType.wow: ReactionData(
      type: ReactionType.wow,
      name: 'رائع',
      emoji: '😮',
      color: Color(0xFFD32F2F), // أحمر بدلاً من الأخضر
      icon: Icons.lightbulb_outline,
    ),
    ReactionType.sad: ReactionData(
      type: ReactionType.sad,
      name: 'محزن',
      emoji: '😢',
      color: Color(0xFF70B5F9), // أزرق فاتح LinkedIn
      icon: Icons.sentiment_dissatisfied,
    ),
    ReactionType.angry: ReactionData(
      type: ReactionType.angry,
      name: 'غاضب',
      emoji: '😠',
      color: Color(0xFFE07C24), // برتقالي LinkedIn
      icon: Icons.sentiment_very_dissatisfied,
    ),
  };

  static ReactionData getReaction(ReactionType type) {
    return reactions[type]!;
  }

  static List<ReactionData> getAllReactions() {
    return reactions.values.toList();
  }

  static ReactionData getReactionByName(String name) {
    return reactions.values.firstWhere(
      (reaction) => reaction.type.name == name,
      orElse: () => reactions[ReactionType.like]!,
    );
  }
}

class PostReactionExtended {
  final String userId;
  final ReactionType type;
  final DateTime timestamp;
  final String userName;

  const PostReactionExtended({
    required this.userId,
    required this.type,
    required this.timestamp,
    required this.userName,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'userName': userName,
    };
  }

  factory PostReactionExtended.fromJson(Map<String, dynamic> json) {
    return PostReactionExtended(
      userId: json['userId'],
      type: ReactionType.values.firstWhere((e) => e.name == json['type']),
      timestamp: DateTime.parse(json['timestamp']),
      userName: json['userName'],
    );
  }
}
