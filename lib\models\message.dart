enum MessageType {
  text,
  image,
  video,
  audio,
  file,
  emoji,
  sticker,
  location,
  contact,
  reply, // رد على رسالة
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

class Message {
  final String id;
  final String senderId;
  final String receiverId;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final bool isRead;
  final MessageStatus status;

  // ميزات متقدمة
  final String? mediaUrl; // رابط الصورة/الفيديو/الملف
  final String? thumbnailUrl; // صورة مصغرة للفيديو
  final String? fileName; // اسم الملف
  final int? fileSize; // حجم الملف بالبايت
  final Duration? audioDuration; // مدة الصوت
  final Duration? videoDuration; // مدة الفيديو
  final String? replyToMessageId; // معرف الرسالة المرد عليها
  final Message? replyToMessage; // الرسالة المرد عليها
  final Map<String, dynamic>? metadata; // بيانات إضافية

  // معلومات الموقع
  final double? latitude;
  final double? longitude;
  final String? locationName;

  // ميزات التفاعل والتثبيت
  final Map<String, String> reactions; // userId -> emoji
  final bool isPinned;

  const Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    this.type = MessageType.text,
    required this.timestamp,
    this.isRead = false,
    this.status = MessageStatus.sent,
    this.mediaUrl,
    this.thumbnailUrl,
    this.fileName,
    this.fileSize,
    this.audioDuration,
    this.videoDuration,
    this.replyToMessageId,
    this.replyToMessage,
    this.metadata,
    this.latitude,
    this.longitude,
    this.locationName,
    this.reactions = const {},
    this.isPinned = false,
  });

  Message copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? content,
    MessageType? type,
    DateTime? timestamp,
    bool? isRead,
    MessageStatus? status,
    String? mediaUrl,
    String? thumbnailUrl,
    String? fileName,
    int? fileSize,
    Duration? audioDuration,
    Duration? videoDuration,
    String? replyToMessageId,
    Message? replyToMessage,
    Map<String, dynamic>? metadata,
    double? latitude,
    double? longitude,
    String? locationName,
    Map<String, String>? reactions,
    bool? isPinned,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      status: status ?? this.status,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      audioDuration: audioDuration ?? this.audioDuration,
      videoDuration: videoDuration ?? this.videoDuration,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      replyToMessage: replyToMessage ?? this.replyToMessage,
      metadata: metadata ?? this.metadata,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      reactions: reactions ?? this.reactions,
      isPinned: isPinned ?? this.isPinned,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'receiverId': receiverId,
      'content': content,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'status': status.name,
      'mediaUrl': mediaUrl,
      'thumbnailUrl': thumbnailUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'audioDuration': audioDuration?.inMilliseconds,
      'videoDuration': videoDuration?.inMilliseconds,
      'replyToMessageId': replyToMessageId,
      'replyToMessage': replyToMessage?.toJson(),
      'metadata': metadata,
      'latitude': latitude,
      'longitude': longitude,
      'locationName': locationName,
      'reactions': reactions,
      'isPinned': isPinned,
    };
  }

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      senderId: json['senderId'],
      receiverId: json['receiverId'],
      content: json['content'],
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.text,
      ),
      timestamp: DateTime.parse(json['timestamp']),
      isRead: json['isRead'] ?? false,
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      mediaUrl: json['mediaUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      fileName: json['fileName'],
      fileSize: json['fileSize'],
      audioDuration: json['audioDuration'] != null
          ? Duration(milliseconds: json['audioDuration'])
          : null,
      videoDuration: json['videoDuration'] != null
          ? Duration(milliseconds: json['videoDuration'])
          : null,
      replyToMessageId: json['replyToMessageId'],
      replyToMessage: json['replyToMessage'] != null
          ? Message.fromJson(json['replyToMessage'])
          : null,
      metadata: json['metadata'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      locationName: json['locationName'],
      reactions: Map<String, String>.from(json['reactions'] ?? {}),
      isPinned: json['isPinned'] ?? false,
    );
  }
}
