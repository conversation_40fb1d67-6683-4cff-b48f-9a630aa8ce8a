import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/comments_provider.dart';
import '../providers/auth_provider.dart';
import '../models/comment.dart';
import '../theme/app_theme.dart';
import '../widgets/comment_item.dart';
import '../widgets/comment_input.dart';

class CommentsScreen extends StatefulWidget {
  final String postId;
  final String postTitle;

  const CommentsScreen({
    super.key,
    required this.postId,
    required this.postTitle,
  });

  @override
  State<CommentsScreen> createState() => _CommentsScreenState();
}

class _CommentsScreenState extends State<CommentsScreen> {
  final ScrollController _scrollController = ScrollController();
  Comment? _replyingTo;
  Comment? _editingComment;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CommentsProvider>(context, listen: false)
          .loadComments(widget.postId);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'التعليقات',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Consumer<CommentsProvider>(
            builder: (context, provider, child) {
              final commentsCount = provider.getCommentsCount(widget.postId);
              return Center(
                child: Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: Text(
                    '$commentsCount تعليق',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // قائمة التعليقات
          Expanded(
            child: Consumer<CommentsProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading && provider.getComments(widget.postId).isEmpty) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (provider.errorMessage != null) {
                  return _buildErrorState(provider);
                }

                final comments = provider.getComments(widget.postId);

                if (comments.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () => provider.refreshComments(widget.postId),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: comments.length,
                    itemBuilder: (context, index) {
                      final comment = comments[index];
                      return CommentItem(
                        comment: comment,
                        postId: widget.postId,
                        currentUserId: _getCurrentUserId(),
                        onReply: () => _startReply(comment),
                        onEdit: (comment) => _startEdit(comment),
                        onDelete: (comment) => _deleteComment(comment),
                      );
                    },
                  ),
                );
              },
            ),
          ),

          // شريط إدخال التعليق
          _buildCommentInput(),
        ],
      ),
    );
  }

  Widget _buildErrorState(CommentsProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            provider.errorMessage!,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => provider.loadComments(widget.postId),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.comment_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد تعليقات بعد',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'كن أول من يعلق على هذا المنشور',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: CommentInput(
        postId: widget.postId,
        replyingTo: _replyingTo,
        editingComment: _editingComment,
        onCommentSubmitted: _handleCommentSubmitted,
        onCancelReply: () {
          setState(() {
            _replyingTo = null;
          });
        },
        onCancelEdit: () {
          setState(() {
            _editingComment = null;
          });
        },
      ),
    );
  }

  void _startReply(Comment comment) {
    setState(() {
      _replyingTo = comment;
      _editingComment = null;
    });
  }

  void _startEdit(Comment comment) {
    setState(() {
      _editingComment = comment;
      _replyingTo = null;
    });
  }

  Future<void> _deleteComment(Comment comment) async {
    final provider = Provider.of<CommentsProvider>(context, listen: false);
    
    final success = await provider.deleteComment(
      postId: widget.postId,
      commentId: comment.id,
    );

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف التعليق'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(provider.errorMessage ?? 'خطأ في حذف التعليق'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleCommentSubmitted() {
    setState(() {
      _replyingTo = null;
      _editingComment = null;
    });

    // التمرير إلى أسفل القائمة لعرض التعليق الجديد
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  String _getCurrentUserId() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.currentUser?.id ?? 'current_user';
  }
}
