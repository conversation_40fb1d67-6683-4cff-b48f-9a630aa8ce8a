import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../providers/social_provider.dart';
import '../providers/auth_provider.dart';

class CreateGroupScreen extends StatefulWidget {
  const CreateGroupScreen({super.key});

  @override
  State<CreateGroupScreen> createState() => _CreateGroupScreenState();
}

class _CreateGroupScreenState extends State<CreateGroupScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  
  GroupPrivacy _selectedPrivacy = GroupPrivacy.public;
  bool _isLoading = false;
  String? _coverImagePath;
  String? _profileImagePath;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'إنشاء مجموعة جديدة',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _createGroup,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text(
                    'إنشاء',
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الغلاف
            _buildCoverImageSection(),
            
            const SizedBox(height: 24),
            
            // صورة المجموعة
            _buildProfileImageSection(),
            
            const SizedBox(height: 24),
            
            // اسم المجموعة
            _buildSectionTitle('اسم المجموعة *'),
            const SizedBox(height: 8),
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                hintText: 'أدخل اسم المجموعة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
                counterText: '${_nameController.text.length}/100',
              ),
              maxLength: 100,
              onChanged: (value) => setState(() {}),
            ),
            
            const SizedBox(height: 16),
            
            // وصف المجموعة
            _buildSectionTitle('وصف المجموعة'),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              decoration: InputDecoration(
                hintText: 'أدخل وصف المجموعة (اختياري)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
                counterText: '${_descriptionController.text.length}/500',
              ),
              maxLines: 4,
              maxLength: 500,
              onChanged: (value) => setState(() {}),
            ),
            
            const SizedBox(height: 24),
            
            // نوع المجموعة
            _buildSectionTitle('نوع المجموعة'),
            const SizedBox(height: 8),
            _buildPrivacyOptions(),
            
            const SizedBox(height: 24),
            
            // معلومات إضافية
            _buildInfoCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('صورة الغلاف'),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _selectCoverImage,
          child: Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
              image: _coverImagePath != null
                  ? DecorationImage(
                      image: AssetImage(_coverImagePath!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: _coverImagePath == null
                ? const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_photo_alternate,
                        size: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'إضافة صورة غلاف',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '(اختياري)',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  )
                : Stack(
                    children: [
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.edit,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: _selectCoverImage,
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('صورة المجموعة'),
        const SizedBox(height: 8),
        Row(
          children: [
            GestureDetector(
              onTap: _selectProfileImage,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey[300]!),
                  image: _profileImagePath != null
                      ? DecorationImage(
                          image: AssetImage(_profileImagePath!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: _profileImagePath == null
                    ? const Icon(
                        Icons.group,
                        size: 40,
                        color: Colors.grey,
                      )
                    : null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ElevatedButton.icon(
                    onPressed: _selectProfileImage,
                    icon: const Icon(Icons.camera_alt, size: 18),
                    label: Text(_profileImagePath == null ? 'إضافة صورة' : 'تغيير الصورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'اختياري - يمكنك إضافة صورة للمجموعة',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPrivacyOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            RadioListTile<GroupPrivacy>(
              title: const Text('عامة'),
              subtitle: const Text('يمكن لأي شخص العثور عليها والانضمام'),
              value: GroupPrivacy.public,
              groupValue: _selectedPrivacy,
              onChanged: (value) {
                setState(() {
                  _selectedPrivacy = value!;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
            
            RadioListTile<GroupPrivacy>(
              title: const Text('خاصة'),
              subtitle: const Text('لا يمكن الانضمام إلا بدعوة أو موافقة'),
              value: GroupPrivacy.private,
              groupValue: _selectedPrivacy,
              onChanged: (value) {
                setState(() {
                  _selectedPrivacy = value!;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
            
            RadioListTile<GroupPrivacy>(
              title: const Text('مخفية'),
              subtitle: const Text('لا تظهر في البحث، فقط من يملك الرابط'),
              value: GroupPrivacy.secret,
              groupValue: _selectedPrivacy,
              onChanged: (value) {
                setState(() {
                  _selectedPrivacy = value!;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: Colors.blue[50],
      child: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'معلومات مهمة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              '• يمكنك تغيير إعدادات المجموعة لاحقاً\n'
              '• ستصبح مديراً للمجموعة تلقائياً\n'
              '• يمكنك إضافة مديرين ومشرفين آخرين\n'
              '• يمكنك حذف المجموعة في أي وقت',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  void _selectCoverImage() {
    // محاكاة اختيار صورة الغلاف
    setState(() {
      _coverImagePath = 'assets/images/sample_cover.jpg';
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم اختيار صورة الغلاف'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _selectProfileImage() {
    // محاكاة اختيار صورة المجموعة
    setState(() {
      _profileImagePath = 'assets/images/sample_group.jpg';
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم اختيار صورة المجموعة'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  Future<void> _createGroup() async {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال اسم المجموعة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final socialProvider = Provider.of<SocialProvider>(context, listen: false);
      
      final currentUser = authProvider.currentUser;
      if (currentUser == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // إنشاء المجموعة
      final group = Group(
        id: 'group_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        privacy: _selectedPrivacy,
        coverImage: _coverImagePath,
        profileImage: _profileImagePath,
        createdBy: currentUser.id,
        createdAt: DateTime.now(),
        members: [
          GroupMember(
            userId: currentUser.id,
            role: GroupMemberRole.admin,
            joinedAt: DateTime.now(),
          ),
        ],
        lastActivity: DateTime.now(),
      );

      // إضافة المجموعة للنظام
      socialProvider.addGroup(group);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء المجموعة بنجاح! 🎉'),
            backgroundColor: Colors.green,
          ),
        );
        
        Navigator.pop(context, group);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء المجموعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
