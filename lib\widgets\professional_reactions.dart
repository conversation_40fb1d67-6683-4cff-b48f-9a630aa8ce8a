import 'package:flutter/material.dart';
import '../models/reaction_types.dart';

class ProfessionalReactionIcon extends StatelessWidget {
  final ReactionType type;
  final double size;
  final bool isSelected;
  final bool showShadow;

  const ProfessionalReactionIcon({
    super.key,
    required this.type,
    this.size = 24,
    this.isSelected = false,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: _getReactionColor(type).withValues(alpha: 0.3),
                  blurRadius: isSelected ? 8 : 4,
                  offset: const Offset(0, 2),
                  spreadRadius: isSelected ? 1 : 0,
                ),
              ]
            : null,
      ),
      child: _buildReactionIcon(type, size, isSelected),
    );
  }

  Widget _buildReactionIcon(ReactionType type, double size, bool isSelected) {
    final scale = isSelected ? 1.1 : 1.0;
    
    switch (type) {
      case ReactionType.like:
        return Transform.scale(
          scale: scale,
          child: _buildLikeIcon(size),
        );
      case ReactionType.love:
        return Transform.scale(
          scale: scale,
          child: _buildLoveIcon(size),
        );
      case ReactionType.haha:
        return Transform.scale(
          scale: scale,
          child: _buildLaughIcon(size),
        );
      case ReactionType.wow:
        return Transform.scale(
          scale: scale,
          child: _buildWowIcon(size),
        );
      case ReactionType.sad:
        return Transform.scale(
          scale: scale,
          child: _buildSadIcon(size),
        );
      case ReactionType.angry:
        return Transform.scale(
          scale: scale,
          child: _buildAngryIcon(size),
        );
    }
  }

  Widget _buildLikeIcon(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1877F2),
            Color(0xFF0E4A8C),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF1877F2).withValues(alpha: 0.4),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // خلفية بيضاء للأيقونة
          Center(
            child: Container(
              width: size * 0.8,
              height: size * 0.8,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
          ),
          // الأيقونة
          Center(
            child: Icon(
              Icons.thumb_up,
              color: const Color(0xFF1877F2),
              size: size * 0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoveIcon(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFF6B6B),
            Color(0xFFE53E3E),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFF6B6B).withValues(alpha: 0.4),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // تأثير النبضة للقلب
          Center(
            child: Container(
              width: size * 0.9,
              height: size * 0.9,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
            ),
          ),
          // القلب
          Center(
            child: Icon(
              Icons.favorite,
              color: Colors.white,
              size: size * 0.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLaughIcon(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFC107),
            Color(0xFFFF8F00),
          ],
        ),
      ),
      child: Center(
        child: Text(
          '😂',
          style: TextStyle(
            fontSize: size * 0.7,
          ),
        ),
      ),
    );
  }

  Widget _buildWowIcon(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFC107),
            Color(0xFFFF8F00),
          ],
        ),
      ),
      child: Center(
        child: Text(
          '😮',
          style: TextStyle(
            fontSize: size * 0.7,
          ),
        ),
      ),
    );
  }

  Widget _buildSadIcon(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFC107),
            Color(0xFFFF8F00),
          ],
        ),
      ),
      child: Center(
        child: Text(
          '😢',
          style: TextStyle(
            fontSize: size * 0.7,
          ),
        ),
      ),
    );
  }

  Widget _buildAngryIcon(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFF5722),
            Color(0xFFD32F2F),
          ],
        ),
      ),
      child: Center(
        child: Text(
          '😠',
          style: TextStyle(
            fontSize: size * 0.7,
          ),
        ),
      ),
    );
  }

  Color _getReactionColor(ReactionType type) {
    switch (type) {
      case ReactionType.like:
        return const Color(0xFF1877F2);
      case ReactionType.love:
        return const Color(0xFFFF6B6B);
      case ReactionType.haha:
        return const Color(0xFFFFC107);
      case ReactionType.wow:
        return const Color(0xFFFFC107);
      case ReactionType.sad:
        return const Color(0xFFFFC107);
      case ReactionType.angry:
        return const Color(0xFFFF5722);
    }
  }
}

// Widget للتفاعلات المتحركة
class AnimatedProfessionalReaction extends StatefulWidget {
  final ReactionType type;
  final double size;
  final bool isSelected;
  final VoidCallback? onTap;

  const AnimatedProfessionalReaction({
    super.key,
    required this.type,
    this.size = 24,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<AnimatedProfessionalReaction> createState() => _AnimatedProfessionalReactionState();
}

class _AnimatedProfessionalReactionState extends State<AnimatedProfessionalReaction>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _animateReaction() {
    _controller.forward().then((_) {
      _controller.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _animateReaction();
        widget.onTap?.call();
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: ProfessionalReactionIcon(
                type: widget.type,
                size: widget.size,
                isSelected: widget.isSelected,
              ),
            ),
          );
        },
      ),
    );
  }
}

// Widget لعرض التفاعلات في شريط الاختيار
class ProfessionalReactionPicker extends StatelessWidget {
  final ReactionType? currentReaction;
  final Function(ReactionType) onReactionSelected;
  final VoidCallback onClose;

  const ProfessionalReactionPicker({
    super.key,
    this.currentReaction,
    required this.onReactionSelected,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: ReactionType.values.map((type) {
          final isSelected = currentReaction == type;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: AnimatedProfessionalReaction(
              type: type,
              size: 32,
              isSelected: isSelected,
              onTap: () {
                onReactionSelected(type);
                onClose();
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}
