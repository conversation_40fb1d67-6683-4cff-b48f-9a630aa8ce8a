import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/live_stream.dart';
import '../models/user.dart';
import '../models/reaction_types.dart';

class LiveStreamService {
  static const String _streamsKey = 'live_streams';
  static const String _activeStreamsKey = 'active_streams';
  
  // Stream controllers للتحديث الفوري
  final StreamController<List<LiveStream>> _activeStreamsController = 
      StreamController<List<LiveStream>>.broadcast();
  final StreamController<LiveStream> _streamUpdatesController = 
      StreamController<LiveStream>.broadcast();
  final StreamController<LiveStreamComment> _commentsController = 
      StreamController<LiveStreamComment>.broadcast();
  final StreamController<LiveStreamReaction> _reactionsController = 
      StreamController<LiveStreamReaction>.broadcast();
  final StreamController<List<LiveStreamViewer>> _viewersController = 
      StreamController<List<LiveStreamViewer>>.broadcast();
  
  // Streams للاستماع للتحديثات
  Stream<List<LiveStream>> get activeStreamsStream => _activeStreamsController.stream;
  Stream<LiveStream> get streamUpdatesStream => _streamUpdatesController.stream;
  Stream<LiveStreamComment> get commentsStream => _commentsController.stream;
  Stream<LiveStreamReaction> get reactionsStream => _reactionsController.stream;
  Stream<List<LiveStreamViewer>> get viewersStream => _viewersController.stream;
  
  // محاكاة خادم البث
  Timer? _streamUpdateTimer;
  final Map<String, Timer> _streamTimers = {};
  
  // بدء بث مباشر جديد
  Future<LiveStream> startLiveStream({
    required String streamerId,
    required User streamer,
    required String title,
    String? description,
    LiveStreamPrivacy privacy = LiveStreamPrivacy.public,
    LiveStreamQuality quality = LiveStreamQuality.auto,
  }) async {
    final stream = LiveStream(
      id: 'stream_${DateTime.now().millisecondsSinceEpoch}',
      streamerId: streamerId,
      streamer: streamer,
      title: title,
      description: description,
      status: LiveStreamStatus.preparing,
      privacy: privacy,
      quality: quality,
      startTime: DateTime.now(),
      streamUrl: _generateStreamUrl(),
    );
    
    // حفظ البث
    await _saveStream(stream);
    
    // محاكاة تحضير البث
    await Future.delayed(const Duration(seconds: 2));
    
    // تحديث حالة البث إلى مباشر
    final liveStream = stream.copyWith(
      status: LiveStreamStatus.live,
    );
    
    await _updateStream(liveStream);
    
    // بدء محاكاة التحديثات
    _startStreamSimulation(liveStream.id);
    
    // إرسال إشعارات للمتابعين
    await _notifyFollowers(liveStream);
    
    return liveStream;
  }
  
  // إنهاء البث المباشر
  Future<LiveStream> endLiveStream(String streamId) async {
    final stream = await getStream(streamId);
    if (stream == null) throw Exception('البث غير موجود');
    
    final endTime = DateTime.now();
    final duration = endTime.difference(stream.startTime);
    
    final endedStream = stream.copyWith(
      status: LiveStreamStatus.ended,
      endTime: endTime,
      duration: duration,
    );
    
    await _updateStream(endedStream);
    
    // إيقاف المحاكاة
    _stopStreamSimulation(streamId);
    
    // حفظ البث كفيديو (اختياري)
    if (stream.isRecording) {
      await _saveStreamAsVideo(endedStream);
    }
    
    return endedStream;
  }
  
  // الحصول على البثوث النشطة
  Future<List<LiveStream>> getActiveStreams() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final streamsJson = prefs.getStringList(_activeStreamsKey) ?? [];
      
      final streams = streamsJson
          .map((json) => LiveStream.fromJson(jsonDecode(json)))
          .where((stream) => stream.isLive)
          .toList();
      
      // ترتيب حسب عدد المشاهدين
      streams.sort((a, b) => b.viewerCount.compareTo(a.viewerCount));
      
      return streams;
    } catch (e) {
      print('خطأ في تحميل البثوث النشطة: $e');
      return _generateSampleStreams();
    }
  }
  
  // الحصول على بث معين
  Future<LiveStream?> getStream(String streamId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final streamJson = prefs.getString('stream_$streamId');
      
      if (streamJson != null) {
        return LiveStream.fromJson(jsonDecode(streamJson));
      }
      
      return null;
    } catch (e) {
      print('خطأ في تحميل البث: $e');
      return null;
    }
  }
  
  // الانضمام لمشاهدة البث
  Future<void> joinStream(String streamId, String userId, String userName) async {
    final stream = await getStream(streamId);
    if (stream == null) return;
    
    final viewer = LiveStreamViewer(
      userId: userId,
      userName: userName,
      joinedAt: DateTime.now(),
    );
    
    final updatedViewers = [...stream.viewers, viewer];
    final updatedStream = stream.copyWith(
      viewers: updatedViewers,
      viewerCount: updatedViewers.length,
      totalViewers: stream.totalViewers + 1,
      peakViewers: max(stream.peakViewers, updatedViewers.length),
    );
    
    await _updateStream(updatedStream);
    _viewersController.add(updatedViewers);
  }
  
  // مغادرة البث
  Future<void> leaveStream(String streamId, String userId) async {
    final stream = await getStream(streamId);
    if (stream == null) return;
    
    final updatedViewers = stream.viewers
        .where((viewer) => viewer.userId != userId)
        .toList();
    
    final updatedStream = stream.copyWith(
      viewers: updatedViewers,
      viewerCount: updatedViewers.length,
    );
    
    await _updateStream(updatedStream);
    _viewersController.add(updatedViewers);
  }
  
  // إضافة تعليق للبث
  Future<LiveStreamComment> addComment({
    required String streamId,
    required String userId,
    required String userName,
    String? userAvatar,
    required String content,
  }) async {
    final comment = LiveStreamComment(
      id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      userName: userName,
      userAvatar: userAvatar,
      content: content,
      timestamp: DateTime.now(),
    );
    
    final stream = await getStream(streamId);
    if (stream != null) {
      final updatedComments = [...stream.comments, comment];
      final updatedStream = stream.copyWith(comments: updatedComments);
      await _updateStream(updatedStream);
    }
    
    _commentsController.add(comment);
    return comment;
  }
  
  // إضافة تفاعل للبث
  Future<void> addReaction({
    required String streamId,
    required String userId,
    required String userName,
    required ReactionType reactionType,
  }) async {
    final reaction = LiveStreamReaction(
      id: 'reaction_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      userName: userName,
      type: reactionType,
      timestamp: DateTime.now(),
    );
    
    final stream = await getStream(streamId);
    if (stream != null) {
      final updatedReactions = [...stream.reactions, reaction];
      final reactionCounts = Map<ReactionType, int>.from(stream.reactionCounts);
      reactionCounts[reactionType] = (reactionCounts[reactionType] ?? 0) + 1;
      
      final updatedStream = stream.copyWith(
        reactions: updatedReactions,
        reactionCounts: reactionCounts,
      );
      
      await _updateStream(updatedStream);
    }
    
    _reactionsController.add(reaction);
  }
  
  // تحديث إعدادات البث
  Future<LiveStream> updateStreamSettings({
    required String streamId,
    bool? isCameraEnabled,
    bool? isMicEnabled,
    bool? isFrontCamera,
    LiveStreamQuality? quality,
  }) async {
    final stream = await getStream(streamId);
    if (stream == null) throw Exception('البث غير موجود');
    
    final updatedStream = stream.copyWith(
      isCameraEnabled: isCameraEnabled ?? stream.isCameraEnabled,
      isMicEnabled: isMicEnabled ?? stream.isMicEnabled,
      isFrontCamera: isFrontCamera ?? stream.isFrontCamera,
      quality: quality ?? stream.quality,
    );
    
    await _updateStream(updatedStream);
    return updatedStream;
  }
  
  // حفظ البث
  Future<void> _saveStream(LiveStream stream) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ البث الفردي
      await prefs.setString(
        'stream_${stream.id}',
        jsonEncode(stream.toJson()),
      );
      
      // إضافة للبثوث النشطة إذا كان مباشر
      if (stream.isLive) {
        final activeStreams = await getActiveStreams();
        activeStreams.add(stream);
        
        final activeStreamsJson = activeStreams
            .map((s) => jsonEncode(s.toJson()))
            .toList();
        
        await prefs.setStringList(_activeStreamsKey, activeStreamsJson);
        _activeStreamsController.add(activeStreams);
      }
    } catch (e) {
      print('خطأ في حفظ البث: $e');
    }
  }
  
  // تحديث البث
  Future<void> _updateStream(LiveStream stream) async {
    await _saveStream(stream);
    _streamUpdatesController.add(stream);
  }
  
  // توليد رابط البث
  String _generateStreamUrl() {
    final random = Random();
    final streamKey = random.nextInt(999999).toString().padLeft(6, '0');
    return 'rtmp://live.example.com/stream/$streamKey';
  }
  
  // بدء محاكاة البث
  void _startStreamSimulation(String streamId) {
    _streamTimers[streamId] = Timer.periodic(
      const Duration(seconds: 5),
      (timer) async {
        await _simulateStreamActivity(streamId);
      },
    );
  }
  
  // إيقاف محاكاة البث
  void _stopStreamSimulation(String streamId) {
    _streamTimers[streamId]?.cancel();
    _streamTimers.remove(streamId);
  }
  
  // محاكاة نشاط البث
  Future<void> _simulateStreamActivity(String streamId) async {
    final stream = await getStream(streamId);
    if (stream == null || !stream.isLive) return;
    
    final random = Random();
    
    // محاكاة تغيير عدد المشاهدين
    final viewerChange = random.nextInt(5) - 2; // -2 إلى +2
    final newViewerCount = max(0, stream.viewerCount + viewerChange);
    
    // محاكاة تعليقات عشوائية
    if (random.nextBool()) {
      await _addRandomComment(streamId);
    }
    
    // محاكاة تفاعلات عشوائية
    if (random.nextBool()) {
      await _addRandomReaction(streamId);
    }
    
    // تحديث البث
    final updatedStream = stream.copyWith(
      viewerCount: newViewerCount,
      peakViewers: max(stream.peakViewers, newViewerCount),
      duration: DateTime.now().difference(stream.startTime),
    );
    
    await _updateStream(updatedStream);
  }
  
  // إضافة تعليق عشوائي
  Future<void> _addRandomComment(String streamId) async {
    final comments = [
      'مرحباً! 👋',
      'بث رائع! 🔥',
      'استمر! 💪',
      'أحبك! ❤️',
      'ما شاء الله! 🌟',
      'رائع جداً! 👏',
      'متابع من المغرب 🇲🇦',
      'سلام عليكم 🙏',
    ];
    
    final names = ['أحمد', 'فاطمة', 'محمد', 'عائشة', 'علي', 'خديجة'];
    final random = Random();
    
    await addComment(
      streamId: streamId,
      userId: 'user_${random.nextInt(1000)}',
      userName: names[random.nextInt(names.length)],
      content: comments[random.nextInt(comments.length)],
    );
  }
  
  // إضافة تفاعل عشوائي
  Future<void> _addRandomReaction(String streamId) async {
    final reactions = ReactionType.values;
    final names = ['أحمد', 'فاطمة', 'محمد', 'عائشة', 'علي', 'خديجة'];
    final random = Random();
    
    await addReaction(
      streamId: streamId,
      userId: 'user_${random.nextInt(1000)}',
      userName: names[random.nextInt(names.length)],
      reactionType: reactions[random.nextInt(reactions.length)],
    );
  }
  
  // إرسال إشعارات للمتابعين
  Future<void> _notifyFollowers(LiveStream stream) async {
    // هنا يمكن إرسال إشعارات push للمتابعين
    print('إرسال إشعار: ${stream.streamer.name} بدأ بثاً مباشراً!');
  }
  
  // حفظ البث كفيديو
  Future<void> _saveStreamAsVideo(LiveStream stream) async {
    // هنا يمكن حفظ البث كفيديو في المعرض
    print('حفظ البث كفيديو: ${stream.title}');
  }
  
  // إنشاء بثوث تجريبية
  List<LiveStream> _generateSampleStreams() {
    final now = DateTime.now();
    
    return [
      LiveStream(
        id: 'sample_1',
        streamerId: 'user_1',
        streamer: User(
          id: 'user_1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          avatar: null,
          gender: 'male',
          joinDate: now,
        ),
        title: 'بث مباشر من الرياض 🇸🇦',
        description: 'مرحباً بكم في بثي المباشر!',
        status: LiveStreamStatus.live,
        startTime: now.subtract(const Duration(minutes: 15)),
        viewerCount: 234,
        totalViewers: 456,
        peakViewers: 289,
        duration: const Duration(minutes: 15),
      ),
      LiveStream(
        id: 'sample_2',
        streamerId: 'user_2',
        streamer: User(
          id: 'user_2',
          name: 'فاطمة علي',
          email: '<EMAIL>',
          avatar: null,
          gender: 'female',
          joinDate: now,
        ),
        title: 'طبخ مع فاطمة 👩‍🍳',
        description: 'تعلموا معي طبخ المنسف!',
        status: LiveStreamStatus.live,
        startTime: now.subtract(const Duration(minutes: 8)),
        viewerCount: 89,
        totalViewers: 123,
        peakViewers: 95,
        duration: const Duration(minutes: 8),
      ),
    ];
  }
  
  void dispose() {
    _activeStreamsController.close();
    _streamUpdatesController.close();
    _commentsController.close();
    _reactionsController.close();
    _viewersController.close();
    
    for (final timer in _streamTimers.values) {
      timer.cancel();
    }
    _streamTimers.clear();
    
    _streamUpdateTimer?.cancel();
  }
}
