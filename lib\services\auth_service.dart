import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';

class AuthService {
  static const String _usersKey = 'registered_users';
  static const String _currentUserKey = 'current_user';
  static const String _tokenKey = 'auth_token';
  
  final _uuid = const Uuid();

  // تسجيل مستخدم جديد
  Future<RegistrationResponse> register(RegistrationData data) async {
    try {
      // التحقق من صحة البيانات
      if (!data.isComplete) {
        return RegistrationResponse.error('يرجى إكمال جميع البيانات المطلوبة');
      }

      // التحقق من صحة البريد الإلكتروني
      if (!_isValidEmail(data.email)) {
        return RegistrationResponse.error('البريد الإلكتروني غير صحيح');
      }

      // التحقق من قوة كلمة المرور
      if (!_isValidPassword(data.password)) {
        return RegistrationResponse.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }

      // التحقق من عدم وجود المستخدم مسبقاً
      final existingUser = await _getUserByEmail(data.email);
      if (existingUser != null) {
        return RegistrationResponse.error('البريد الإلكتروني مستخدم بالفعل');
      }

      // إنشاء المستخدم الجديد
      final userId = _uuid.v4();
      final passwordHash = _hashPassword(data.password);
      final now = DateTime.now();

      final user = UserProfile(
        id: userId,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        passwordHash: passwordHash,
        gender: data.gender!,
        birthDate: data.birthDate!,
        country: data.country,
        city: data.city,
        createdAt: now,
        lastLoginAt: now,
        isEmailVerified: true, // في التطبيق الحقيقي سيكون false
        isActive: true,
      );

      // حفظ المستخدم
      await _saveUser(user);

      // إنشاء توكن
      final token = _generateToken(userId);
      await _saveToken(token);
      await _saveCurrentUser(user);

      return RegistrationResponse.success(userId, token);
    } catch (e) {
      return RegistrationResponse.error('حدث خطأ أثناء التسجيل: $e');
    }
  }

  // تسجيل الدخول
  Future<LoginResponse> login(LoginData data) async {
    try {
      // التحقق من صحة البيانات
      if (!data.isValid) {
        return LoginResponse.error('يرجى إدخال البريد الإلكتروني وكلمة المرور');
      }

      // البحث عن المستخدم
      final user = await _getUserByEmail(data.email);
      if (user == null) {
        return LoginResponse.error('البريد الإلكتروني غير مسجل');
      }

      // التحقق من كلمة المرور
      final passwordHash = _hashPassword(data.password);
      if (user.passwordHash != passwordHash) {
        return LoginResponse.error('كلمة المرور غير صحيحة');
      }

      // التحقق من حالة الحساب
      if (!user.isActive) {
        return LoginResponse.error('الحساب معطل');
      }

      // تحديث آخر تسجيل دخول
      final updatedUser = user.copyWith(lastLoginAt: DateTime.now());
      await _updateUser(updatedUser);

      // إنشاء توكن جديد
      final token = _generateToken(user.id);
      await _saveToken(token);
      await _saveCurrentUser(updatedUser);

      return LoginResponse.success(token, updatedUser);
    } catch (e) {
      return LoginResponse.error('حدث خطأ أثناء تسجيل الدخول: $e');
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentUserKey);
    await prefs.remove(_tokenKey);
  }

  // الحصول على المستخدم الحالي
  Future<UserProfile?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_currentUserKey);
      if (userJson != null) {
        return UserProfile.fromJson(jsonDecode(userJson));
      }
    } catch (e) {
      // تجاهل الأخطاء
    }
    return null;
  }

  // التحقق من صحة التوكن
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(_tokenKey);
      final user = await getCurrentUser();
      return token != null && user != null;
    } catch (e) {
      return false;
    }
  }

  // الحصول على جميع المستخدمين (للاختبار)
  Future<List<UserProfile>> getAllUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getStringList(_usersKey) ?? [];
      return usersJson
          .map((json) => UserProfile.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // دوال مساعدة خاصة
  Future<UserProfile?> _getUserByEmail(String email) async {
    final users = await getAllUsers();
    try {
      return users.firstWhere((user) => user.email.toLowerCase() == email.toLowerCase());
    } catch (e) {
      return null;
    }
  }

  Future<void> _saveUser(UserProfile user) async {
    final prefs = await SharedPreferences.getInstance();
    final users = await getAllUsers();
    users.add(user);
    final usersJson = users.map((u) => jsonEncode(u.toJson())).toList();
    await prefs.setStringList(_usersKey, usersJson);
  }

  Future<void> _updateUser(UserProfile user) async {
    final prefs = await SharedPreferences.getInstance();
    final users = await getAllUsers();
    final index = users.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      users[index] = user;
      final usersJson = users.map((u) => jsonEncode(u.toJson())).toList();
      await prefs.setStringList(_usersKey, usersJson);
    }
  }

  Future<void> _saveCurrentUser(UserProfile user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentUserKey, jsonEncode(user.toJson()));
  }

  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password + 'arzawo_salt'); // إضافة salt
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _generateToken(String userId) {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomString = List.generate(16, (index) => 
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'[random.nextInt(62)]).join();
    return base64Encode(utf8.encode('$userId:$timestamp:$randomString'));
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPassword(String password) {
    return password.length >= 6;
  }
}
