
enum OnlineStatus {
  online,
  away,
  busy,
  offline,
}

class OnlineFriend {
  final String id;
  final String name;
  final String? profileImageUrl;
  final OnlineStatus status;
  final DateTime lastSeen;
  final bool isTyping;
  final String? currentActivity;

  const OnlineFriend({
    required this.id,
    required this.name,
    this.profileImageUrl,
    required this.status,
    required this.lastSeen,
    this.isTyping = false,
    this.currentActivity,
  });

  bool get isOnline => status == OnlineStatus.online;
  bool get isAway => status == OnlineStatus.away;
  bool get isBusy => status == OnlineStatus.busy;
  bool get isOffline => status == OnlineStatus.offline;

  String get statusText {
    switch (status) {
      case OnlineStatus.online:
        return 'متصل الآن';
      case OnlineStatus.away:
        return 'بعيد';
      case OnlineStatus.busy:
        return 'مشغول';
      case OnlineStatus.offline:
        return _getLastSeenText();
    }
  }

  String _getLastSeenText() {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inMinutes < 1) {
      return 'نشط منذ لحظات';
    } else if (difference.inMinutes < 60) {
      return 'نشط منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'نشط منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'نشط منذ ${difference.inDays} يوم';
    } else {
      return 'نشط منذ أسبوع';
    }
  }

  OnlineFriend copyWith({
    String? id,
    String? name,
    String? profileImageUrl,
    OnlineStatus? status,
    DateTime? lastSeen,
    bool? isTyping,
    String? currentActivity,
  }) {
    return OnlineFriend(
      id: id ?? this.id,
      name: name ?? this.name,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      status: status ?? this.status,
      lastSeen: lastSeen ?? this.lastSeen,
      isTyping: isTyping ?? this.isTyping,
      currentActivity: currentActivity ?? this.currentActivity,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'profileImageUrl': profileImageUrl,
      'status': status.name,
      'lastSeen': lastSeen.toIso8601String(),
      'isTyping': isTyping,
      'currentActivity': currentActivity,
    };
  }

  factory OnlineFriend.fromJson(Map<String, dynamic> json) {
    return OnlineFriend(
      id: json['id'],
      name: json['name'],
      profileImageUrl: json['profileImageUrl'],
      status: OnlineStatus.values.firstWhere((e) => e.name == json['status']),
      lastSeen: DateTime.parse(json['lastSeen']),
      isTyping: json['isTyping'] ?? false,
      currentActivity: json['currentActivity'],
    );
  }
}

class OnlineFriendsData {
  final List<OnlineFriend> onlineFriends;
  final List<OnlineFriend> recentlyActive;
  final int totalOnlineCount;

  const OnlineFriendsData({
    required this.onlineFriends,
    required this.recentlyActive,
    required this.totalOnlineCount,
  });

  List<OnlineFriend> get allFriends => [...onlineFriends, ...recentlyActive];

  OnlineFriendsData copyWith({
    List<OnlineFriend>? onlineFriends,
    List<OnlineFriend>? recentlyActive,
    int? totalOnlineCount,
  }) {
    return OnlineFriendsData(
      onlineFriends: onlineFriends ?? this.onlineFriends,
      recentlyActive: recentlyActive ?? this.recentlyActive,
      totalOnlineCount: totalOnlineCount ?? this.totalOnlineCount,
    );
  }
}

// بيانات تجريبية للأصدقاء المتصلين
class MockOnlineFriendsData {
  static List<OnlineFriend> getMockOnlineFriends() {
    final now = DateTime.now();
    
    return [
      OnlineFriend(
        id: '1',
        name: 'أحمد محمد',
        profileImageUrl: 'https://picsum.photos/100/100?random=1',
        status: OnlineStatus.online,
        lastSeen: now,
        currentActivity: 'يكتب رسالة...',
      ),
      OnlineFriend(
        id: '2',
        name: 'فاطمة أحمد',
        profileImageUrl: 'https://picsum.photos/100/100?random=2',
        status: OnlineStatus.online,
        lastSeen: now.subtract(const Duration(minutes: 2)),
      ),
      OnlineFriend(
        id: '3',
        name: 'محمد علي',
        profileImageUrl: 'https://picsum.photos/100/100?random=3',
        status: OnlineStatus.away,
        lastSeen: now.subtract(const Duration(minutes: 15)),
        currentActivity: 'بعيد',
      ),
      OnlineFriend(
        id: '4',
        name: 'سارة أحمد',
        profileImageUrl: 'https://picsum.photos/100/100?random=4',
        status: OnlineStatus.online,
        lastSeen: now.subtract(const Duration(minutes: 1)),
        isTyping: true,
      ),
      OnlineFriend(
        id: '5',
        name: 'عبدالله محمد',
        profileImageUrl: 'https://picsum.photos/100/100?random=5',
        status: OnlineStatus.busy,
        lastSeen: now.subtract(const Duration(minutes: 30)),
        currentActivity: 'في اجتماع',
      ),
      OnlineFriend(
        id: '6',
        name: 'نور الدين',
        profileImageUrl: 'https://picsum.photos/100/100?random=6',
        status: OnlineStatus.online,
        lastSeen: now.subtract(const Duration(seconds: 30)),
      ),
      OnlineFriend(
        id: '7',
        name: 'ليلى حسن',
        profileImageUrl: 'https://picsum.photos/100/100?random=7',
        status: OnlineStatus.offline,
        lastSeen: now.subtract(const Duration(hours: 2)),
      ),
      OnlineFriend(
        id: '8',
        name: 'يوسف أحمد',
        profileImageUrl: 'https://picsum.photos/100/100?random=8',
        status: OnlineStatus.online,
        lastSeen: now.subtract(const Duration(minutes: 5)),
      ),
      OnlineFriend(
        id: '9',
        name: 'مريم محمد',
        profileImageUrl: 'https://picsum.photos/100/100?random=9',
        status: OnlineStatus.away,
        lastSeen: now.subtract(const Duration(minutes: 45)),
      ),
      OnlineFriend(
        id: '10',
        name: 'خالد علي',
        profileImageUrl: 'https://picsum.photos/100/100?random=10',
        status: OnlineStatus.offline,
        lastSeen: now.subtract(const Duration(hours: 1)),
      ),
    ];
  }

  static OnlineFriendsData getMockOnlineFriendsData() {
    final allFriends = getMockOnlineFriends();
    final onlineFriends = allFriends.where((f) => f.isOnline || f.isAway || f.isBusy).toList();
    final recentlyActive = allFriends.where((f) => f.isOffline).toList();
    
    return OnlineFriendsData(
      onlineFriends: onlineFriends,
      recentlyActive: recentlyActive,
      totalOnlineCount: onlineFriends.where((f) => f.isOnline).length,
    );
  }
}
