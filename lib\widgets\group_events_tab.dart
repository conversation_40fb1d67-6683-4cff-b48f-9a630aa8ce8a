import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../models/group_event.dart';
import '../providers/social_provider.dart';
import '../theme/app_theme.dart';

class GroupEventsTab extends StatefulWidget {
  final Group group;

  const GroupEventsTab({
    super.key,
    required this.group,
  });

  @override
  State<GroupEventsTab> createState() => _GroupEventsTabState();
}

class _GroupEventsTabState extends State<GroupEventsTab> {
  String _selectedFilter = 'upcoming'; // upcoming, past, all

  @override
  Widget build(BuildContext context) {
    return Consumer<SocialProvider>(
      builder: (context, socialProvider, child) {
        final events = socialProvider.getGroupEvents(widget.group.id);
        final filteredEvents = _filterEvents(events.cast<GroupEvent>());

        return Column(
          children: [
            // شريط إنشاء الأحداث
            if (widget.group.isUserMember('current_user'))
              _buildCreateEventBar(),
            
            // فلاتر الأحداث
            _buildEventFilters(),
            
            // قائمة الأحداث
            Expanded(
              child: filteredEvents.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: filteredEvents.length,
                      itemBuilder: (context, index) {
                        final event = filteredEvents[index];
                        return _buildEventCard(event);
                      },
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCreateEventBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        onPressed: () => _showCreateEventDialog(),
        icon: const Icon(Icons.add),
        label: const Text('إنشاء حدث جديد'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          minimumSize: const Size(double.infinity, 48),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildEventFilters() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          _buildFilterChip('upcoming', 'القادمة'),
          const SizedBox(width: 8),
          _buildFilterChip('past', 'السابقة'),
          const SizedBox(width: 8),
          _buildFilterChip('all', 'جميع الأحداث'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  Widget _buildEventCard(GroupEvent event) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _openEventDetails(event),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الحدث
            if (event.coverImage != null)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                child: Image.network(
                  event.coverImage!,
                  height: 150,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultEventCover(event);
                  },
                ),
              )
            else
              _buildDefaultEventCover(event),
            
            // محتوى الحدث
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // تاريخ ووقت الحدث
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getEventStatusColor(event.status).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            Text(
                              '${event.startDate.day}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: _getEventStatusColor(event.status),
                              ),
                            ),
                            Text(
                              _getMonthName(event.startDate.month),
                              style: TextStyle(
                                fontSize: 10,
                                color: _getEventStatusColor(event.status),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // عنوان الحدث
                            Text(
                              event.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            
                            const SizedBox(height: 4),
                            
                            // وقت الحدث
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 14,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _formatEventTime(event),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 4),
                            
                            // موقع الحدث
                            Row(
                              children: [
                                Icon(
                                  event.isOnline ? Icons.videocam : Icons.location_on,
                                  size: 14,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    event.isOnline ? 'حدث عبر الإنترنت' : (event.location ?? 'لم يحدد الموقع'),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      // حالة الحدث
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getEventStatusColor(event.status),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getEventStatusText(event.status),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // وصف الحدث
                  Text(
                    event.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // إحصائيات الحضور
                  Row(
                    children: [
                      _buildAttendanceInfo(
                        icon: Icons.check_circle,
                        label: 'سيحضر',
                        count: event.attendees.length,
                        color: Colors.green,
                      ),
                      
                      const SizedBox(width: 16),
                      
                      _buildAttendanceInfo(
                        icon: Icons.star,
                        label: 'مهتم',
                        count: event.interested.length,
                        color: Colors.orange,
                      ),
                      
                      const Spacer(),
                      
                      // زر الاستجابة
                      _buildResponseButton(event),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultEventCover(GroupEvent event) {
    return Container(
      height: 150,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getEventTypeColor(event.type),
            _getEventTypeColor(event.type).withValues(alpha: 0.7),
          ],
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getEventTypeIcon(event.type),
              size: 48,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            const SizedBox(height: 8),
            Text(
              _getEventTypeText(event.type),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceInfo({
    required IconData icon,
    required String label,
    required int count,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          '$count $label',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildResponseButton(GroupEvent event) {
    final userResponse = event.getUserResponse('current_user');
    
    return PopupMenuButton<String>(
      onSelected: (value) => _handleEventResponse(event, value),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: _getResponseColor(userResponse).withValues(alpha: 0.1),
          border: Border.all(color: _getResponseColor(userResponse)),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getResponseIcon(userResponse),
              size: 14,
              color: _getResponseColor(userResponse),
            ),
            const SizedBox(width: 4),
            Text(
              _getResponseText(userResponse),
              style: TextStyle(
                fontSize: 12,
                color: _getResponseColor(userResponse),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'going',
          child: ListTile(
            leading: Icon(Icons.check_circle, color: Colors.green),
            title: Text('سأحضر'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'interested',
          child: ListTile(
            leading: Icon(Icons.star, color: Colors.orange),
            title: Text('مهتم'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'not_going',
          child: ListTile(
            leading: Icon(Icons.cancel, color: Colors.red),
            title: Text('لن أحضر'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أحداث',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم إنشاء أي أحداث في هذه المجموعة بعد',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.group.isUserMember('current_user')) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showCreateEventDialog(),
              icon: const Icon(Icons.add),
              label: const Text('إنشاء حدث'),
            ),
          ],
        ],
      ),
    );
  }

  List<GroupEvent> _filterEvents(List<GroupEvent> events) {
    final now = DateTime.now();
    
    switch (_selectedFilter) {
      case 'upcoming':
        return events.where((e) => e.startDate.isAfter(now)).toList()
          ..sort((a, b) => a.startDate.compareTo(b.startDate));
      case 'past':
        return events.where((e) => e.endDate.isBefore(now)).toList()
          ..sort((a, b) => b.startDate.compareTo(a.startDate));
      default:
        return events..sort((a, b) => a.startDate.compareTo(b.startDate));
    }
  }

  Color _getEventStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.upcoming:
        return Colors.blue;
      case EventStatus.ongoing:
        return Colors.green;
      case EventStatus.completed:
        return Colors.grey;
      case EventStatus.cancelled:
        return Colors.red;
    }
  }

  String _getEventStatusText(EventStatus status) {
    switch (status) {
      case EventStatus.upcoming:
        return 'قادم';
      case EventStatus.ongoing:
        return 'جاري';
      case EventStatus.completed:
        return 'انتهى';
      case EventStatus.cancelled:
        return 'ملغي';
    }
  }

  Color _getEventTypeColor(EventType type) {
    switch (type) {
      case EventType.meeting:
        return Colors.blue;
      case EventType.social:
        return Colors.pink;
      case EventType.educational:
        return Colors.green;
      case EventType.entertainment:
        return Colors.purple;
      case EventType.sports:
        return Colors.orange;
      case EventType.business:
        return Colors.indigo;
      case EventType.other:
        return Colors.grey;
    }
  }

  IconData _getEventTypeIcon(EventType type) {
    switch (type) {
      case EventType.meeting:
        return Icons.meeting_room;
      case EventType.social:
        return Icons.people;
      case EventType.educational:
        return Icons.school;
      case EventType.entertainment:
        return Icons.celebration;
      case EventType.sports:
        return Icons.sports;
      case EventType.business:
        return Icons.business;
      case EventType.other:
        return Icons.event;
    }
  }

  String _getEventTypeText(EventType type) {
    switch (type) {
      case EventType.meeting:
        return 'اجتماع';
      case EventType.social:
        return 'اجتماعي';
      case EventType.educational:
        return 'تعليمي';
      case EventType.entertainment:
        return 'ترفيهي';
      case EventType.sports:
        return 'رياضي';
      case EventType.business:
        return 'عمل';
      case EventType.other:
        return 'أخرى';
    }
  }

  Color _getResponseColor(String? response) {
    switch (response) {
      case 'going':
        return Colors.green;
      case 'interested':
        return Colors.orange;
      case 'not_going':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getResponseIcon(String? response) {
    switch (response) {
      case 'going':
        return Icons.check_circle;
      case 'interested':
        return Icons.star;
      case 'not_going':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  String _getResponseText(String? response) {
    switch (response) {
      case 'going':
        return 'سأحضر';
      case 'interested':
        return 'مهتم';
      case 'not_going':
        return 'لن أحضر';
      default:
        return 'الاستجابة';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month - 1];
  }

  String _formatEventTime(GroupEvent event) {
    final startTime = '${event.startDate.hour.toString().padLeft(2, '0')}:${event.startDate.minute.toString().padLeft(2, '0')}';
    final endTime = '${event.endDate.hour.toString().padLeft(2, '0')}:${event.endDate.minute.toString().padLeft(2, '0')}';
    return '$startTime - $endTime';
  }

  void _showCreateEventDialog() {
    // TODO: فتح حوار إنشاء حدث
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حدث جديد')),
    );
  }

  void _openEventDetails(GroupEvent event) {
    // TODO: فتح تفاصيل الحدث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تفاصيل الحدث: ${event.title}')),
    );
  }

  void _handleEventResponse(GroupEvent event, String response) {
    // TODO: تنفيذ الاستجابة للحدث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم تسجيل استجابتك: ${_getResponseText(response)}')),
    );
  }
}
