import 'package:flutter/material.dart';
import '../models/post.dart';
import '../models/reaction_types.dart';

class PostReactionsDisplay extends StatelessWidget {
  final List<PostReaction> reactions;
  final VoidCallback? onTap;

  const PostReactionsDisplay({
    super.key,
    required this.reactions,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (reactions.isEmpty) return const SizedBox.shrink();

    final reactionCounts = _getReactionCounts();
    final topReactions = _getTopReactions(reactionCounts);
    final totalCount = reactions.length;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // عرض أيقونات التفاعلات الأكثر شيوعاً
            if (topReactions.isNotEmpty) ...[
              _buildReactionIcons(topReactions),
              const SizedBox(width: 8),
            ],
            
            // عدد التفاعلات
            Text(
              _formatReactionText(totalCount, topReactions),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            
            const Spacer(),
            
            // عدد التعليقات والمشاركات (إذا كانت متوفرة)
            Text(
              '${_getRandomCommentCount()} تعليق • ${_getRandomShareCount()} مشاركة',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Map<ReactionType, int> _getReactionCounts() {
    final counts = <ReactionType, int>{};
    
    for (final reaction in reactions) {
      try {
        final reactionType = ReactionType.values.firstWhere(
          (type) => type.name == reaction.type,
          orElse: () => ReactionType.like,
        );
        counts[reactionType] = (counts[reactionType] ?? 0) + 1;
      } catch (e) {
        // تجاهل التفاعلات غير الصحيحة
      }
    }
    
    return counts;
  }

  List<ReactionType> _getTopReactions(Map<ReactionType, int> counts) {
    final sortedReactions = counts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // عرض أكثر 3 تفاعلات شيوعاً
    return sortedReactions.take(3).map((e) => e.key).toList();
  }

  Widget _buildReactionIcons(List<ReactionType> topReactions) {
    return Stack(
      children: [
        for (int i = 0; i < topReactions.length; i++)
          Positioned(
            left: i * 16.0,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: ReactionData.getReaction(topReactions[i]).color,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1.5),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  ReactionData.getReaction(topReactions[i]).emoji,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ),
          ),
      ],
    );
  }

  String _formatReactionText(int totalCount, List<ReactionType> topReactions) {
    if (totalCount == 0) return '';
    
    if (totalCount == 1) {
      if (topReactions.isNotEmpty) {
        final reactionName = ReactionData.getReaction(topReactions.first).name;
        return '$reactionName واحد';
      }
      return 'تفاعل واحد';
    }
    
    if (topReactions.length == 1) {
      final reactionName = ReactionData.getReaction(topReactions.first).name;
      return '$totalCount $reactionName';
    }
    
    return '$totalCount تفاعل';
  }

  int _getRandomCommentCount() {
    // محاكاة عدد التعليقات
    return DateTime.now().millisecond % 50;
  }

  int _getRandomShareCount() {
    // محاكاة عدد المشاركات
    return DateTime.now().millisecond % 20;
  }
}

// Widget لعرض قائمة المتفاعلين
class ReactionsListDialog extends StatelessWidget {
  final List<PostReaction> reactions;

  const ReactionsListDialog({
    super.key,
    required this.reactions,
  });

  @override
  Widget build(BuildContext context) {
    final reactionCounts = _getReactionCounts();
    final reactionTypes = reactionCounts.keys.toList();

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                const Text(
                  'التفاعلات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // تبويبات التفاعلات
            if (reactionTypes.isNotEmpty) ...[
              SizedBox(
                height: 50,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: reactionTypes.length + 1,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return _buildReactionTab(
                        'الكل',
                        reactions.length,
                        null,
                        true,
                      );
                    }
                    
                    final reactionType = reactionTypes[index - 1];
                    final count = reactionCounts[reactionType] ?? 0;
                    
                    return _buildReactionTab(
                      ReactionData.getReaction(reactionType).emoji,
                      count,
                      reactionType,
                      false,
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 16),
            ],
            
            // قائمة المتفاعلين
            Expanded(
              child: ListView.builder(
                itemCount: reactions.length,
                itemBuilder: (context, index) {
                  final reaction = reactions[index];
                  return _buildReactionItem(reaction);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Map<ReactionType, int> _getReactionCounts() {
    final counts = <ReactionType, int>{};
    
    for (final reaction in reactions) {
      try {
        final reactionType = ReactionType.values.firstWhere(
          (type) => type.name == reaction.type,
          orElse: () => ReactionType.like,
        );
        counts[reactionType] = (counts[reactionType] ?? 0) + 1;
      } catch (e) {
        // تجاهل التفاعلات غير الصحيحة
      }
    }
    
    return counts;
  }

  Widget _buildReactionTab(String label, int count, ReactionType? type, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withValues(alpha: 0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: isSelected ? Colors.blue : Colors.grey[700],
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '$count',
            style: TextStyle(
              fontSize: 14,
              color: isSelected ? Colors.blue : Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReactionItem(PostReaction reaction) {
    final reactionType = ReactionType.values.firstWhere(
      (type) => type.name == reaction.type,
      orElse: () => ReactionType.like,
    );
    final reactionData = ReactionData.getReaction(reactionType);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.grey[300],
        child: Text(
          reaction.userId == 'current_user' ? 'أ' : reaction.userId[0].toUpperCase(),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      title: Text(
        reaction.userId == 'current_user' ? 'أنت' : 'مستخدم ${reaction.userId}',
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        _formatTime(reaction.timestamp),
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      trailing: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: reactionData.color,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            reactionData.emoji,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
