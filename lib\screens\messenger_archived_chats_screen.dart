import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../providers/messenger_provider.dart';
import '../models/chat.dart';
import '../models/message.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import 'messenger_chat_screen.dart';

class MessengerArchivedChatsScreen extends StatefulWidget {
  const MessengerArchivedChatsScreen({super.key});

  @override
  State<MessengerArchivedChatsScreen> createState() => _MessengerArchivedChatsScreenState();
}

class _MessengerArchivedChatsScreenState extends State<MessengerArchivedChatsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: const Text('المحادثات المؤرشفة'),
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'unarchive_all',
                child: Row(
                  children: [
                    Icon(Icons.unarchive),
                    SizedBox(width: 8),
                    Text('إلغاء أرشفة الكل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete_all',
                child: Row(
                  children: [
                    Icon(Icons.delete_forever, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف الكل', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<MessengerProvider>(
        builder: (context, messengerProvider, child) {
          final archivedChats = messengerProvider.archivedChats;

          if (messengerProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (archivedChats.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: archivedChats.length,
            itemBuilder: (context, index) {
              final chat = archivedChats[index];
              return _buildArchivedChatItem(chat, messengerProvider);
            },
          );
        },
      ),
    );
  }

  Widget _buildArchivedChatItem(Chat chat, MessengerProvider messengerProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Dismissible(
        key: Key(chat.id),
        background: Container(
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(12),
          ),
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 20),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.unarchive, color: Colors.white, size: 28),
              SizedBox(height: 4),
              Text(
                'إلغاء الأرشفة',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        secondaryBackground: Container(
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(12),
          ),
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(left: 20),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.delete_forever, color: Colors.white, size: 28),
              SizedBox(height: 4),
              Text(
                'حذف نهائي',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        confirmDismiss: (direction) async {
          if (direction == DismissDirection.endToStart) {
            // حذف نهائي
            return await _showDeleteConfirmDialog(chat);
          } else {
            // إلغاء الأرشفة
            return true;
          }
        },
        onDismissed: (direction) {
          if (direction == DismissDirection.endToStart) {
            // حذف نهائي
            messengerProvider.deleteArchivedChat(chat.id);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('تم حذف محادثة ${chat.otherUser.name} نهائياً')),
            );
          } else {
            // إلغاء الأرشفة
            messengerProvider.unarchiveChat(chat.id);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('تم إلغاء أرشفة محادثة ${chat.otherUser.name}')),
            );
          }
        },
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          leading: Stack(
            children: [
              SmartAvatarWithText(
                user: chat.otherUser,
                radius: 28,
              ),
              
              // أيقونة الأرشيف
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                  child: const Icon(
                    Icons.archive,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          
          title: Row(
            children: [
              Expanded(
                child: Text(
                  chat.type == ChatType.group 
                      ? (chat.groupName ?? chat.otherUser.name)
                      : chat.otherUser.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              // وقت آخر رسالة
              if (chat.lastMessage != null)
                Text(
                  timeago.format(chat.lastMessage!.timestamp, locale: 'ar'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
            ],
          ),
          
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Row(
                children: [
                  // أيقونة نوع الرسالة
                  if (chat.lastMessage != null) ...[
                    if (chat.lastMessage!.senderId == 'current_user')
                      Icon(
                        chat.lastMessage!.status == MessageStatus.read
                            ? Icons.done_all
                            : Icons.done,
                        size: 16,
                        color: chat.lastMessage!.status == MessageStatus.read
                            ? Colors.blue
                            : Colors.grey,
                      ),
                    const SizedBox(width: 4),
                  ],
                  
                  Expanded(
                    child: Text(
                      chat.lastMessage?.content ?? 'لا توجد رسائل',
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 4),
              
              // تلميح السحب
              Text(
                'اسحب يميناً لإلغاء الأرشفة • اسحب يساراً للحذف',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[400],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          
          onTap: () => _openArchivedChat(chat),
          onLongPress: () => _showArchivedChatActions(chat, messengerProvider),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.archive_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد محادثات مؤرشفة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'المحادثات المؤرشفة ستظهر هنا',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _openArchivedChat(Chat chat) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MessengerChatScreen(chat: chat),
      ),
    );
  }

  void _showArchivedChatActions(Chat chat, MessengerProvider messengerProvider) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.unarchive, color: Colors.green),
              title: const Text('إلغاء الأرشفة'),
              onTap: () {
                Navigator.pop(context);
                messengerProvider.unarchiveChat(chat.id);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('تم إلغاء أرشفة محادثة ${chat.otherUser.name}')),
                );
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              title: const Text('حذف نهائي', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmDialog(chat).then((confirmed) {
                  if (confirmed == true && mounted) {
                    messengerProvider.deleteArchivedChat(chat.id);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('تم حذف محادثة ${chat.otherUser.name} نهائياً')),
                    );
                  }
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<bool?> _showDeleteConfirmDialog(Chat chat) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المحادثة نهائياً'),
        content: Text('هل تريد حذف المحادثة مع ${chat.otherUser.name} نهائياً؟\n\nلن تتمكن من استرداد هذه المحادثة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف نهائي', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    
    switch (action) {
      case 'unarchive_all':
        _showUnarchiveAllDialog(messengerProvider);
        break;
      case 'delete_all':
        _showDeleteAllDialog(messengerProvider);
        break;
    }
  }

  void _showUnarchiveAllDialog(MessengerProvider messengerProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء أرشفة جميع المحادثات'),
        content: const Text('هل تريد إلغاء أرشفة جميع المحادثات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              messengerProvider.unarchiveAllChats();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إلغاء أرشفة جميع المحادثات')),
              );
            },
            child: const Text('إلغاء الأرشفة'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAllDialog(MessengerProvider messengerProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع المحادثات المؤرشفة'),
        content: const Text('هل تريد حذف جميع المحادثات المؤرشفة نهائياً؟\n\nلن تتمكن من استرداد هذه المحادثات.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              messengerProvider.deleteAllArchivedChats();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف جميع المحادثات المؤرشفة')),
              );
            },
            child: const Text('حذف نهائي', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
