import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/live_stream.dart';
import '../models/user.dart';
import '../models/reaction_types.dart';
import '../services/live_stream_service.dart';

class LiveStreamProvider with ChangeNotifier {
  final LiveStreamService _liveStreamService = LiveStreamService();
  
  // حالة البثوث
  List<LiveStream> _activeStreams = [];
  LiveStream? _currentStream;
  bool _isLoading = false;
  String? _errorMessage;
  
  // حالة البث الحالي
  bool _isStreaming = false;
  bool _isCameraEnabled = true;
  bool _isMicEnabled = true;
  bool _isFrontCamera = true;
  LiveStreamQuality _streamQuality = LiveStreamQuality.auto;
  
  // تعليقات وتفاعلات البث الحالي
  List<LiveStreamComment> _currentComments = [];
  List<LiveStreamReaction> _currentReactions = [];
  List<LiveStreamViewer> _currentViewers = [];
  
  // Subscriptions للتحديثات الفورية
  StreamSubscription<List<LiveStream>>? _activeStreamsSubscription;
  StreamSubscription<LiveStream>? _streamUpdatesSubscription;
  StreamSubscription<LiveStreamComment>? _commentsSubscription;
  StreamSubscription<LiveStreamReaction>? _reactionsSubscription;
  StreamSubscription<List<LiveStreamViewer>>? _viewersSubscription;
  
  // Getters
  List<LiveStream> get activeStreams => _activeStreams;
  LiveStream? get currentStream => _currentStream;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  
  bool get isStreaming => _isStreaming;
  bool get isCameraEnabled => _isCameraEnabled;
  bool get isMicEnabled => _isMicEnabled;
  bool get isFrontCamera => _isFrontCamera;
  LiveStreamQuality get streamQuality => _streamQuality;
  
  List<LiveStreamComment> get currentComments => _currentComments;
  List<LiveStreamReaction> get currentReactions => _currentReactions;
  List<LiveStreamViewer> get currentViewers => _currentViewers;
  
  LiveStreamProvider() {
    _initializeStreams();
    _setupStreamListeners();
  }
  
  // تهيئة البثوث
  Future<void> _initializeStreams() async {
    await loadActiveStreams();
  }
  
  // إعداد المستمعين للتحديثات الفورية
  void _setupStreamListeners() {
    // الاستماع للبثوث النشطة
    _activeStreamsSubscription = _liveStreamService.activeStreamsStream.listen(
      (streams) {
        _activeStreams = streams;
        notifyListeners();
      },
    );
    
    // الاستماع لتحديثات البث
    _streamUpdatesSubscription = _liveStreamService.streamUpdatesStream.listen(
      (stream) {
        if (_currentStream?.id == stream.id) {
          _currentStream = stream;
          notifyListeners();
        }
        
        // تحديث البث في القائمة النشطة
        final index = _activeStreams.indexWhere((s) => s.id == stream.id);
        if (index != -1) {
          _activeStreams[index] = stream;
          notifyListeners();
        }
      },
    );
    
    // الاستماع للتعليقات
    _commentsSubscription = _liveStreamService.commentsStream.listen(
      (comment) {
        if (_currentStream != null) {
          _currentComments.add(comment);
          notifyListeners();
        }
      },
    );
    
    // الاستماع للتفاعلات
    _reactionsSubscription = _liveStreamService.reactionsStream.listen(
      (reaction) {
        if (_currentStream != null) {
          _currentReactions.add(reaction);
          // إزالة التفاعلات القديمة (الاحتفاظ بآخر 50)
          if (_currentReactions.length > 50) {
            _currentReactions.removeAt(0);
          }
          notifyListeners();
        }
      },
    );
    
    // الاستماع للمشاهدين
    _viewersSubscription = _liveStreamService.viewersStream.listen(
      (viewers) {
        if (_currentStream != null) {
          _currentViewers = viewers;
          notifyListeners();
        }
      },
    );
  }
  
  // تحميل البثوث النشطة
  Future<void> loadActiveStreams() async {
    try {
      _setLoading(true);
      _activeStreams = await _liveStreamService.getActiveStreams();
      _clearError();
    } catch (e) {
      _setError('خطأ في تحميل البثوث: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // بدء بث مباشر جديد
  Future<LiveStream?> startLiveStream({
    required String streamerId,
    required User streamer,
    required String title,
    String? description,
    LiveStreamPrivacy privacy = LiveStreamPrivacy.public,
  }) async {
    try {
      _setLoading(true);
      
      final stream = await _liveStreamService.startLiveStream(
        streamerId: streamerId,
        streamer: streamer,
        title: title,
        description: description,
        privacy: privacy,
        quality: _streamQuality,
      );
      
      _currentStream = stream;
      _isStreaming = true;
      _currentComments.clear();
      _currentReactions.clear();
      _currentViewers.clear();
      
      _clearError();
      return stream;
    } catch (e) {
      _setError('خطأ في بدء البث: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }
  
  // إنهاء البث المباشر
  Future<void> endLiveStream() async {
    if (_currentStream == null) return;
    
    try {
      _setLoading(true);
      
      final endedStream = await _liveStreamService.endLiveStream(_currentStream!.id);
      
      _currentStream = endedStream;
      _isStreaming = false;
      
      _clearError();
    } catch (e) {
      _setError('خطأ في إنهاء البث: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // الانضمام لمشاهدة بث
  Future<void> joinStream(String streamId, String userId, String userName) async {
    try {
      await _liveStreamService.joinStream(streamId, userId, userName);
      
      // تحديث البث الحالي إذا كان نفس البث
      if (_currentStream?.id == streamId) {
        _currentStream = await _liveStreamService.getStream(streamId);
        notifyListeners();
      }
      
      _clearError();
    } catch (e) {
      _setError('خطأ في الانضمام للبث: $e');
    }
  }
  
  // مغادرة البث
  Future<void> leaveStream(String streamId, String userId) async {
    try {
      await _liveStreamService.leaveStream(streamId, userId);
      
      // مسح البث الحالي إذا كان نفس البث
      if (_currentStream?.id == streamId) {
        _currentStream = null;
        _currentComments.clear();
        _currentReactions.clear();
        _currentViewers.clear();
        notifyListeners();
      }
      
      _clearError();
    } catch (e) {
      _setError('خطأ في مغادرة البث: $e');
    }
  }
  
  // إضافة تعليق
  Future<void> addComment({
    required String streamId,
    required String userId,
    required String userName,
    String? userAvatar,
    required String content,
  }) async {
    try {
      await _liveStreamService.addComment(
        streamId: streamId,
        userId: userId,
        userName: userName,
        userAvatar: userAvatar,
        content: content,
      );
      _clearError();
    } catch (e) {
      _setError('خطأ في إضافة التعليق: $e');
    }
  }
  
  // إضافة تفاعل
  Future<void> addReaction({
    required String streamId,
    required String userId,
    required String userName,
    required ReactionType reactionType,
  }) async {
    try {
      await _liveStreamService.addReaction(
        streamId: streamId,
        userId: userId,
        userName: userName,
        reactionType: reactionType,
      );
      _clearError();
    } catch (e) {
      _setError('خطأ في إضافة التفاعل: $e');
    }
  }
  
  // تحديث إعدادات البث
  Future<void> updateStreamSettings({
    bool? isCameraEnabled,
    bool? isMicEnabled,
    bool? isFrontCamera,
    LiveStreamQuality? quality,
  }) async {
    if (_currentStream == null) return;
    
    try {
      // تحديث الإعدادات المحلية
      _isCameraEnabled = isCameraEnabled ?? _isCameraEnabled;
      _isMicEnabled = isMicEnabled ?? _isMicEnabled;
      _isFrontCamera = isFrontCamera ?? _isFrontCamera;
      _streamQuality = quality ?? _streamQuality;
      
      // تحديث البث
      _currentStream = await _liveStreamService.updateStreamSettings(
        streamId: _currentStream!.id,
        isCameraEnabled: _isCameraEnabled,
        isMicEnabled: _isMicEnabled,
        isFrontCamera: _isFrontCamera,
        quality: _streamQuality,
      );
      
      notifyListeners();
      _clearError();
    } catch (e) {
      _setError('خطأ في تحديث إعدادات البث: $e');
    }
  }
  
  // تبديل الكاميرا
  Future<void> toggleCamera() async {
    await updateStreamSettings(isCameraEnabled: !_isCameraEnabled);
  }
  
  // تبديل المايكروفون
  Future<void> toggleMicrophone() async {
    await updateStreamSettings(isMicEnabled: !_isMicEnabled);
  }
  
  // تبديل الكاميرا الأمامية/الخلفية
  Future<void> switchCamera() async {
    await updateStreamSettings(isFrontCamera: !_isFrontCamera);
  }
  
  // تغيير جودة البث
  Future<void> changeQuality(LiveStreamQuality quality) async {
    await updateStreamSettings(quality: quality);
  }
  
  // تحديد البث الحالي للمشاهدة
  Future<void> setCurrentStream(String streamId) async {
    try {
      _setLoading(true);
      _currentStream = await _liveStreamService.getStream(streamId);
      
      if (_currentStream != null) {
        _currentComments = List.from(_currentStream!.comments);
        _currentReactions = List.from(_currentStream!.reactions);
        _currentViewers = List.from(_currentStream!.viewers);
      }
      
      _clearError();
    } catch (e) {
      _setError('خطأ في تحميل البث: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // مسح البث الحالي
  void clearCurrentStream() {
    _currentStream = null;
    _currentComments.clear();
    _currentReactions.clear();
    _currentViewers.clear();
    notifyListeners();
  }
  
  // دوال مساعدة لإدارة الحالة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  @override
  void dispose() {
    // إلغاء الاشتراكات
    _activeStreamsSubscription?.cancel();
    _streamUpdatesSubscription?.cancel();
    _commentsSubscription?.cancel();
    _reactionsSubscription?.cancel();
    _viewersSubscription?.cancel();
    
    // تنظيف الخدمة
    _liveStreamService.dispose();
    
    super.dispose();
  }
}
