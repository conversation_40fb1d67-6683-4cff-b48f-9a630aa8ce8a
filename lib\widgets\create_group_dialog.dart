import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/social_provider.dart';
import '../models/group.dart';
import '../theme/app_theme.dart';

class CreateGroupDialog extends StatefulWidget {
  const CreateGroupDialog({super.key});

  @override
  State<CreateGroupDialog> createState() => _CreateGroupDialogState();
}

class _CreateGroupDialogState extends State<CreateGroupDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _tagsController = TextEditingController();
  
  GroupPrivacy _selectedPrivacy = GroupPrivacy.public;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // رأس الحوار
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'إنشاء مجموعة جديدة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: _isLoading ? null : _createGroup,
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('إنشاء'),
                ),
              ],
            ),
          ),

          // محتوى النموذج
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // صورة المجموعة (محاكاة)
                    Center(
                      child: GestureDetector(
                        onTap: () => _selectGroupImage(),
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.camera_alt,
                                size: 32,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'إضافة صورة',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // اسم المجموعة
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المجموعة *',
                        hintText: 'أدخل اسم المجموعة',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال اسم المجموعة';
                        }
                        if (value.trim().length < 3) {
                          return 'يجب أن يكون الاسم 3 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // وصف المجموعة
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'وصف المجموعة *',
                        hintText: 'اكتب وصفاً مختصراً عن المجموعة',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال وصف المجموعة';
                        }
                        if (value.trim().length < 10) {
                          return 'يجب أن يكون الوصف 10 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // خصوصية المجموعة
                    const Text(
                      'خصوصية المجموعة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    Column(
                      children: GroupPrivacy.values.map((privacy) {
                        return RadioListTile<GroupPrivacy>(
                          title: Text(_getPrivacyTitle(privacy)),
                          subtitle: Text(_getPrivacyDescription(privacy)),
                          value: privacy,
                          groupValue: _selectedPrivacy,
                          onChanged: (value) {
                            setState(() {
                              _selectedPrivacy = value!;
                            });
                          },
                          activeColor: AppTheme.primaryColor,
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 16),

                    // الموقع (اختياري)
                    TextFormField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        labelText: 'الموقع (اختياري)',
                        hintText: 'أدخل موقع المجموعة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // التاغات
                    TextFormField(
                      controller: _tagsController,
                      decoration: const InputDecoration(
                        labelText: 'التاغات (اختياري)',
                        hintText: 'أدخل التاغات مفصولة بفواصل',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.tag),
                        helperText: 'مثال: تقنية, برمجة, تطوير',
                      ),
                    ),

                    const SizedBox(height: 24),

                    // معلومات إضافية
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withOpacity(0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Colors.blue[700],
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'معلومات مهمة',
                                style: TextStyle(
                                  color: Colors.blue[700],
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '• ستصبح مديراً للمجموعة تلقائياً\n'
                            '• يمكنك دعوة الأعضاء بعد الإنشاء\n'
                            '• يمكن تعديل الإعدادات لاحقاً',
                            style: TextStyle(
                              color: Colors.blue[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getPrivacyTitle(GroupPrivacy privacy) {
    switch (privacy) {
      case GroupPrivacy.public:
        return 'عامة';
      case GroupPrivacy.private:
        return 'خاصة';
      case GroupPrivacy.secret:
        return 'سرية';
    }
  }

  String _getPrivacyDescription(GroupPrivacy privacy) {
    switch (privacy) {
      case GroupPrivacy.public:
        return 'يمكن لأي شخص رؤية المجموعة والانضمام إليها';
      case GroupPrivacy.private:
        return 'يمكن لأي شخص رؤية المجموعة لكن يحتاج موافقة للانضمام';
      case GroupPrivacy.secret:
        return 'لا يمكن العثور على المجموعة إلا بالدعوة';
    }
  }

  void _selectGroupImage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة اختيار الصورة قريباً!')),
    );
  }

  void _createGroup() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      await Provider.of<SocialProvider>(context, listen: false).createGroup(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        privacy: _selectedPrivacy,
        tags: tags,
        location: _locationController.text.trim().isEmpty 
            ? null 
            : _locationController.text.trim(),
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء المجموعة بنجاح!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء إنشاء المجموعة')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
