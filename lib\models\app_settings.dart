enum ThemeMode {
  light,
  dark,
  system,
}

enum FontSize {
  small,
  medium,
  large,
  extraLarge,
}

enum AutoDownloadSetting {
  never,
  wifiOnly,
  always,
}

enum PrivacySetting {
  everyone,
  friends,
  nobody,
}

class AppSettings {
  // إعدادات المظهر
  final ThemeMode themeMode;
  final FontSize fontSize;
  
  // إعدادات الخصوصية
  final PrivacySetting lastSeenPrivacy;
  final PrivacySetting profilePhotoPrivacy;
  final PrivacySetting statusPrivacy;
  final bool readReceiptsEnabled;
  
  // إعدادات التحميل
  final AutoDownloadSetting autoDownloadPhotos;
  final AutoDownloadSetting autoDownloadVideos;
  final AutoDownloadSetting autoDownloadDocuments;
  
  // إعدادات التخزين
  final bool cacheEnabled;
  final int maxCacheSize; // بالميجابايت
  
  // إعدادات النسخ الاحتياطي
  final bool backupEnabled;
  final String? backupEmail;
  final DateTime? lastBackupDate;
  
  // إعدادات أخرى
  final String language;
  final bool notificationsEnabled;
  final List<String> blockedUsers;

  const AppSettings({
    this.themeMode = ThemeMode.system,
    this.fontSize = FontSize.medium,
    this.lastSeenPrivacy = PrivacySetting.everyone,
    this.profilePhotoPrivacy = PrivacySetting.everyone,
    this.statusPrivacy = PrivacySetting.everyone,
    this.readReceiptsEnabled = true,
    this.autoDownloadPhotos = AutoDownloadSetting.wifiOnly,
    this.autoDownloadVideos = AutoDownloadSetting.wifiOnly,
    this.autoDownloadDocuments = AutoDownloadSetting.wifiOnly,
    this.cacheEnabled = true,
    this.maxCacheSize = 500,
    this.backupEnabled = false,
    this.backupEmail,
    this.lastBackupDate,
    this.language = 'ar',
    this.notificationsEnabled = true,
    this.blockedUsers = const [],
  });

  AppSettings copyWith({
    ThemeMode? themeMode,
    FontSize? fontSize,
    PrivacySetting? lastSeenPrivacy,
    PrivacySetting? profilePhotoPrivacy,
    PrivacySetting? statusPrivacy,
    bool? readReceiptsEnabled,
    AutoDownloadSetting? autoDownloadPhotos,
    AutoDownloadSetting? autoDownloadVideos,
    AutoDownloadSetting? autoDownloadDocuments,
    bool? cacheEnabled,
    int? maxCacheSize,
    bool? backupEnabled,
    String? backupEmail,
    DateTime? lastBackupDate,
    String? language,
    bool? notificationsEnabled,
    List<String>? blockedUsers,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      fontSize: fontSize ?? this.fontSize,
      lastSeenPrivacy: lastSeenPrivacy ?? this.lastSeenPrivacy,
      profilePhotoPrivacy: profilePhotoPrivacy ?? this.profilePhotoPrivacy,
      statusPrivacy: statusPrivacy ?? this.statusPrivacy,
      readReceiptsEnabled: readReceiptsEnabled ?? this.readReceiptsEnabled,
      autoDownloadPhotos: autoDownloadPhotos ?? this.autoDownloadPhotos,
      autoDownloadVideos: autoDownloadVideos ?? this.autoDownloadVideos,
      autoDownloadDocuments: autoDownloadDocuments ?? this.autoDownloadDocuments,
      cacheEnabled: cacheEnabled ?? this.cacheEnabled,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      backupEnabled: backupEnabled ?? this.backupEnabled,
      backupEmail: backupEmail ?? this.backupEmail,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
      language: language ?? this.language,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      blockedUsers: blockedUsers ?? this.blockedUsers,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.name,
      'fontSize': fontSize.name,
      'lastSeenPrivacy': lastSeenPrivacy.name,
      'profilePhotoPrivacy': profilePhotoPrivacy.name,
      'statusPrivacy': statusPrivacy.name,
      'readReceiptsEnabled': readReceiptsEnabled,
      'autoDownloadPhotos': autoDownloadPhotos.name,
      'autoDownloadVideos': autoDownloadVideos.name,
      'autoDownloadDocuments': autoDownloadDocuments.name,
      'cacheEnabled': cacheEnabled,
      'maxCacheSize': maxCacheSize,
      'backupEnabled': backupEnabled,
      'backupEmail': backupEmail,
      'lastBackupDate': lastBackupDate?.toIso8601String(),
      'language': language,
      'notificationsEnabled': notificationsEnabled,
      'blockedUsers': blockedUsers,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      themeMode: ThemeMode.values.firstWhere(
        (e) => e.name == json['themeMode'],
        orElse: () => ThemeMode.system,
      ),
      fontSize: FontSize.values.firstWhere(
        (e) => e.name == json['fontSize'],
        orElse: () => FontSize.medium,
      ),
      lastSeenPrivacy: PrivacySetting.values.firstWhere(
        (e) => e.name == json['lastSeenPrivacy'],
        orElse: () => PrivacySetting.everyone,
      ),
      profilePhotoPrivacy: PrivacySetting.values.firstWhere(
        (e) => e.name == json['profilePhotoPrivacy'],
        orElse: () => PrivacySetting.everyone,
      ),
      statusPrivacy: PrivacySetting.values.firstWhere(
        (e) => e.name == json['statusPrivacy'],
        orElse: () => PrivacySetting.everyone,
      ),
      readReceiptsEnabled: json['readReceiptsEnabled'] ?? true,
      autoDownloadPhotos: AutoDownloadSetting.values.firstWhere(
        (e) => e.name == json['autoDownloadPhotos'],
        orElse: () => AutoDownloadSetting.wifiOnly,
      ),
      autoDownloadVideos: AutoDownloadSetting.values.firstWhere(
        (e) => e.name == json['autoDownloadVideos'],
        orElse: () => AutoDownloadSetting.wifiOnly,
      ),
      autoDownloadDocuments: AutoDownloadSetting.values.firstWhere(
        (e) => e.name == json['autoDownloadDocuments'],
        orElse: () => AutoDownloadSetting.wifiOnly,
      ),
      cacheEnabled: json['cacheEnabled'] ?? true,
      maxCacheSize: json['maxCacheSize'] ?? 500,
      backupEnabled: json['backupEnabled'] ?? false,
      backupEmail: json['backupEmail'],
      lastBackupDate: json['lastBackupDate'] != null
          ? DateTime.parse(json['lastBackupDate'])
          : null,
      language: json['language'] ?? 'ar',
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      blockedUsers: List<String>.from(json['blockedUsers'] ?? []),
    );
  }

  // دوال مساعدة للحصول على النصوص المترجمة
  String get themeModeDisplayName {
    switch (themeMode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
      case ThemeMode.system:
        return 'تلقائي';
    }
  }

  String get fontSizeDisplayName {
    switch (fontSize) {
      case FontSize.small:
        return 'صغير';
      case FontSize.medium:
        return 'متوسط';
      case FontSize.large:
        return 'كبير';
      case FontSize.extraLarge:
        return 'كبير جداً';
    }
  }

  String get privacyDisplayName {
    switch (lastSeenPrivacy) {
      case PrivacySetting.everyone:
        return 'الجميع';
      case PrivacySetting.friends:
        return 'الأصدقاء فقط';
      case PrivacySetting.nobody:
        return 'لا أحد';
    }
  }

  String get autoDownloadDisplayName {
    switch (autoDownloadPhotos) {
      case AutoDownloadSetting.never:
        return 'أبداً';
      case AutoDownloadSetting.wifiOnly:
        return 'Wi-Fi فقط';
      case AutoDownloadSetting.always:
        return 'دائماً';
    }
  }

  double get fontSizeMultiplier {
    switch (fontSize) {
      case FontSize.small:
        return 0.85;
      case FontSize.medium:
        return 1.0;
      case FontSize.large:
        return 1.15;
      case FontSize.extraLarge:
        return 1.3;
    }
  }

  // حساب حجم الكاش المستخدم (محاكاة)
  double get usedCacheSize {
    // محاكاة حجم الكاش المستخدم
    return (maxCacheSize * 0.6); // 60% من الحد الأقصى
  }

  String get formattedCacheSize {
    if (usedCacheSize < 1024) {
      return '${usedCacheSize.toStringAsFixed(1)} MB';
    } else {
      return '${(usedCacheSize / 1024).toStringAsFixed(1)} GB';
    }
  }

  String get formattedMaxCacheSize {
    if (maxCacheSize < 1024) {
      return '$maxCacheSize MB';
    } else {
      return '${(maxCacheSize / 1024).toStringAsFixed(1)} GB';
    }
  }

  String? get formattedLastBackupDate {
    if (lastBackupDate == null) return null;
    
    final now = DateTime.now();
    final difference = now.difference(lastBackupDate!);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
