import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../providers/social_provider.dart';
import '../widgets/advanced_video_player.dart';
import 'package:timeago/timeago.dart' as timeago;

class VideosScreen extends StatefulWidget {
  const VideosScreen({super.key});

  @override
  State<VideosScreen> createState() => _VideosScreenState();
}

class _VideosScreenState extends State<VideosScreen> {
  @override
  void initState() {
    super.initState();
    timeago.setLocaleMessages('ar', timeago.ArMessages());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'الفيديوهات',
          style: TextStyle(
            color: Colors.black,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black),
            onPressed: () {},
          ),
        ],
      ),
      body: Consumer<SocialProvider>(
        builder: (context, socialProvider, child) {
          final allVideos = _getAllVideos(socialProvider);

          if (allVideos.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.video_library_outlined,
                    size: 80,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد فيديوهات',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'ستظهر هنا جميع الفيديوهات المنشورة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: allVideos.length,
            itemBuilder: (context, index) {
              final post = allVideos[index];
              return VideoCard(post: post);
            },
          );
        },
      ),
    );
  }

  List<Post> _getAllVideos(SocialProvider socialProvider) {
    final List<Post> allVideos = [];

    for (final post in socialProvider.posts) {
      if (post.media.any((media) => media.type == PostType.video)) {
        allVideos.add(post);
      }
    }

    // فيديوهات من المجموعات (سيتم إضافتها لاحقاً)
    // for (final group in socialProvider.groups) {
    //   for (final post in group.posts) {
    //     if (post.media.any((media) => media.type == PostType.video)) {
    //       allVideos.add(post);
    //     }
    //   }
    // }

    allVideos.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return allVideos;
  }
}

class VideoCard extends StatefulWidget {
  final Post post;

  const VideoCard({super.key, required this.post});

  @override
  State<VideoCard> createState() => _VideoCardState();
}

class _VideoCardState extends State<VideoCard> {
  @override
  Widget build(BuildContext context) {
    final videoMedia = widget.post.media.firstWhere(
      (media) => media.type == PostType.video,
    );

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          AdvancedVideoPlayer(
            post: widget.post,
            videoMedia: videoMedia,
          ),
          if (widget.post.content.isNotEmpty) _buildContent(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey[300],
            child: Text(
              _getAuthorName(widget.post.authorId)[0],
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black54,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getAuthorName(widget.post.authorId),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  timeago.format(widget.post.timestamp, locale: 'ar'),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

        ],
      ),
    );
  }



  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      child: Text(
        widget.post.content,
        style: const TextStyle(fontSize: 14),
      ),
    );
  }

  // دوال المساعدة
  String _getAuthorName(String authorId) {
    switch (authorId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }
}