import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import '../models/connectivity_status.dart';

class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  // Cache storage
  final Map<String, CachedData> _memoryCache = {};
  
  // Configuration
  static const Duration _defaultTTL = Duration(hours: 1);
  static const int _maxMemoryCacheSize = 100;
  static const String _diskCacheKey = 'disk_cache';
  
  // Cache statistics
  int _hits = 0;
  int _misses = 0;
  
  // Getters
  double get hitRate => _hits + _misses > 0 ? _hits / (_hits + _misses) : 0.0;
  int get cacheSize => _memoryCache.length;
  
  // Initialize cache service
  Future<void> initialize() async {
    try {
      await _loadDiskCache();
      await _cleanExpiredEntries();
      print('CacheService initialized successfully');
    } catch (e) {
      print('Error initializing CacheService: $e');
    }
  }

  // Get cached data
  Future<T?> get<T>(
    String key, {
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      // Check memory cache first
      final cachedData = _memoryCache[key];
      
      if (cachedData != null) {
        if (!cachedData.isExpired) {
          _hits++;
          if (fromJson != null) {
            return fromJson(cachedData.data);
          }
          return cachedData.data as T?;
        } else {
          // Remove expired entry
          _memoryCache.remove(key);
        }
      }

      // Check disk cache
      final diskData = await _getDiskCache(key);
      if (diskData != null && !diskData.isExpired) {
        // Move to memory cache
        _memoryCache[key] = diskData;
        _ensureMemoryCacheSize();
        
        _hits++;
        if (fromJson != null) {
          return fromJson(diskData.data);
        }
        return diskData.data as T?;
      }

      _misses++;
      return null;
    } catch (e) {
      print('Error getting cached data for key $key: $e');
      _misses++;
      return null;
    }
  }

  // Set cached data
  Future<void> set<T>(
    String key,
    T data, {
    Duration? ttl,
    String? etag,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    try {
      final Map<String, dynamic> dataMap;
      
      if (toJson != null) {
        dataMap = toJson(data);
      } else if (data is Map<String, dynamic>) {
        dataMap = data;
      } else {
        // Try to convert to JSON
        dataMap = {'data': data};
      }

      final cachedData = CachedData(
        key: key,
        data: dataMap,
        cachedAt: DateTime.now(),
        ttl: ttl ?? _defaultTTL,
        etag: etag,
      );

      // Store in memory cache
      _memoryCache[key] = cachedData;
      _ensureMemoryCacheSize();

      // Store in disk cache
      await _setDiskCache(key, cachedData);
    } catch (e) {
      print('Error setting cached data for key $key: $e');
    }
  }

  // Remove cached data
  Future<void> remove(String key) async {
    try {
      _memoryCache.remove(key);
      await _removeDiskCache(key);
    } catch (e) {
      print('Error removing cached data for key $key: $e');
    }
  }

  // Clear all cache
  Future<void> clear() async {
    try {
      _memoryCache.clear();
      await _clearDiskCache();
      _hits = 0;
      _misses = 0;
    } catch (e) {
      print('Error clearing cache: $e');
    }
  }

  // Check if key exists and is not expired
  Future<bool> exists(String key) async {
    try {
      final cachedData = _memoryCache[key];
      if (cachedData != null && !cachedData.isExpired) {
        return true;
      }

      final diskData = await _getDiskCache(key);
      return diskData != null && !diskData.isExpired;
    } catch (e) {
      print('Error checking cache existence for key $key: $e');
      return false;
    }
  }

  // Get cache info
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final diskCacheSize = await _getDiskCacheSize();
      
      return {
        'memoryCache': {
          'size': _memoryCache.length,
          'maxSize': _maxMemoryCacheSize,
        },
        'diskCache': {
          'size': diskCacheSize,
        },
        'statistics': {
          'hits': _hits,
          'misses': _misses,
          'hitRate': hitRate,
        },
        'entries': _memoryCache.keys.toList(),
      };
    } catch (e) {
      print('Error getting cache info: $e');
      return {};
    }
  }

  // Clean expired entries
  Future<void> cleanExpiredEntries() async {
    await _cleanExpiredEntries();
  }

  // Preload data for offline use
  Future<void> preloadData(Map<String, dynamic> dataMap) async {
    try {
      for (final entry in dataMap.entries) {
        await set(entry.key, entry.value);
      }
    } catch (e) {
      print('Error preloading data: $e');
    }
  }

  // Get cached data with fallback
  Future<T> getWithFallback<T>(
    String key,
    Future<T> Function() fallback, {
    Duration? ttl,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    try {
      // Try to get from cache first
      final cachedData = await get<T>(key, fromJson: fromJson);
      if (cachedData != null) {
        return cachedData;
      }

      // Fallback to network/computation
      final freshData = await fallback();
      
      // Cache the fresh data
      await set(key, freshData, ttl: ttl, toJson: toJson);
      
      return freshData;
    } catch (e) {
      print('Error in getWithFallback for key $key: $e');
      rethrow;
    }
  }

  // Private methods

  // Ensure memory cache doesn't exceed max size
  void _ensureMemoryCacheSize() {
    while (_memoryCache.length > _maxMemoryCacheSize) {
      // Remove oldest entry
      final oldestKey = _memoryCache.keys.first;
      _memoryCache.remove(oldestKey);
    }
  }

  // Clean expired entries from both memory and disk
  Future<void> _cleanExpiredEntries() async {
    try {
      // Clean memory cache
      final expiredKeys = _memoryCache.entries
          .where((entry) => entry.value.isExpired)
          .map((entry) => entry.key)
          .toList();

      for (final key in expiredKeys) {
        _memoryCache.remove(key);
      }

      // Clean disk cache
      await _cleanExpiredDiskEntries();
    } catch (e) {
      print('Error cleaning expired entries: $e');
    }
  }

  // Disk cache operations

  Future<void> _loadDiskCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKeys = prefs.getStringList('${_diskCacheKey}_keys') ?? [];
      
      // Load most recent entries into memory cache
      final recentKeys = cacheKeys.take(_maxMemoryCacheSize ~/ 2);
      
      for (final key in recentKeys) {
        final cachedData = await _getDiskCache(key);
        if (cachedData != null && !cachedData.isExpired) {
          _memoryCache[key] = cachedData;
        }
      }
    } catch (e) {
      print('Error loading disk cache: $e');
    }
  }

  Future<CachedData?> _getDiskCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('${_diskCacheKey}_$key');
      
      if (jsonString != null) {
        final json = jsonDecode(jsonString);
        return CachedData.fromJson(json);
      }
      
      return null;
    } catch (e) {
      print('Error getting disk cache for key $key: $e');
      return null;
    }
  }

  Future<void> _setDiskCache(String key, CachedData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data.toJson());
      
      await prefs.setString('${_diskCacheKey}_$key', jsonString);
      
      // Update keys list
      final keys = prefs.getStringList('${_diskCacheKey}_keys') ?? [];
      if (!keys.contains(key)) {
        keys.add(key);
        await prefs.setStringList('${_diskCacheKey}_keys', keys);
      }
    } catch (e) {
      print('Error setting disk cache for key $key: $e');
    }
  }

  Future<void> _removeDiskCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('${_diskCacheKey}_$key');
      
      // Update keys list
      final keys = prefs.getStringList('${_diskCacheKey}_keys') ?? [];
      keys.remove(key);
      await prefs.setStringList('${_diskCacheKey}_keys', keys);
    } catch (e) {
      print('Error removing disk cache for key $key: $e');
    }
  }

  Future<void> _clearDiskCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getStringList('${_diskCacheKey}_keys') ?? [];
      
      for (final key in keys) {
        await prefs.remove('${_diskCacheKey}_$key');
      }
      
      await prefs.remove('${_diskCacheKey}_keys');
    } catch (e) {
      print('Error clearing disk cache: $e');
    }
  }

  Future<void> _cleanExpiredDiskEntries() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getStringList('${_diskCacheKey}_keys') ?? [];
      final expiredKeys = <String>[];
      
      for (final key in keys) {
        final cachedData = await _getDiskCache(key);
        if (cachedData == null || cachedData.isExpired) {
          expiredKeys.add(key);
        }
      }
      
      for (final key in expiredKeys) {
        await _removeDiskCache(key);
      }
    } catch (e) {
      print('Error cleaning expired disk entries: $e');
    }
  }

  Future<int> _getDiskCacheSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getStringList('${_diskCacheKey}_keys') ?? [];
      return keys.length;
    } catch (e) {
      print('Error getting disk cache size: $e');
      return 0;
    }
  }
}
