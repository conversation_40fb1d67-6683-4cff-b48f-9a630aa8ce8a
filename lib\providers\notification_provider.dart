import 'package:flutter/foundation.dart';
import '../models/notification.dart';
import '../services/notification_service.dart';

class NotificationProvider with ChangeNotifier {
  final NotificationService _notificationService = NotificationService();
  
  List<AppNotification> _notifications = [];
  List<AppNotification> _filteredNotifications = [];
  bool _isLoading = false;
  String? _errorMessage;
  NotificationType? _selectedFilter;
  bool _showUnreadOnly = false;
  String _timeRange = 'all';
  List<NotificationType> _selectedTypes = [];
  
  // Getters
  List<AppNotification> get notifications => _filteredNotifications;
  List<AppNotification> get allNotifications => _notifications;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  NotificationType? get selectedFilter => _selectedFilter;
  
  // إحصائيات الإشعارات
  int get unreadCount => _notifications.where((n) => n.isUnread).length;
  int get totalCount => _notifications.length;
  int get todayCount => _notifications.where((n) => 
    DateTime.now().difference(n.timestamp).inDays == 0).length;
  
  Map<NotificationType, int> get typeCount {
    final Map<NotificationType, int> counts = {};
    for (final notification in _notifications) {
      counts[notification.type] = (counts[notification.type] ?? 0) + 1;
    }
    return counts;
  }
  
  // تحميل الإشعارات
  Future<void> loadNotifications() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      _notifications = await _notificationService.getNotifications();
      _applyFilter();
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'خطأ في تحميل الإشعارات: $e';
      notifyListeners();
    }
  }
  
  // تطبيق الفلتر
  void _applyFilter() {
    _filteredNotifications = List.from(_notifications);

    // فلترة حسب النوع
    if (_selectedFilter != null) {
      _filteredNotifications = _filteredNotifications
          .where((n) => n.type == _selectedFilter)
          .toList();
    }

    // فلترة حسب الحالة (مقروء/غير مقروء)
    if (_showUnreadOnly) {
      _filteredNotifications = _filteredNotifications
          .where((n) => n.isUnread)
          .toList();
    }

    // فلترة حسب الوقت
    if (_timeRange != 'all') {
      final now = DateTime.now();
      DateTime cutoffDate;

      switch (_timeRange) {
        case 'today':
          cutoffDate = DateTime(now.year, now.month, now.day);
          break;
        case 'week':
          cutoffDate = now.subtract(const Duration(days: 7));
          break;
        case 'month':
          cutoffDate = DateTime(now.year, now.month - 1, now.day);
          break;
        default:
          cutoffDate = DateTime(1970);
      }

      _filteredNotifications = _filteredNotifications
          .where((n) => n.timestamp.isAfter(cutoffDate))
          .toList();
    }

    // فلترة حسب الأنواع المحددة
    if (_selectedTypes.isNotEmpty) {
      _filteredNotifications = _filteredNotifications
          .where((n) => _selectedTypes.contains(n.type))
          .toList();
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    _filteredNotifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }
  
  // تطبيق فلتر حسب النوع
  void filterByType(NotificationType? type) {
    _selectedFilter = type;
    _applyFilter();
    notifyListeners();
  }
  
  // تجميع الإشعارات المتشابهة
  List<GroupedNotification> get groupedNotifications {
    final Map<String, List<AppNotification>> groups = {};
    
    for (final notification in _filteredNotifications) {
      String groupKey = '${notification.type}_${notification.postId ?? notification.groupId ?? 'general'}';
      
      if (groups[groupKey] == null) {
        groups[groupKey] = [];
      }
      groups[groupKey]!.add(notification);
    }
    
    return groups.entries.map((entry) {
      final notifications = entry.value;
      final firstNotification = notifications.first;
      
      return GroupedNotification(
        type: firstNotification.type,
        notifications: notifications,
        groupTitle: _getGroupTitle(notifications),
        groupBody: _getGroupBody(notifications),
        latestTimestamp: notifications.first.timestamp,
      );
    }).toList()
      ..sort((a, b) => b.latestTimestamp.compareTo(a.latestTimestamp));
  }
  
  String _getGroupTitle(List<AppNotification> notifications) {
    if (notifications.length == 1) {
      return notifications.first.title;
    }
    
    final type = notifications.first.type;
    final count = notifications.length;
    
    switch (type) {
      case NotificationType.like:
        return '$count أشخاص أعجبوا بمنشورك';
      case NotificationType.comment:
        return '$count تعليقات جديدة';
      case NotificationType.follow:
        return '$count متابعين جدد';
      default:
        return '${notifications.first.title} و${count - 1} آخرين';
    }
  }
  
  String _getGroupBody(List<AppNotification> notifications) {
    if (notifications.length == 1) {
      return notifications.first.message;
    }
    
    final names = notifications.take(3).map((n) => 'مستخدم').join('، ');
    final remaining = notifications.length - 3;
    
    if (remaining > 0) {
      return '$names و$remaining آخرين';
    }
    return names;
  }
  
  // تحديد إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
      
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(
          status: NotificationStatus.read,
        );
        _applyFilter();
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحديث الإشعار: $e';
      notifyListeners();
    }
  }
  
  // تحديد جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    try {
      await _notificationService.markAllAsRead();
      
      _notifications = _notifications.map((n) => n.copyWith(
        status: NotificationStatus.read,
      )).toList();
      
      _applyFilter();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحديث الإشعارات: $e';
      notifyListeners();
    }
  }
  
  // حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationService.deleteNotification(notificationId);
      
      _notifications.removeWhere((n) => n.id == notificationId);
      _applyFilter();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في حذف الإشعار: $e';
      notifyListeners();
    }
  }
  
  // أرشفة إشعار
  Future<void> archiveNotification(String notificationId) async {
    try {
      await _notificationService.archiveNotification(notificationId);
      
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(
          status: NotificationStatus.archived,
        );
        _applyFilter();
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في أرشفة الإشعار: $e';
      notifyListeners();
    }
  }
  
  // إضافة إشعار جديد (للتحديث الفوري)
  void addNotification(AppNotification notification) {
    _notifications.insert(0, notification);
    _applyFilter();
    notifyListeners();
  }
  
  // مسح الأخطاء
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // تحديث فوري للإشعارات
  Future<void> refreshNotifications() async {
    await loadNotifications();
  }

  // تطبيق فلاتر متقدمة
  void applyAdvancedFilters({
    List<NotificationType>? selectedTypes,
    bool? showUnreadOnly,
    String? timeRange,
  }) {
    if (selectedTypes != null) {
      _selectedTypes = selectedTypes;
    }
    if (showUnreadOnly != null) {
      _showUnreadOnly = showUnreadOnly;
    }
    if (timeRange != null) {
      _timeRange = timeRange;
    }

    _applyFilter();
    notifyListeners();
  }

  // إعادة تعيين الفلاتر
  void resetFilters() {
    _selectedFilter = null;
    _showUnreadOnly = false;
    _timeRange = 'all';
    _selectedTypes = [];
    _applyFilter();
    notifyListeners();
  }
}

// فئة لتجميع الإشعارات
class GroupedNotification {
  final NotificationType type;
  final List<AppNotification> notifications;
  final String groupTitle;
  final String groupBody;
  final DateTime latestTimestamp;

  const GroupedNotification({
    required this.type,
    required this.notifications,
    required this.groupTitle,
    required this.groupBody,
    required this.latestTimestamp,
  });

  int get count => notifications.length;
  int get unreadCount => notifications.where((n) => n.isUnread).length;
  bool get hasUnread => unreadCount > 0;
  
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(latestTimestamp);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
    }
  }
}
