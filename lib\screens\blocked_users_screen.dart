import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_settings_provider.dart';

class BlockedUsersScreen extends StatefulWidget {
  const BlockedUsersScreen({super.key});

  @override
  State<BlockedUsersScreen> createState() => _BlockedUsersScreenState();
}

class _BlockedUsersScreenState extends State<BlockedUsersScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل قائمة المستخدمين المحظورين
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadBlockedUsers();
    });
  }

  void _loadBlockedUsers() {
    // محاكاة تحميل المستخدمين المحظورين
    final settingsProvider = Provider.of<AppSettingsProvider>(context, listen: false);
    
    // إضافة بعض المستخدمين المحظورين للاختبار
    if (settingsProvider.blockedUsers.isEmpty) {
      settingsProvider.blockUser('user_1');
      settingsProvider.blockUser('user_2');
      settingsProvider.blockUser('user_3');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'الحسابات المحظورة',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<AppSettingsProvider>(
        builder: (context, settingsProvider, child) {
          if (settingsProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (settingsProvider.blockedUsers.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.block,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد حسابات محظورة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'عندما تحظر شخصاً، سيظهر هنا',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // معلومات الحظر
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.red[700],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'حول حظر الحسابات',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red[700],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'الأشخاص المحظورون لن يتمكنوا من رؤية منشوراتك أو إرسال رسائل إليك',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.red[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // قائمة المستخدمين المحظورين
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: settingsProvider.blockedUsers.length,
                  itemBuilder: (context, index) {
                    final userId = settingsProvider.blockedUsers[index];
                    return _BlockedUserCard(
                      userId: userId,
                      onUnblock: () => _unblockUser(userId),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _unblockUser(String userId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الحظر'),
        content: const Text('هل تريد إلغاء حظر هذا المستخدم؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<AppSettingsProvider>(context, listen: false)
                  .unblockUser(userId);
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إلغاء حظر المستخدم'),
                ),
              );
            },
            child: const Text('إلغاء الحظر'),
          ),
        ],
      ),
    );
  }
}

class _BlockedUserCard extends StatelessWidget {
  final String userId;
  final VoidCallback onUnblock;

  const _BlockedUserCard({
    required this.userId,
    required this.onUnblock,
  });

  @override
  Widget build(BuildContext context) {
    // محاكاة بيانات المستخدم
    final userData = _getUserData(userId);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // صورة المستخدم
            CircleAvatar(
              radius: 25,
              backgroundColor: Colors.grey[300],
              backgroundImage: userData['avatar'] != null
                  ? NetworkImage(userData['avatar'])
                  : null,
              child: userData['avatar'] == null
                  ? Icon(
                      userData['gender'] == 'female' 
                          ? Icons.person_2 
                          : Icons.person,
                      color: Colors.grey[600],
                    )
                  : null,
            ),
            
            const SizedBox(width: 16),
            
            // معلومات المستخدم
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userData['name'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'تم الحظر ${_getBlockedDate()}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            
            // زر إلغاء الحظر
            ElevatedButton(
              onPressed: onUnblock,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                minimumSize: const Size(100, 36),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(18),
                ),
              ),
              child: const Text(
                'إلغاء الحظر',
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> _getUserData(String userId) {
    // محاكاة بيانات المستخدمين
    final users = {
      'user_1': {
        'name': 'أحمد محمد',
        'avatar': null,
        'gender': 'male',
      },
      'user_2': {
        'name': 'فاطمة علي',
        'avatar': null,
        'gender': 'female',
      },
      'user_3': {
        'name': 'سعد الدين',
        'avatar': null,
        'gender': 'male',
      },
    };

    return users[userId] ?? {
      'name': 'مستخدم غير معروف',
      'avatar': null,
      'gender': 'male',
    };
  }

  String _getBlockedDate() {
    // محاكاة تاريخ الحظر
    final random = DateTime.now().subtract(
      Duration(days: userId.hashCode % 30 + 1),
    );
    
    final now = DateTime.now();
    final difference = now.difference(random);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ قليل';
    }
  }
}
