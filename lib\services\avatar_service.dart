import 'package:flutter/material.dart';

enum Gender {
  male,
  female,
  unknown,
}

class AvatarService {
  static final AvatarService _instance = AvatarService._internal();
  factory AvatarService() => _instance;
  AvatarService._internal();

  // قوائم الأسماء العربية
  static const List<String> _femaleNames = [
    'فاطمة', 'فاطمه', 'أسماء', 'اسماء', 'عائشة', 'عايشة', 'خديجة', 'خديجه',
    'مريم', 'زينب', 'رقية', 'رقيه', 'أم كلثوم', 'ام كلثوم', 'سكينة', 'سكينه',
    'نور', 'نورا', 'سارة', 'ساره', 'ليلى', 'ليلا', 'هند', 'دعاء', 'دعا',
    'رانيا', 'رانيه', 'منى', 'منا', 'سلمى', 'سلما', 'ندى', 'ندا', 'رنا',
    'لينا', 'لينه', 'ريم', 'دانا', 'دانه', 'جنى', 'جنا', 'تالا', 'تاله',
    'يارا', 'ياره', 'لمى', 'لما', 'غلا', 'غلى', 'شهد', 'جود', 'روان',
    'روانه', 'لجين', 'لجينه', 'رهف', 'رهاف', 'شوق', 'أمل', 'امل', 'فرح',
    'سعاد', 'سعاده', 'وفاء', 'وفا', 'إيمان', 'ايمان', 'هدى', 'هدا', 'رحمة',
    'رحمه', 'بركة', 'بركه', 'نعمة', 'نعمه', 'حنان', 'حنين', 'ياسمين',
    'ياسمينه', 'وردة', 'ورده', 'زهرة', 'زهره', 'بسمة', 'بسمه', 'ابتسام',
    'سميرة', 'سميره', 'كريمة', 'كريمه', 'لطيفة', 'لطيفه', 'جميلة', 'جميله',
    'حليمة', 'حليمه', 'رشيدة', 'رشيده', 'سعيدة', 'سعيده', 'محبوبة', 'محبوبه',
    'مباركة', 'مباركه', 'مسعودة', 'مسعوده', 'منيرة', 'منيره', 'نادية', 'ناديه',
    'هالة', 'هاله', 'وسام', 'وسامه', 'علياء', 'عليا', 'صفاء', 'صفا',
    'ضحى', 'ضحا', 'شروق', 'غروب', 'قمر', 'بدر', 'نجمة', 'نجمه',
    'لؤلؤة', 'لؤلؤه', 'درة', 'دره', 'جوهرة', 'جوهره', 'ملكة', 'ملكه',
    'أميرة', 'اميرة', 'أميره', 'اميره', 'سلطانة', 'سلطانه'
  ];

  static const List<String> _maleNames = [
    'محمد', 'أحمد', 'احمد', 'علي', 'على', 'حسن', 'حسين', 'عبدالله',
    'عبد الله', 'عبدالرحمن', 'عبد الرحمن', 'عبدالعزيز', 'عبد العزيز',
    'عمر', 'عثمان', 'أبو بكر', 'ابو بكر', 'خالد', 'سعد', 'سعيد',
    'فيصل', 'عبدالملك', 'عبد الملك', 'سلمان', 'طلال', 'بندر', 'تركي',
    'نواف', 'مشعل', 'فهد', 'عبدالإله', 'عبد الاله', 'ماجد', 'سلطان',
    'عادل', 'كريم', 'رحيم', 'حكيم', 'أمين', 'امين', 'صادق', 'طاهر',
    'نبيل', 'شريف', 'عزيز', 'حبيب', 'رفيق', 'صديق', 'وفي', 'مخلص',
    'صالح', 'فالح', 'ناجح', 'فائز', 'غالب', 'قاسم', 'باسم', 'حاتم',
    'إبراهيم', 'ابراهيم', 'إسماعيل', 'اسماعيل', 'إسحاق', 'اسحاق',
    'يعقوب', 'يوسف', 'موسى', 'هارون', 'داود', 'سليمان', 'عيسى',
    'يحيى', 'زكريا', 'عمران', 'لقمان', 'إدريس', 'ادريس', 'نوح',
    'هود', 'صالح', 'شعيب', 'أيوب', 'ايوب', 'يونس', 'ذو الكفل',
    'الياس', 'اليسع', 'يوشع', 'صموئيل', 'جرجيس', 'بولس', 'متى',
    'مرقس', 'لوقا', 'يوحنا', 'بطرس', 'أندراوس', 'اندراوس', 'فيليب',
    'توما', 'يعقوب', 'تداوس', 'سمعان', 'متياس', 'برنابا', 'استفانوس',
    'تيموثاوس', 'تيطس', 'فليمون', 'أبولوس', 'ابولوس'
  ];

  // مسارات الصور الافتراضية
  static const String _maleAvatarPath = 'assets/images/default_avatars/male_avatar.png';
  static const String _femaleAvatarPath = 'assets/images/default_avatars/female_avatar.png';

  /// تحديد جنس الشخص بناءً على الاسم
  Gender detectGender(String name) {
    if (name.isEmpty) return Gender.unknown;
    
    final cleanName = _cleanName(name);
    
    // البحث في أسماء الإناث
    for (final femaleName in _femaleNames) {
      if (cleanName.contains(femaleName) || femaleName.contains(cleanName)) {
        return Gender.female;
      }
    }
    
    // البحث في أسماء الذكور
    for (final maleName in _maleNames) {
      if (cleanName.contains(maleName) || maleName.contains(cleanName)) {
        return Gender.male;
      }
    }
    
    // إذا لم يتم العثور على تطابق، نحاول التخمين بناءً على النهايات الشائعة
    if (_isFemaleByEnding(cleanName)) {
      return Gender.female;
    }
    
    if (_isMaleByEnding(cleanName)) {
      return Gender.male;
    }
    
    return Gender.unknown;
  }

  /// تنظيف الاسم من الرموز والمسافات الزائدة
  String _cleanName(String name) {
    return name
        .trim()
        .toLowerCase()
        .replaceAll(RegExp(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]'), '')
        .replaceAll(RegExp(r'\s+'), ' ');
  }

  /// تحديد الجنس بناءً على نهايات الأسماء الشائعة
  bool _isFemaleByEnding(String name) {
    final femaleEndings = ['ة', 'ه', 'اء', 'ى', 'ان', 'ين', 'ات'];
    return femaleEndings.any((ending) => name.endsWith(ending));
  }

  bool _isMaleByEnding(String name) {
    final maleEndings = ['د', 'ر', 'ن', 'م', 'ل', 'ك', 'ت', 'س', 'ف', 'ق'];
    return maleEndings.any((ending) => name.endsWith(ending));
  }

  /// الحصول على مسار الصورة الافتراضية بناءً على الجنس
  String getDefaultAvatarPath(String name) {
    final gender = detectGender(name);
    
    switch (gender) {
      case Gender.female:
        return _femaleAvatarPath;
      case Gender.male:
        return _maleAvatarPath;
      case Gender.unknown:
        // افتراضياً نستخدم صورة الذكر للحالات غير المعروفة
        return _maleAvatarPath;
    }
  }

  /// إنشاء Widget للصورة الشخصية مع الصورة الافتراضية المناسبة
  Widget buildAvatar({
    required String name,
    String? imageUrl,
    double radius = 20,
    Color? backgroundColor,
    Color? textColor,
    bool showBorder = false,
    Color borderColor = Colors.grey,
    double borderWidth = 2,
  }) {
    Widget avatarWidget;

    if (imageUrl != null && imageUrl.isNotEmpty) {
      // إذا كان لديه صورة، استخدمها
      avatarWidget = CircleAvatar(
        radius: radius,
        backgroundColor: backgroundColor ?? Colors.grey[300],
        backgroundImage: NetworkImage(imageUrl),
        onBackgroundImageError: (exception, stackTrace) {
          // في حالة فشل تحميل الصورة، استخدم الصورة الافتراضية
        },
        child: null,
      );
    } else {
      // استخدم الصورة الافتراضية بناءً على الجنس
      final defaultAvatarPath = getDefaultAvatarPath(name);
      
      avatarWidget = CircleAvatar(
        radius: radius,
        backgroundColor: backgroundColor ?? Colors.grey[100],
        backgroundImage: AssetImage(defaultAvatarPath),
        onBackgroundImageError: (exception, stackTrace) {
          // في حالة عدم وجود الصورة الافتراضية، استخدم الحرف الأول
        },
        child: null,
      );
    }

    // إضافة حدود إذا كانت مطلوبة
    if (showBorder) {
      return Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: borderColor,
            width: borderWidth,
          ),
        ),
        child: avatarWidget,
      );
    }

    return avatarWidget;
  }

  /// إنشاء Widget للصورة الشخصية مع fallback للحرف الأول
  Widget buildAvatarWithFallback({
    required String name,
    String? imageUrl,
    double radius = 20,
    Color? backgroundColor,
    Color? textColor,
    bool showBorder = false,
    Color borderColor = Colors.grey,
    double borderWidth = 2,
  }) {
    if (imageUrl != null && imageUrl.isNotEmpty) {
      // إذا كان لديه صورة، استخدمها
      return buildAvatar(
        name: name,
        imageUrl: imageUrl,
        radius: radius,
        backgroundColor: backgroundColor,
        textColor: textColor,
        showBorder: showBorder,
        borderColor: borderColor,
        borderWidth: borderWidth,
      );
    }

    // استخدم أيقونة مناسبة للجنس بدلاً من الصورة الافتراضية مؤقتاً
    final gender = detectGender(name);
    IconData iconData;
    Color iconColor;

    switch (gender) {
      case Gender.female:
        iconData = Icons.person_2; // أيقونة أنثوية
        iconColor = Colors.pink[300]!;
        break;
      case Gender.male:
        iconData = Icons.person; // أيقونة ذكورية
        iconColor = Colors.blue[300]!;
        break;
      case Gender.unknown:
        iconData = Icons.person;
        iconColor = Colors.grey[400]!;
        break;
    }

    Widget avatarWidget = CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey[100],
      child: Icon(
        iconData,
        size: radius * 1.2,
        color: iconColor,
      ),
    );

    // إضافة حدود إذا كانت مطلوبة
    if (showBorder) {
      return Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: borderColor,
            width: borderWidth,
          ),
        ),
        child: avatarWidget,
      );
    }

    return avatarWidget;
  }

  /// توليد لون بناءً على الاسم
  Color _getColorFromName(String name) {
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    
    final hash = name.hashCode;
    return colors[hash.abs() % colors.length];
  }

  /// فحص ما إذا كان الاسم أنثوي
  bool isFemale(String name) {
    return detectGender(name) == Gender.female;
  }

  /// فحص ما إذا كان الاسم ذكوري
  bool isMale(String name) {
    return detectGender(name) == Gender.male;
  }

  /// الحصول على نص الجنس
  String getGenderText(String name) {
    final gender = detectGender(name);
    switch (gender) {
      case Gender.female:
        return 'أنثى';
      case Gender.male:
        return 'ذكر';
      case Gender.unknown:
        return 'غير محدد';
    }
  }
}
