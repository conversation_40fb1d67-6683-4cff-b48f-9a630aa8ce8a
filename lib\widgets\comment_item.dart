import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/comment.dart';
import '../models/reaction_types.dart';
import '../providers/comments_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import '../widgets/reaction_picker.dart';
import '../widgets/link_preview.dart';
import '../screens/facebook_profile_screen.dart';

class CommentItem extends StatefulWidget {
  final Comment comment;
  final String postId;
  final String currentUserId;
  final int depth;
  final VoidCallback? onReply;
  final Function(Comment)? onEdit;
  final Function(Comment)? onDelete;

  const CommentItem({
    super.key,
    required this.comment,
    required this.postId,
    required this.currentUserId,
    this.depth = 0,
    this.onReply,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<CommentItem> createState() => _CommentItemState();
}

class _CommentItemState extends State<CommentItem>
    with TickerProviderStateMixin {
  bool _showReactionPicker = false;
  bool _showReplies = true;
  late AnimationController _reactionController;
  late Animation<double> _reactionAnimation;

  @override
  void initState() {
    super.initState();
    _reactionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _reactionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _reactionController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _reactionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isOwner = widget.comment.authorId == widget.currentUserId;
    final maxDepth = 3; // أقصى عمق للردود
    final shouldIndent = widget.depth > 0 && widget.depth <= maxDepth;

    return Container(
      margin: EdgeInsets.only(
        left: shouldIndent ? (widget.depth * 20.0) : 0,
        bottom: 8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // خط الاتصال للردود
          if (shouldIndent) _buildConnectionLine(),
          
          // محتوى التعليق
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المستخدم
              GestureDetector(
                onTap: () => _navigateToProfile(),
                child: SmartAvatarWithText(
                  user: widget.comment.author,
                  radius: shouldIndent ? 16 : 20,
                ),
              ),
              
              const SizedBox(width: 8),
              
              // محتوى التعليق
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فقاعة التعليق
                    _buildCommentBubble(),
                    
                    const SizedBox(height: 4),
                    
                    // أزرار التفاعل
                    _buildActionButtons(),
                    
                    // عرض التفاعلات
                    if (widget.comment.hasReactions)
                      _buildReactionsDisplay(),
                    
                    // عرض الردود
                    if (widget.comment.hasReplies && _showReplies)
                      _buildReplies(),
                  ],
                ),
              ),
            ],
          ),
          
          // منتقي التفاعلات
          if (_showReactionPicker)
            AnimatedBuilder(
              animation: _reactionAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _reactionAnimation.value,
                  child: ReactionPicker(
                    onReactionSelected: _handleReaction,
                    onClose: () {
                      setState(() {
                        _showReactionPicker = false;
                      });
                      _reactionController.reverse();
                    },
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildConnectionLine() {
    return Container(
      margin: const EdgeInsets.only(left: 28, bottom: 4),
      child: Row(
        children: [
          Container(
            width: 2,
            height: 20,
            color: Colors.grey[300],
          ),
          Container(
            width: 20,
            height: 2,
            color: Colors.grey[300],
          ),
        ],
      ),
    );
  }

  Widget _buildCommentBubble() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.comment.isDeleted 
            ? Colors.grey[100] 
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // اسم المستخدم والوقت
          Row(
            children: [
              GestureDetector(
                onTap: () => _navigateToProfile(),
                child: Text(
                  widget.comment.author.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                widget.comment.timeAgo,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
              if (widget.comment.isEdited) ...[
                const SizedBox(width: 4),
                Text(
                  '(معدل)',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 11,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 4),
          
          // محتوى التعليق
          if (!widget.comment.isDeleted) ...[
            Text(
              widget.comment.content,
              style: const TextStyle(fontSize: 14),
            ),
            
            // عرض الوسائط
            if (widget.comment.hasMedia)
              _buildMediaContent(),
            
            // عرض معاينة الرابط
            if (widget.comment.hasLink)
              _buildLinkPreview(),
          ] else ...[
            Text(
              widget.comment.content,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMediaContent() {
    if (widget.comment.mediaUrl == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: widget.comment.type == CommentType.image
            ? Image.network(
                widget.comment.mediaUrl!,
                fit: BoxFit.cover,
                height: 200,
                width: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(Icons.broken_image, size: 50),
                    ),
                  );
                },
              )
            : Container(
                height: 200,
                color: Colors.black,
                child: const Center(
                  child: Icon(
                    Icons.play_circle_fill,
                    color: Colors.white,
                    size: 50,
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildLinkPreview() {
    if (widget.comment.linkUrl == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: LinkPreview(
        url: widget.comment.linkUrl!,
        title: widget.comment.linkTitle,
        description: widget.comment.linkDescription,
        imageUrl: widget.comment.linkImage,
      ),
    );
  }

  Widget _buildActionButtons() {
    final userReaction = widget.comment.getUserReaction(widget.currentUserId);
    
    return Row(
      children: [
        // زر الإعجاب/التفاعل
        GestureDetector(
          onTap: () => _handleQuickReaction(),
          onLongPress: () => _showReactionPickerDialog(),
          child: Row(
            children: [
              Icon(
                userReaction != null ? Icons.favorite : Icons.favorite_border,
                size: 16,
                color: userReaction != null 
                    ? _getReactionColor(userReaction.type)
                    : Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                userReaction?.label ?? 'إعجاب',
                style: TextStyle(
                  color: userReaction != null 
                      ? _getReactionColor(userReaction.type)
                      : Colors.grey[600],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(width: 16),
        
        // زر الرد
        if (!widget.comment.isDeleted)
          GestureDetector(
            onTap: widget.onReply,
            child: Row(
              children: [
                Icon(
                  Icons.reply,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  'رد',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        
        const Spacer(),
        
        // قائمة الخيارات
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_horiz,
            size: 16,
            color: Colors.grey[600],
          ),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            if (widget.comment.authorId == widget.currentUserId && !widget.comment.isDeleted) ...[
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16, color: Colors.black),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
            const PopupMenuItem(
              value: 'copy',
              child: Row(
                children: [
                  Icon(Icons.copy, size: 16, color: Colors.black),
                  SizedBox(width: 8),
                  Text('نسخ النص'),
                ],
              ),
            ),
            if (!widget.comment.isDeleted)
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report, size: 16, color: Colors.orange),
                    SizedBox(width: 8),
                    Text('إبلاغ'),
                  ],
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildReactionsDisplay() {
    if (!widget.comment.hasReactions) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Wrap(
        spacing: 4,
        children: widget.comment.reactionCounts.entries.map((entry) {
          final reaction = CommentReaction(
            id: '',
            userId: '',
            userName: '',
            type: entry.key,
            timestamp: DateTime.now(),
          );

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(reaction.emoji, style: const TextStyle(fontSize: 12)),
                const SizedBox(width: 2),
                Text(
                  '${entry.value}',
                  style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildReplies() {
    if (!widget.comment.hasReplies) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        children: [
          // زر عرض/إخفاء الردود
          if (widget.comment.repliesCount > 0)
            GestureDetector(
              onTap: () {
                setState(() {
                  _showReplies = !_showReplies;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      _showReplies ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _showReplies
                          ? 'إخفاء الردود (${widget.comment.repliesCount})'
                          : 'عرض الردود (${widget.comment.repliesCount})',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // عرض الردود
          if (_showReplies)
            ...widget.comment.replies.map((reply) {
              return CommentItem(
                comment: reply,
                postId: widget.postId,
                currentUserId: widget.currentUserId,
                depth: widget.depth + 1,
                onReply: () => widget.onReply?.call(),
                onEdit: widget.onEdit,
                onDelete: widget.onDelete,
              );
            }),
        ],
      ),
    );
  }

  void _handleQuickReaction() {
    final provider = Provider.of<CommentsProvider>(context, listen: false);

    provider.toggleReaction(
      postId: widget.postId,
      commentId: widget.comment.id,
      userId: widget.currentUserId,
      userName: 'المستخدم الحالي', // يجب الحصول على الاسم الحقيقي
      reactionType: ReactionType.like,
    );
  }

  void _showReactionPickerDialog() {
    setState(() {
      _showReactionPicker = true;
    });
    _reactionController.forward();
  }

  void _handleReaction(ReactionType reactionType) {
    final provider = Provider.of<CommentsProvider>(context, listen: false);

    provider.toggleReaction(
      postId: widget.postId,
      commentId: widget.comment.id,
      userId: widget.currentUserId,
      userName: 'المستخدم الحالي', // يجب الحصول على الاسم الحقيقي
      reactionType: reactionType,
    );

    setState(() {
      _showReactionPicker = false;
    });
    _reactionController.reverse();
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        widget.onEdit?.call(widget.comment);
        break;
      case 'delete':
        _showDeleteDialog();
        break;
      case 'copy':
        _copyToClipboard();
        break;
      case 'report':
        _showReportDialog();
        break;
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التعليق'),
        content: const Text('هل تريد حذف هذا التعليق؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDelete?.call(widget.comment);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard() {
    // نسخ النص إلى الحافظة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم نسخ النص')),
    );
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إبلاغ عن التعليق'),
        content: const Text('سبب الإبلاغ:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إرسال البلاغ')),
              );
            },
            child: const Text('إرسال'),
          ),
        ],
      ),
    );
  }

  void _navigateToProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FacebookProfileScreen(
          userId: widget.comment.author.id,
          userName: widget.comment.author.name,
        ),
      ),
    );
  }

  Color _getReactionColor(ReactionType type) {
    switch (type) {
      case ReactionType.like:
        return Colors.blue;
      case ReactionType.love:
        return Colors.red;
      case ReactionType.haha:
        return Colors.orange;
      case ReactionType.wow:
        return Colors.yellow;
      case ReactionType.sad:
        return Colors.blue;
      case ReactionType.angry:
        return Colors.red;
    }
  }
}
