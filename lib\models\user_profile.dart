
enum Gender {
  male,
  female,
}

class UserProfile {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String passwordHash;
  final Gender gender;
  final DateTime birthDate;
  final String country;
  final String city;
  final String? profileImageUrl;
  final String? coverImageUrl;
  final String? bio;
  final String? phoneNumber;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final bool isEmailVerified;
  final bool isActive;

  const UserProfile({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.passwordHash,
    required this.gender,
    required this.birthDate,
    required this.country,
    required this.city,
    this.profileImageUrl,
    this.coverImageUrl,
    this.bio,
    this.phoneNumber,
    required this.createdAt,
    required this.lastLoginAt,
    this.isEmailVerified = false,
    this.isActive = true,
  });

  String get fullName => '$firstName $lastName';
  
  int get age {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  String get genderText => gender == Gender.male ? 'ذكر' : 'أنثى';

  UserProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? passwordHash,
    Gender? gender,
    DateTime? birthDate,
    String? country,
    String? city,
    String? profileImageUrl,
    String? coverImageUrl,
    String? bio,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isEmailVerified,
    bool? isActive,
  }) {
    return UserProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      passwordHash: passwordHash ?? this.passwordHash,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      country: country ?? this.country,
      city: city ?? this.city,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      bio: bio ?? this.bio,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'passwordHash': passwordHash,
      'gender': gender.name,
      'birthDate': birthDate.toIso8601String(),
      'country': country,
      'city': city,
      'profileImageUrl': profileImageUrl,
      'coverImageUrl': coverImageUrl,
      'bio': bio,
      'phoneNumber': phoneNumber,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'isEmailVerified': isEmailVerified,
      'isActive': isActive,
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      email: json['email'],
      passwordHash: json['passwordHash'],
      gender: Gender.values.firstWhere((e) => e.name == json['gender']),
      birthDate: DateTime.parse(json['birthDate']),
      country: json['country'],
      city: json['city'],
      profileImageUrl: json['profileImageUrl'],
      coverImageUrl: json['coverImageUrl'],
      bio: json['bio'],
      phoneNumber: json['phoneNumber'],
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: DateTime.parse(json['lastLoginAt']),
      isEmailVerified: json['isEmailVerified'] ?? false,
      isActive: json['isActive'] ?? true,
    );
  }
}

// نموذج بيانات التسجيل
class RegistrationData {
  String firstName = '';
  String lastName = '';
  Gender? gender;
  DateTime? birthDate;
  String country = '';
  String city = '';
  String email = '';
  String password = '';

  bool get isStep1Valid => firstName.isNotEmpty && lastName.isNotEmpty;
  
  bool get isStep2Valid => 
      gender != null && 
      birthDate != null && 
      country.isNotEmpty && 
      city.isNotEmpty;
  
  bool get isStep3Valid => 
      email.isNotEmpty && 
      password.isNotEmpty && 
      password.length >= 6;

  bool get isComplete => isStep1Valid && isStep2Valid && isStep3Valid;

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'gender': gender?.name,
      'birthDate': birthDate?.toIso8601String(),
      'country': country,
      'city': city,
      'email': email,
      'password': password,
    };
  }

  void clear() {
    firstName = '';
    lastName = '';
    gender = null;
    birthDate = null;
    country = '';
    city = '';
    email = '';
    password = '';
  }
}

// نموذج بيانات تسجيل الدخول
class LoginData {
  String email = '';
  String password = '';

  bool get isValid => email.isNotEmpty && password.isNotEmpty;

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }

  void clear() {
    email = '';
    password = '';
  }
}

// استجابة تسجيل الدخول
class LoginResponse {
  final bool success;
  final String? token;
  final UserProfile? user;
  final String? errorMessage;

  const LoginResponse({
    required this.success,
    this.token,
    this.user,
    this.errorMessage,
  });

  factory LoginResponse.success(String token, UserProfile user) {
    return LoginResponse(
      success: true,
      token: token,
      user: user,
    );
  }

  factory LoginResponse.error(String message) {
    return LoginResponse(
      success: false,
      errorMessage: message,
    );
  }
}

// استجابة التسجيل
class RegistrationResponse {
  final bool success;
  final String? userId;
  final String? token;
  final String? errorMessage;

  const RegistrationResponse({
    required this.success,
    this.userId,
    this.token,
    this.errorMessage,
  });

  factory RegistrationResponse.success(String userId, String token) {
    return RegistrationResponse(
      success: true,
      userId: userId,
      token: token,
    );
  }

  factory RegistrationResponse.error(String message) {
    return RegistrationResponse(
      success: false,
      errorMessage: message,
    );
  }
}
