import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../theme/app_theme.dart';
import '../providers/social_provider.dart';
import '../models/post.dart';

class CreatePostScreen extends StatefulWidget {
  final String? postType; // 'text', 'image', 'video'
  final File? selectedMedia;
  
  const CreatePostScreen({
    super.key,
    this.postType,
    this.selectedMedia,
  });

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final TextEditingController _textController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  File? _selectedImage;
  File? _selectedVideo;
  String? _selectedFeeling;
  String? _selectedLocation;
  bool _isPosting = false;

  @override
  void initState() {
    super.initState();
    if (widget.selectedMedia != null) {
      if (widget.postType == 'image') {
        _selectedImage = widget.selectedMedia;
      } else if (widget.postType == 'video') {
        _selectedVideo = widget.selectedMedia;
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'إنشاء منشور',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: _isPosting ? null : _publishPost,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: _isPosting
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('نشر'),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المستخدم
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: AppTheme.primaryColor,
                  child: const Icon(Icons.person, color: Colors.white),
                ),
                const SizedBox(width: 12),
                const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'أنت',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'عام',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // مربع النص
            TextField(
              controller: _textController,
              maxLines: null,
              decoration: const InputDecoration(
                hintText: 'ما الذي تفكر فيه؟',
                border: InputBorder.none,
                hintStyle: TextStyle(fontSize: 18, color: Colors.grey),
              ),
              style: const TextStyle(fontSize: 18),
            ),
            
            const SizedBox(height: 20),
            
            // عرض الصورة المختارة
            if (_selectedImage != null) _buildImagePreview(),
            
            // عرض الفيديو المختار
            if (_selectedVideo != null) _buildVideoPreview(),
            
            const SizedBox(height: 20),
            
            // عرض الشعور والموقع
            if (_selectedFeeling != null || _selectedLocation != null)
              _buildFeelingLocationDisplay(),
            
            const SizedBox(height: 30),
            
            // خيارات الإضافة
            _buildAddOptions(),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        image: DecorationImage(
          image: FileImage(_selectedImage!),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => setState(() => _selectedImage = null),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPreview() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          const Center(
            child: Icon(Icons.play_circle_filled, color: Colors.white, size: 60),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => setState(() => _selectedVideo = null),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeelingLocationDisplay() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          if (_selectedFeeling != null) ...[
            const Icon(Icons.mood, color: Colors.orange, size: 20),
            const SizedBox(width: 8),
            Text(_selectedFeeling!),
          ],
          if (_selectedFeeling != null && _selectedLocation != null)
            const Text(' • '),
          if (_selectedLocation != null) ...[
            const Icon(Icons.location_on, color: Colors.red, size: 20),
            const SizedBox(width: 8),
            Text(_selectedLocation!),
          ],
        ],
      ),
    );
  }

  Widget _buildAddOptions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إضافة إلى منشورك',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildAddOptionIcon(
                icon: Icons.image,
                color: Colors.green,
                onTap: _pickImage,
              ),
              _buildAddOptionIcon(
                icon: Icons.videocam,
                color: Colors.orange,
                onTap: _pickVideo,
              ),
              _buildAddOptionIcon(
                icon: Icons.mood,
                color: Colors.purple,
                onTap: _selectFeeling,
              ),
              _buildAddOptionIcon(
                icon: Icons.location_on,
                color: Colors.red,
                onTap: _selectLocation,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddOptionIcon({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: color),
      ),
    );
  }

  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
        _selectedVideo = null; // إزالة الفيديو إذا كان موجود
      });
    }
  }

  Future<void> _pickVideo() async {
    final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
    if (video != null) {
      setState(() {
        _selectedVideo = File(video.path);
        _selectedImage = null; // إزالة الصورة إذا كانت موجودة
      });
    }
  }

  void _selectFeeling() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('كيف تشعر؟'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFeelingOption('😊 سعيد'),
            _buildFeelingOption('😢 حزين'),
            _buildFeelingOption('😍 متحمس'),
            _buildFeelingOption('😴 متعب'),
            _buildFeelingOption('🤔 مفكر'),
          ],
        ),
      ),
    );
  }

  Widget _buildFeelingOption(String feeling) {
    return ListTile(
      title: Text(feeling),
      onTap: () {
        setState(() => _selectedFeeling = feeling);
        Navigator.pop(context);
      },
    );
  }

  void _selectLocation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('أين أنت؟'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLocationOption('الرياض، السعودية'),
            _buildLocationOption('جدة، السعودية'),
            _buildLocationOption('الدمام، السعودية'),
            _buildLocationOption('مكة المكرمة، السعودية'),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationOption(String location) {
    return ListTile(
      title: Text(location),
      onTap: () {
        setState(() => _selectedLocation = location);
        Navigator.pop(context);
      },
    );
  }

  Future<void> _publishPost() async {
    if (_textController.text.trim().isEmpty && 
        _selectedImage == null && 
        _selectedVideo == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة محتوى للمنشور')),
      );
      return;
    }

    setState(() => _isPosting = true);

    try {
      final socialProvider = Provider.of<SocialProvider>(context, listen: false);

      // إنشاء المنشور
      await socialProvider.createPost(
        content: _textController.text.trim(),
        type: _getPostType(),
        media: _buildMediaList(),
        feeling: _selectedFeeling,
        location: _selectedLocation,
      );

      // العودة للشاشة الرئيسية
      if (mounted) {
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم نشر المنشور بنجاح!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في النشر: $e')),
        );
      }
    } finally {
      setState(() => _isPosting = false);
    }
  }

  PostType _getPostType() {
    if (_selectedImage != null) {
      return PostType.image;
    } else if (_selectedVideo != null) {
      return PostType.video;
    } else {
      return PostType.text;
    }
  }

  List<PostMedia> _buildMediaList() {
    List<PostMedia> mediaList = [];

    if (_selectedImage != null) {
      mediaList.add(PostMedia(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: PostType.image,
        url: _selectedImage!.path,
      ));
    }

    if (_selectedVideo != null) {
      mediaList.add(PostMedia(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: PostType.video,
        url: _selectedVideo!.path,
      ));
    }

    return mediaList;
  }
}
