import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/chat.dart';
import '../models/message.dart';
import '../models/group_chat.dart';
import '../services/mock_data_service.dart';

class MessengerProvider with ChangeNotifier {
  final MockDataService _dataService = MockDataService();

  // قوائم البيانات
  List<Chat> _chats = [];
  List<Chat> _archivedChats = [];
  List<GroupChat> _groupChats = [];
  List<Message> _currentChatMessages = [];
  List<User> _contacts = [];
  
  // حالة التطبيق
  bool _isLoading = false;
  String? _errorMessage;
  String? _currentChatId;
  bool _isTyping = false;
  bool _isRecording = false;
  
  // إعدادات الماسنجر
  MessengerSettings _settings = MessengerSettings();
  
  // Getters
  List<Chat> get chats => _chats;
  List<Chat> get archivedChats => _archivedChats;
  List<GroupChat> get groupChats => _groupChats;
  List<Message> get currentChatMessages => _currentChatMessages;
  List<User> get contacts => _contacts;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get currentChatId => _currentChatId;
  bool get isTyping => _isTyping;
  bool get isRecording => _isRecording;
  MessengerSettings get settings => _settings;

  MessengerProvider() {
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _loadSettings();
    await _loadContacts();
    await loadChats();
    await _loadArchivedChats();
    await _loadGroupChats();
  }

  // تحميل الإعدادات
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _settings = MessengerSettings(
      // إعدادات الإشعارات
      notificationsEnabled: prefs.getBool('notifications_enabled') ?? true,
      messagePreview: prefs.getBool('message_preview') ?? true,
      soundEnabled: prefs.getBool('sound_enabled') ?? true,
      vibrationEnabled: prefs.getBool('vibration_enabled') ?? true,
      
      // إعدادات الخصوصية
      readReceipts: prefs.getBool('read_receipts') ?? true,
      lastSeen: prefs.getBool('last_seen') ?? true,
      onlineStatus: prefs.getBool('online_status') ?? true,
      
      // إعدادات التحميل التلقائي
      autoDownloadImages: prefs.getBool('auto_download_images') ?? true,
      autoDownloadVideos: prefs.getBool('auto_download_videos') ?? false,
      autoDownloadAudio: prefs.getBool('auto_download_audio') ?? true,
      autoDownloadDocuments: prefs.getBool('auto_download_documents') ?? false,
      
      // إعدادات المظهر
      fontSize: prefs.getString('font_size') ?? 'متوسط',
      chatWallpaper: prefs.getString('chat_wallpaper') ?? 'افتراضي',
      darkMode: prefs.getBool('dark_mode') ?? false,
      
      // إعدادات عامة
      language: prefs.getString('language') ?? 'العربية',
      backupFrequency: prefs.getString('backup_frequency') ?? 'يومياً',
    );
    notifyListeners();
  }

  // حفظ الإعدادات
  Future<void> saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // إعدادات الإشعارات
    await prefs.setBool('notifications_enabled', _settings.notificationsEnabled);
    await prefs.setBool('message_preview', _settings.messagePreview);
    await prefs.setBool('sound_enabled', _settings.soundEnabled);
    await prefs.setBool('vibration_enabled', _settings.vibrationEnabled);
    
    // إعدادات الخصوصية
    await prefs.setBool('read_receipts', _settings.readReceipts);
    await prefs.setBool('last_seen', _settings.lastSeen);
    await prefs.setBool('online_status', _settings.onlineStatus);
    
    // إعدادات التحميل التلقائي
    await prefs.setBool('auto_download_images', _settings.autoDownloadImages);
    await prefs.setBool('auto_download_videos', _settings.autoDownloadVideos);
    await prefs.setBool('auto_download_audio', _settings.autoDownloadAudio);
    await prefs.setBool('auto_download_documents', _settings.autoDownloadDocuments);
    
    // إعدادات المظهر
    await prefs.setString('font_size', _settings.fontSize);
    await prefs.setString('chat_wallpaper', _settings.chatWallpaper);
    await prefs.setBool('dark_mode', _settings.darkMode);
    
    // إعدادات عامة
    await prefs.setString('language', _settings.language);
    await prefs.setString('backup_frequency', _settings.backupFrequency);
    
    notifyListeners();
  }

  // تحديث إعداد واحد
  Future<void> updateSetting(String key, dynamic value) async {
    switch (key) {
      case 'notifications_enabled':
        _settings = _settings.copyWith(notificationsEnabled: value);
        break;
      case 'message_preview':
        _settings = _settings.copyWith(messagePreview: value);
        break;
      case 'sound_enabled':
        _settings = _settings.copyWith(soundEnabled: value);
        break;
      case 'vibration_enabled':
        _settings = _settings.copyWith(vibrationEnabled: value);
        break;
      case 'read_receipts':
        _settings = _settings.copyWith(readReceipts: value);
        break;
      case 'last_seen':
        _settings = _settings.copyWith(lastSeen: value);
        break;
      case 'online_status':
        _settings = _settings.copyWith(onlineStatus: value);
        break;
      case 'auto_download_images':
        _settings = _settings.copyWith(autoDownloadImages: value);
        break;
      case 'auto_download_videos':
        _settings = _settings.copyWith(autoDownloadVideos: value);
        break;
      case 'auto_download_audio':
        _settings = _settings.copyWith(autoDownloadAudio: value);
        break;
      case 'auto_download_documents':
        _settings = _settings.copyWith(autoDownloadDocuments: value);
        break;
      case 'font_size':
        _settings = _settings.copyWith(fontSize: value);
        break;
      case 'chat_wallpaper':
        _settings = _settings.copyWith(chatWallpaper: value);
        break;
      case 'dark_mode':
        _settings = _settings.copyWith(darkMode: value);
        break;
      case 'language':
        _settings = _settings.copyWith(language: value);
        break;
      case 'backup_frequency':
        _settings = _settings.copyWith(backupFrequency: value);
        break;
    }
    await saveSettings();
  }

  // تحميل جهات الاتصال
  Future<void> _loadContacts() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _contacts = _dataService.mockUsers;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل جهات الاتصال';
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل المحادثات
  Future<void> loadChats() async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      _chats = _dataService.generateMockChats();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المحادثات';
      notifyListeners();
    }
  }

  // تحميل المحادثات المؤرشفة
  Future<void> _loadArchivedChats() async {
    try {
      _archivedChats = [
        Chat(
          id: 'archived_1',
          otherUser: User(
            id: 'archived_user_1',
            name: 'أحمد المؤرشف',
            email: '<EMAIL>',
            isOnline: false,
            lastSeen: DateTime.now().subtract(const Duration(days: 5)),
            joinDate: DateTime.now().subtract(const Duration(days: 30)),
          ),
          lastMessage: Message(
            id: 'archived_msg_1',
            senderId: 'archived_user_1',
            receiverId: 'current_user',
            content: 'هذه محادثة مؤرشفة قديمة',
            timestamp: DateTime.now().subtract(const Duration(days: 5)),
          ),
          lastActivity: DateTime.now().subtract(const Duration(days: 5)),
        ),
        Chat(
          id: 'archived_2',
          otherUser: User(
            id: 'archived_user_2',
            name: 'فاطمة المؤرشفة',
            email: '<EMAIL>',
            isOnline: false,
            lastSeen: DateTime.now().subtract(const Duration(days: 10)),
            joinDate: DateTime.now().subtract(const Duration(days: 60)),
          ),
          lastMessage: Message(
            id: 'archived_msg_2',
            senderId: 'current_user',
            receiverId: 'archived_user_2',
            content: 'آخر رسالة قبل الأرشفة',
            timestamp: DateTime.now().subtract(const Duration(days: 10)),
          ),
          lastActivity: DateTime.now().subtract(const Duration(days: 10)),
        ),
      ];
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المحادثات المؤرشفة';
      notifyListeners();
    }
  }

  // تحميل المجموعات
  Future<void> _loadGroupChats() async {
    try {
      _groupChats = [];
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المجموعات';
      notifyListeners();
    }
  }

  // إنشاء محادثة جديدة
  Future<Chat> createNewChat(User user) async {
    try {
      // التحقق من وجود محادثة سابقة
      final existingChat = _chats.firstWhere(
        (chat) => chat.otherUser.id == user.id,
        orElse: () => Chat(
          id: 'new_chat_${user.id}_${DateTime.now().millisecondsSinceEpoch}',
          otherUser: user,
          lastActivity: DateTime.now(),
        ),
      );

      if (!_chats.contains(existingChat)) {
        _chats.insert(0, existingChat);
        notifyListeners();
      }

      return existingChat;
    } catch (e) {
      _errorMessage = 'خطأ في إنشاء المحادثة';
      notifyListeners();
      rethrow;
    }
  }

  // إنشاء مجموعة جديدة
  Future<void> createGroup(GroupChat group) async {
    try {
      _groupChats.add(group);

      // إنشاء محادثة للمجموعة
      final groupChat = Chat(
        id: group.id,
        otherUser: User(
          id: group.id,
          name: group.name,
          email: '${group.name}@group.local',
          avatar: group.avatar,
          isOnline: true,
          joinDate: group.createdAt,
        ),
        lastActivity: group.lastActivity,
        type: ChatType.group,
        groupName: group.name,
        groupAvatar: group.avatar,
        groupMembers: group.members.map((m) => m.userId).toList(),
      );

      _chats.insert(0, groupChat);
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      _errorMessage = 'خطأ في إنشاء المجموعة: $e';
      notifyListeners();
    }
  }

  // أرشفة محادثة
  Future<void> archiveChat(String chatId) async {
    try {
      final chatIndex = _chats.indexWhere((chat) => chat.id == chatId);
      if (chatIndex != -1) {
        final chat = _chats.removeAt(chatIndex);
        _archivedChats.insert(0, chat);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في أرشفة المحادثة';
      notifyListeners();
    }
  }

  // إلغاء أرشفة محادثة
  Future<void> unarchiveChat(String chatId) async {
    try {
      final chatIndex = _archivedChats.indexWhere((chat) => chat.id == chatId);
      if (chatIndex != -1) {
        final chat = _archivedChats.removeAt(chatIndex);
        _chats.insert(0, chat);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في إلغاء أرشفة المحادثة';
      notifyListeners();
    }
  }

  // حذف محادثة مؤرشفة
  Future<void> deleteArchivedChat(String chatId) async {
    try {
      _archivedChats.removeWhere((chat) => chat.id == chatId);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في حذف المحادثة';
      notifyListeners();
    }
  }

  // إلغاء أرشفة جميع المحادثات
  Future<void> unarchiveAllChats() async {
    try {
      _chats.addAll(_archivedChats);
      _archivedChats.clear();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إلغاء أرشفة جميع المحادثات';
      notifyListeners();
    }
  }

  // حذف جميع المحادثات المؤرشفة
  Future<void> deleteAllArchivedChats() async {
    try {
      _archivedChats.clear();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في حذف جميع المحادثات المؤرشفة';
      notifyListeners();
    }
  }

  // إضافة جهة اتصال جديدة
  Future<void> addContact(User user) async {
    try {
      if (!_contacts.any((contact) => contact.id == user.id)) {
        _contacts.insert(0, user);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في إضافة جهة الاتصال';
      notifyListeners();
    }
  }

  // تحميل رسائل محادثة
  Future<void> loadChatMessages(String chatId) async {
    try {
      _isLoading = true;
      _currentChatId = chatId;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 500));

      // محاكاة تحميل الرسائل
      _currentChatMessages = _dataService.getMockMessages(chatId);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل الرسائل';
      _isLoading = false;
      notifyListeners();
    }
  }

  // إرسال رسالة
  Future<void> sendMessage(Message message) async {
    try {
      _currentChatMessages.add(message);

      // تحديث آخر رسالة في المحادثة
      final chatIndex = _chats.indexWhere((chat) => chat.id == _currentChatId);
      if (chatIndex != -1) {
        _chats[chatIndex] = _chats[chatIndex].copyWith(
          lastMessage: message,
          lastActivity: message.timestamp,
        );
      }

      notifyListeners();

      // محاكاة إرسال للخادم
      await Future.delayed(const Duration(milliseconds: 300));
    } catch (e) {
      _errorMessage = 'خطأ في إرسال الرسالة';
      notifyListeners();
    }
  }

  // تحديث رسالة
  Future<void> updateMessage(Message updatedMessage) async {
    try {
      // البحث عن الرسالة وتحديثها في القائمة
      final messageIndex = _currentChatMessages.indexWhere((msg) => msg.id == updatedMessage.id);
      if (messageIndex != -1) {
        _currentChatMessages[messageIndex] = updatedMessage;
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحديث الرسالة';
      notifyListeners();
    }
  }

  // مسح الأخطاء
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // تحديث حالة الكتابة
  void setTyping(bool typing) {
    _isTyping = typing;
    notifyListeners();
  }

  // تحديث حالة التسجيل
  void setRecording(bool recording) {
    _isRecording = recording;
    notifyListeners();
  }


}

// فئة إعدادات الماسنجر
class MessengerSettings {
  // إعدادات الإشعارات
  final bool notificationsEnabled;
  final bool messagePreview;
  final bool soundEnabled;
  final bool vibrationEnabled;
  
  // إعدادات الخصوصية
  final bool readReceipts;
  final bool lastSeen;
  final bool onlineStatus;
  
  // إعدادات التحميل التلقائي
  final bool autoDownloadImages;
  final bool autoDownloadVideos;
  final bool autoDownloadAudio;
  final bool autoDownloadDocuments;
  
  // إعدادات المظهر
  final String fontSize;
  final String chatWallpaper;
  final bool darkMode;
  
  // إعدادات عامة
  final String language;
  final String backupFrequency;

  const MessengerSettings({
    this.notificationsEnabled = true,
    this.messagePreview = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.readReceipts = true,
    this.lastSeen = true,
    this.onlineStatus = true,
    this.autoDownloadImages = true,
    this.autoDownloadVideos = false,
    this.autoDownloadAudio = true,
    this.autoDownloadDocuments = false,
    this.fontSize = 'متوسط',
    this.chatWallpaper = 'افتراضي',
    this.darkMode = false,
    this.language = 'العربية',
    this.backupFrequency = 'يومياً',
  });

  MessengerSettings copyWith({
    bool? notificationsEnabled,
    bool? messagePreview,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? readReceipts,
    bool? lastSeen,
    bool? onlineStatus,
    bool? autoDownloadImages,
    bool? autoDownloadVideos,
    bool? autoDownloadAudio,
    bool? autoDownloadDocuments,
    String? fontSize,
    String? chatWallpaper,
    bool? darkMode,
    String? language,
    String? backupFrequency,
  }) {
    return MessengerSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      messagePreview: messagePreview ?? this.messagePreview,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      readReceipts: readReceipts ?? this.readReceipts,
      lastSeen: lastSeen ?? this.lastSeen,
      onlineStatus: onlineStatus ?? this.onlineStatus,
      autoDownloadImages: autoDownloadImages ?? this.autoDownloadImages,
      autoDownloadVideos: autoDownloadVideos ?? this.autoDownloadVideos,
      autoDownloadAudio: autoDownloadAudio ?? this.autoDownloadAudio,
      autoDownloadDocuments: autoDownloadDocuments ?? this.autoDownloadDocuments,
      fontSize: fontSize ?? this.fontSize,
      chatWallpaper: chatWallpaper ?? this.chatWallpaper,
      darkMode: darkMode ?? this.darkMode,
      language: language ?? this.language,
      backupFrequency: backupFrequency ?? this.backupFrequency,
    );
  }
}
