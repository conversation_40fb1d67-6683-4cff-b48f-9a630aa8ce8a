import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'dart:convert';
import '../models/post.dart';
import '../models/story.dart';
import '../models/notification.dart';
import '../models/group.dart';
import '../models/video.dart';
import '../models/saved_post.dart';
import '../models/downloaded_image.dart';
import '../models/reaction_types.dart';
import '../models/link_preview.dart';
import '../services/social_data_service.dart';

class SocialProvider with ChangeNotifier {
  final SocialDataService _dataService = SocialDataService();
  final Uuid _uuid = const Uuid();
  
  List<Post> _posts = [];
  List<UserStories> _stories = [];
  List<AppNotification> _notifications = [];
  List<Group> _groups = [];
  VideoFeed? _videoFeed;
  bool _isLoading = false;
  String? _errorMessage;

  // قوائم جديدة للميزات المتقدمة
  final List<SavedPost> _savedPosts = [];
  final List<HiddenPost> _hiddenPosts = [];
  final List<BlockedUser> _blockedUsers = [];
  final List<PostReport> _reportedPosts = [];

  List<Post> get posts => _posts.where((post) =>
      !_hiddenPosts.any((hidden) => hidden.postId == post.id) &&
      !_blockedUsers.any((blocked) => blocked.blockedUserId == post.authorId)
  ).toList();
  List<UserStories> get stories => _stories;
  List<AppNotification> get notifications => _notifications;
  List<Group> get groups => _groups;
  VideoFeed? get videoFeed => _videoFeed;
  List<VideoPost> get reelsVideos => _videoFeed?.reelsVideos ?? [];
  List<VideoPost> get longVideos => _videoFeed?.longVideos ?? [];
  List<VideoPost> get allVideos => _videoFeed?.allVideos ?? [];
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // getters جديدة للميزات المتقدمة
  List<SavedPost> get savedPosts => _savedPosts;
  List<HiddenPost> get hiddenPosts => _hiddenPosts;
  List<BlockedUser> get blockedUsers => _blockedUsers;
  List<PostReport> get reportedPosts => _reportedPosts;

  // المنشورات المحفوظة مع تفاصيلها
  List<Post> get savedPostsWithDetails => _savedPosts
      .map((saved) => _posts.firstWhere((post) => post.id == saved.postId))
      .toList();

  int get unreadNotificationsCount =>
      _notifications.where((n) => !n.isRead).length;

  Future<void> loadPosts() async {
    _isLoading = true;
    notifyListeners();

    try {
      _posts = await _dataService.getPosts();
      _posts.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المنشورات';
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> loadStories() async {
    try {
      _stories = await _dataService.getStories();
      _stories = _stories.where((userStories) =>
          userStories.hasActiveStories).toList();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل القصص';
      notifyListeners();
    }
  }

  Future<void> loadNotifications() async {
    try {
      _notifications = await _dataService.getNotifications();
      _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل الإشعارات';
      notifyListeners();
    }
  }

  Future<void> createPost({
    required String content,
    PostType type = PostType.text,
    List<PostMedia> media = const [],
    PostBackground background = PostBackground.none,
    List<String> taggedUsers = const [],
    String? feeling,
    String? activity,
    String? location,
    String? feelingDisplay,
    String? activityDisplay,
    String? locationDisplay,
    File? imageFile,
    File? videoFile,
    LinkPreview? linkPreview,
  }) async {
    try {
      // معالجة الوسائط
      List<PostMedia> processedMedia = List.from(media);

      if (imageFile != null) {
        // في التطبيق الحقيقي، سيتم رفع الصورة إلى الخادم
        // هنا سنستخدم مسار الملف المحلي كـ URL مؤقت
        processedMedia.add(PostMedia(
          id: _uuid.v4(),
          type: PostType.image,
          url: imageFile.path,
          thumbnail: imageFile.path,
        ));
      }

      if (videoFile != null) {
        // في التطبيق الحقيقي، سيتم رفع الفيديو إلى الخادم
        // هنا سنستخدم مسار الملف المحلي كـ URL مؤقت
        processedMedia.add(PostMedia(
          id: _uuid.v4(),
          type: PostType.video,
          url: videoFile.path,
          thumbnail: videoFile.path, // في الحقيقة سيكون thumbnail مختلف
        ));
      }

      final post = Post(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: content,
        type: type,
        media: processedMedia,
        background: background,
        taggedUsers: taggedUsers,
        feeling: feeling,
        activity: activity,
        location: location,
        feelingDisplay: feelingDisplay,
        activityDisplay: activityDisplay,
        locationDisplay: locationDisplay,
        timestamp: DateTime.now(),
        linkPreview: linkPreview,
      );

      await _dataService.createPost(post);
      _posts.insert(0, post);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إنشاء المنشور';
      notifyListeners();
    }
  }

  Future<void> createStory({
    required String content,
    StoryType type = StoryType.text,
    String? mediaUrl,
    String? thumbnailUrl,
    StoryBackground background = StoryBackground.none,
    Duration? duration,
  }) async {
    try {
      final story = Story(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: content,
        type: type,
        mediaUrl: mediaUrl,
        thumbnailUrl: thumbnailUrl,
        background: background,
        duration: duration,
        timestamp: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
      );

      debugPrint('إنشاء قصة جديدة: ${story.id} - النوع: ${story.type}');
      if (story.mediaUrl != null) {
        debugPrint('رابط الوسائط: ${story.mediaUrl}');
      }

      await _dataService.createStory(story);
      await loadStories(); // إعادة تحميل القصص

      debugPrint('تم تحديث قائمة القصص. العدد الحالي: ${_stories.length}');

      // التأكد من إشعار المستمعين
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إنشاء القصة: $e');
      _errorMessage = 'خطأ في إنشاء القصة';
      notifyListeners();
    }
  }

  Future<void> likePost(String postId) async {
    try {
      final reaction = PostReaction(
        userId: 'current_user',
        type: 'like',
        timestamp: DateTime.now(),
      );

      await _dataService.addReaction(postId, reaction);
      
      // تحديث المنشور محلياً
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final reactions = List<PostReaction>.from(post.reactions);
        
        // إزالة التفاعل السابق من نفس المستخدم
        reactions.removeWhere((r) => r.userId == 'current_user');
        reactions.add(reaction);
        
        _posts[postIndex] = post.copyWith(reactions: reactions);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في الإعجاب بالمنشور';
      notifyListeners();
    }
  }

  Future<void> commentOnPost(String postId, String content) async {
    try {
      final comment = PostComment(
        id: _uuid.v4(),
        userId: 'current_user',
        content: content,
        timestamp: DateTime.now(),
      );

      await _dataService.addComment(postId, comment);
      
      // تحديث المنشور محلياً
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final comments = List<PostComment>.from(post.comments);
        comments.add(comment);
        
        _posts[postIndex] = post.copyWith(comments: comments);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في إضافة التعليق';
      notifyListeners();
    }
  }

  Future<void> sharePost(String postId) async {
    try {
      await _dataService.sharePost(postId);
      
      // تحديث المنشور محلياً
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        _posts[postIndex] = post.copyWith(shareCount: post.shareCount + 1);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في مشاركة المنشور';
      notifyListeners();
    }
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _dataService.markNotificationAsRead(notificationId);
      
      // تحديث الإشعار محلياً
      final notificationIndex = _notifications.indexWhere((n) => n.id == notificationId);
      if (notificationIndex != -1) {
        _notifications[notificationIndex] = _notifications[notificationIndex]
            .copyWith(status: NotificationStatus.read);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحديث الإشعار';
      notifyListeners();
    }
  }

  Future<void> markAllNotificationsAsRead() async {
    try {
      for (final notification in _notifications.where((n) => !n.isRead)) {
        await _dataService.markNotificationAsRead(notification.id);
      }
      
      // تحديث جميع الإشعارات محلياً
      _notifications = _notifications.map((n) => n.copyWith(status: NotificationStatus.read)).toList();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحديث الإشعارات';
      notifyListeners();
    }
  }

  bool hasUserLikedPost(String postId) {
    final post = _posts.firstWhere((p) => p.id == postId);
    return post.reactions.any((r) => 
        r.userId == 'current_user' && r.type == 'like');
  }

  int getPostLikesCount(String postId) {
    final post = _posts.firstWhere((p) => p.id == postId);
    return post.reactions.where((r) => r.type == 'like').length;
  }

  int getPostCommentsCount(String postId) {
    final post = _posts.firstWhere((p) => p.id == postId);
    return post.comments.length;
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<void> loadGroups() async {
    try {
      _groups = await _dataService.getGroups();
      _groups.sort((a, b) => b.lastActivity.compareTo(a.lastActivity));
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المجموعات';
      notifyListeners();
    }
  }

  Future<void> loadVideoFeed() async {
    try {
      _videoFeed = await _dataService.getVideoFeed();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل الفيديوهات';
      notifyListeners();
    }
  }

  Future<void> createGroup({
    required String name,
    required String description,
    GroupPrivacy privacy = GroupPrivacy.public,
    List<String> tags = const [],
    String? location,
  }) async {
    try {
      final group = Group(
        id: _uuid.v4(),
        name: name,
        description: description,
        privacy: privacy,
        createdBy: 'current_user',
        createdAt: DateTime.now(),
        members: [
          GroupMember(
            userId: 'current_user',
            role: GroupMemberRole.admin,
            joinedAt: DateTime.now(),
          ),
        ],
        tags: tags,
        location: location,
        lastActivity: DateTime.now(),
      );

      await _dataService.createGroup(group);
      _groups.insert(0, group);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إنشاء المجموعة';
      notifyListeners();
    }
  }

  // إضافة مجموعة جاهزة
  void addGroup(Group group) {
    _groups.insert(0, group);
    notifyListeners();
  }

  Future<void> joinGroup(String groupId) async {
    try {
      await _dataService.joinGroup(groupId, 'current_user');
      await loadGroups(); // إعادة تحميل المجموعات
    } catch (e) {
      _errorMessage = 'خطأ في الانضمام للمجموعة';
      notifyListeners();
    }
  }

  Future<void> leaveGroup(String groupId) async {
    try {
      await _dataService.leaveGroup(groupId, 'current_user');
      await loadGroups(); // إعادة تحميل المجموعات
    } catch (e) {
      _errorMessage = 'خطأ في مغادرة المجموعة';
      notifyListeners();
    }
  }

  Future<void> inviteFriendToGroup(String groupId, String friendId) async {
    try {
      // محاكاة إرسال دعوة
      await Future.delayed(const Duration(milliseconds: 500));

      // يمكن إضافة منطق حقيقي هنا لإرسال إشعار للصديق
      print('تم إرسال دعوة للصديق $friendId للانضمام للمجموعة $groupId');

    } catch (e) {
      _errorMessage = 'خطأ في إرسال الدعوة';
      notifyListeners();
      rethrow;
    }
  }

  Future<void> incrementVideoViews(String videoId) async {
    try {
      await _dataService.incrementVideoViews(videoId);

      // تحديث العدد محلياً
      if (_videoFeed != null) {
        final videos = _videoFeed!.allVideos;
        final videoIndex = videos.indexWhere((v) => v.id == videoId);
        if (videoIndex != -1) {
          final video = videos[videoIndex];
          videos[videoIndex] = video.copyWith(viewCount: video.viewCount + 1);
          _videoFeed = VideoFeed.fromVideos(videos);
          notifyListeners();
        }
      }
    } catch (e) {
      // تجاهل الخطأ - ليس مهماً جداً
    }
  }

  Future<void> likeVideo(String videoId) async {
    try {
      final reaction = PostReaction(
        userId: 'current_user',
        type: 'like',
        timestamp: DateTime.now(),
      );

      await _dataService.addReaction(videoId, reaction);

      // تحديث الفيديو محلياً
      if (_videoFeed != null) {
        final videos = _videoFeed!.allVideos;
        final videoIndex = videos.indexWhere((v) => v.id == videoId);
        if (videoIndex != -1) {
          final video = videos[videoIndex];
          final reactions = List<PostReaction>.from(video.reactions);

          // إزالة التفاعل السابق من نفس المستخدم
          reactions.removeWhere((r) => r.userId == 'current_user');
          reactions.add(reaction);

          videos[videoIndex] = video.copyWith(reactions: reactions);
          _videoFeed = VideoFeed.fromVideos(videos);
          notifyListeners();
        }
      }
    } catch (e) {
      _errorMessage = 'خطأ في الإعجاب بالفيديو';
      notifyListeners();
    }
  }

  Future<void> commentOnVideo(String videoId, String content) async {
    try {
      final comment = PostComment(
        id: _uuid.v4(),
        userId: 'current_user',
        content: content,
        timestamp: DateTime.now(),
      );

      await _dataService.addComment(videoId, comment);

      // تحديث الفيديو محلياً
      if (_videoFeed != null) {
        final videos = _videoFeed!.allVideos;
        final videoIndex = videos.indexWhere((v) => v.id == videoId);
        if (videoIndex != -1) {
          final video = videos[videoIndex];
          final comments = List<PostComment>.from(video.comments);
          comments.add(comment);

          videos[videoIndex] = video.copyWith(comments: comments);
          _videoFeed = VideoFeed.fromVideos(videos);
          notifyListeners();
        }
      }
    } catch (e) {
      _errorMessage = 'خطأ في إضافة التعليق';
      notifyListeners();
    }
  }

  Future<void> shareVideo(String videoId) async {
    try {
      await _dataService.sharePost(videoId);

      // تحديث الفيديو محلياً
      if (_videoFeed != null) {
        final videos = _videoFeed!.allVideos;
        final videoIndex = videos.indexWhere((v) => v.id == videoId);
        if (videoIndex != -1) {
          final video = videos[videoIndex];
          videos[videoIndex] = video.copyWith(shareCount: video.shareCount + 1);
          _videoFeed = VideoFeed.fromVideos(videos);
          notifyListeners();
        }
      }
    } catch (e) {
      _errorMessage = 'خطأ في مشاركة الفيديو';
      notifyListeners();
    }
  }

  bool hasUserLikedVideo(String videoId) {
    if (_videoFeed == null) return false;

    final video = _videoFeed!.allVideos.firstWhere(
      (v) => v.id == videoId,
      orElse: () => _videoFeed!.allVideos.first,
    );

    return video.reactions.any((r) =>
        r.userId == 'current_user' && r.type == 'like');
  }

  int getVideoLikesCount(String videoId) {
    if (_videoFeed == null) return 0;

    final video = _videoFeed!.allVideos.firstWhere(
      (v) => v.id == videoId,
      orElse: () => _videoFeed!.allVideos.first,
    );

    return video.reactions.where((r) => r.type == 'like').length;
  }

  int getVideoCommentsCount(String videoId) {
    if (_videoFeed == null) return 0;

    final video = _videoFeed!.allVideos.firstWhere(
      (v) => v.id == videoId,
      orElse: () => _videoFeed!.allVideos.first,
    );

    return video.comments.length;
  }

  // دوال الميزات الجديدة
  Future<void> savePost(String postId) async {
    try {
      final savedPost = SavedPost(
        id: _uuid.v4(),
        postId: postId,
        userId: 'current_user',
        savedAt: DateTime.now(),
      );

      _savedPosts.add(savedPost);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في حفظ المنشور';
      notifyListeners();
    }
  }

  // حذف منشور
  Future<void> deletePost(String postId) async {
    try {
      // حذف المنشور من القائمة الرئيسية
      _posts.removeWhere((post) => post.id == postId);

      // حذف المنشور من المحفوظات
      _savedPosts.removeWhere((saved) => saved.postId == postId);

      // حذف المنشور من المخفية
      _hiddenPosts.removeWhere((hidden) => hidden.postId == postId);

      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في حذف المنشور';
      notifyListeners();
    }
  }

  Future<void> hidePost(String postId) async {
    try {
      final hiddenPost = HiddenPost(
        id: _uuid.v4(),
        postId: postId,
        userId: 'current_user',
        hiddenAt: DateTime.now(),
      );

      _hiddenPosts.add(hiddenPost);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إخفاء المنشور';
      notifyListeners();
    }
  }

  Future<void> reportPost(String postId, String reason, String description) async {
    try {
      final report = PostReport(
        id: _uuid.v4(),
        postId: postId,
        reporterId: 'current_user',
        reason: reason,
        description: description,
        reportedAt: DateTime.now(),
        status: ReportStatus.pending,
      );

      _reportedPosts.add(report);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في الإبلاغ عن المنشور';
      notifyListeners();
    }
  }

  Future<void> blockUser(String userId) async {
    try {
      final blockedUser = BlockedUser(
        id: _uuid.v4(),
        blockedUserId: userId,
        blockerUserId: 'current_user',
        blockedAt: DateTime.now(),
        reason: 'تم الحظر من المنشور',
      );

      _blockedUsers.add(blockedUser);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في حظر المستخدم';
      notifyListeners();
    }
  }

  Future<void> unblockUser(String userId) async {
    try {
      _blockedUsers.removeWhere((blocked) =>
          blocked.blockedUserId == userId &&
          blocked.blockerUserId == 'current_user');
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إلغاء حظر المستخدم';
      notifyListeners();
    }
  }

  Future<void> unsavePost(String postId) async {
    try {
      _savedPosts.removeWhere((saved) =>
          saved.postId == postId &&
          saved.userId == 'current_user');
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إلغاء حفظ المنشور';
      notifyListeners();
    }
  }

  bool isPostSaved(String postId) {
    return _savedPosts.any((saved) =>
        saved.postId == postId &&
        saved.userId == 'current_user');
  }

  bool isUserBlocked(String userId) {
    return _blockedUsers.any((blocked) =>
        blocked.blockedUserId == userId &&
        blocked.blockerUserId == 'current_user');
  }

  Future<void> reportImage({
    required String imageId,
    required String postId,
    required String reason,
    required String description,
  }) async {
    try {
      final report = PostReport(
        id: _uuid.v4(),
        postId: postId,
        reporterId: 'current_user',
        reason: reason,
        description: '$description - Image ID: $imageId',
        reportedAt: DateTime.now(),
        status: ReportStatus.pending,
      );

      _reportedPosts.add(report);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في الإبلاغ عن الصورة';
      notifyListeners();
    }
  }

  // قائمة الصور المنزلة
  final List<DownloadedImage> _downloadedImages = [];
  List<DownloadedImage> get downloadedImages => _downloadedImages;

  Future<void> addDownloadedImage(String imagePath, String description) async {
    try {
      final downloadedImage = DownloadedImage(
        id: _uuid.v4(),
        imagePath: imagePath,
        description: description,
        downloadedAt: DateTime.now(),
        userId: 'current_user',
      );

      _downloadedImages.add(downloadedImage);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في حفظ معلومات التنزيل';
      notifyListeners();
    }
  }

  Future<void> shareImageAsPost({
    required String imageUrl,
    required String imageId,
    required String originalPostId,
    required String authorName,
  }) async {
    try {
      // إنشاء منشور جديد بالصورة المشاركة
      final sharedPost = Post(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: 'شارك صورة من منشور $authorName',
        type: PostType.image,
        media: [
          PostMedia(
            id: imageId,
            url: imageUrl,
            type: PostType.image,
          ),
        ],
        timestamp: DateTime.now(),
        reactions: [],
        comments: [],
        shareCount: 0,
        taggedUsers: [],
        location: null,
      );

      _posts.insert(0, sharedPost); // إضافة في المقدمة
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في مشاركة الصورة كمنشور';
      notifyListeners();
    }
  }

  Future<void> sendImageInMessage({
    required String imageUrl,
    required String imageId,
    required String recipientName,
    required String senderId,
  }) async {
    try {
      // في التطبيق الحقيقي، سيتم إرسال الصورة في المحادثة
      // هنا نحاكي العملية
      await Future.delayed(const Duration(milliseconds: 500));

      // يمكن إضافة نظام رسائل حقيقي هنا
      print('تم إرسال الصورة $imageId إلى $recipientName');

    } catch (e) {
      _errorMessage = 'خطأ في إرسال الصورة';
      notifyListeners();
    }
  }

  Future<void> shareImageInGroup({
    required String imageUrl,
    required String imageId,
    required String groupName,
    required String sharerId,
  }) async {
    try {
      // في التطبيق الحقيقي، سيتم مشاركة الصورة في المجموعة
      // هنا نحاكي العملية
      await Future.delayed(const Duration(milliseconds: 500));

      // يمكن إضافة نظام مجموعات حقيقي هنا
      print('تم مشاركة الصورة $imageId في مجموعة $groupName');

    } catch (e) {
      _errorMessage = 'خطأ في مشاركة الصورة في المجموعة';
      notifyListeners();
    }
  }

  Future<void> addComment({
    required String postId,
    required String content,
    required String userId,
  }) async {
    try {
      final comment = PostComment(
        id: _uuid.v4(),
        userId: userId,
        content: content,
        timestamp: DateTime.now(),
        reactions: [],
      );

      // البحث عن المنشور وإضافة التعليق
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        _posts[postIndex].comments.add(comment);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في إضافة التعليق';
      notifyListeners();
    }
  }

  // دوال التفاعلات المتعددة
  final Map<String, Map<String, ReactionType>> _postReactions = {};

  ReactionType? getUserReaction(String postId) {
    return _postReactions[postId]?['current_user'];
  }

  int getReactionCount(String postId) {
    return _postReactions[postId]?.length ?? 0;
  }

  Future<void> addReaction(String postId, ReactionType reaction) async {
    try {
      // تحديث المنشور في القائمة مباشرة
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final reactions = List<PostReaction>.from(post.reactions);

        // إزالة التفاعل السابق من المستخدم الحالي
        reactions.removeWhere((r) => r.userId == 'current_user');

        // إضافة التفاعل الجديد
        reactions.add(PostReaction(
          userId: 'current_user',
          type: reaction.name,
          timestamp: DateTime.now(),
        ));

        // تحديث المنشور بالتفاعلات الجديدة
        _posts[postIndex] = post.copyWith(reactions: reactions);

        // تحديث خريطة التفاعلات أيضاً
        if (_postReactions[postId] == null) {
          _postReactions[postId] = {};
        }
        _postReactions[postId]!['current_user'] = reaction;

        // حفظ التحديثات في التخزين المحلي
        await _saveUpdatedPostsToStorage();
      }

      notifyListeners();

    } catch (e) {
      _errorMessage = 'خطأ في إضافة التفاعل';
      notifyListeners();
    }
  }

  // محاكاة تحديث الخادم
  Future<void> _simulateServerUpdate(String postId, ReactionType reaction) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(milliseconds: 500));

      // في التطبيق الحقيقي، هنا سيتم إرسال الطلب للخادم
      print('تم إرسال التفاعل للخادم: $postId - ${reaction.name}');

      // محاكاة استجابة الخادم بإضافة تفاعلات أخرى
      await _simulateOtherUsersReactions(postId);

    } catch (e) {
      print('خطأ في تحديث الخادم: $e');
    }
  }

  // محاكاة تفاعلات مستخدمين آخرين
  Future<void> _simulateOtherUsersReactions(String postId) async {
    try {
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex == -1) return;

      final post = _posts[postIndex];
      final reactions = List<PostReaction>.from(post.reactions);

      // إضافة تفاعلات عشوائية من مستخدمين آخرين
      final otherUsers = [
        {'id': 'user_1', 'name': 'أحمد محمد'},
        {'id': 'user_2', 'name': 'فاطمة علي'},
        {'id': 'user_3', 'name': 'سعد الدين'},
        {'id': 'user_4', 'name': 'نور الهدى'},
        {'id': 'user_5', 'name': 'محمد سالم'},
      ];

      // إضافة 1-3 تفاعلات عشوائية
      final random = DateTime.now().millisecond;
      final numReactions = (random % 3) + 1;

      for (int i = 0; i < numReactions; i++) {
        final user = otherUsers[random % otherUsers.length];
        final reactionTypes = ReactionType.values;
        final randomReaction = reactionTypes[random % reactionTypes.length];

        // تحقق من عدم وجود تفاعل سابق من نفس المستخدم
        if (!reactions.any((r) => r.userId == user['id'])) {
          reactions.add(PostReaction(
            userId: user['id']!,
            type: randomReaction.name,
            timestamp: DateTime.now().subtract(Duration(minutes: random % 60)),
          ));
        }
      }

      _posts[postIndex] = post.copyWith(reactions: reactions);
      notifyListeners();

    } catch (e) {
      print('خطأ في محاكاة تفاعلات المستخدمين: $e');
    }
  }

  // دالة جديدة للتعامل مع PostReaction مباشرة (للنظام الجديد)
  Future<void> addPostReaction(String postId, PostReaction reaction) async {
    try {
      // تحديث المنشور في القائمة مباشرة
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final reactions = List<PostReaction>.from(post.reactions);

        // إزالة التفاعل السابق من المستخدم الحالي
        reactions.removeWhere((r) => r.userId == 'current_user');

        // إضافة التفاعل الجديد
        reactions.add(reaction);

        _posts[postIndex] = post.copyWith(reactions: reactions);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('خطأ في إضافة التفاعل: $e');
    }
  }

  Future<void> removeReaction(String postId) async {
    try {
      // تحديث المنشور في القائمة مباشرة
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final reactions = List<PostReaction>.from(post.reactions);

        // إزالة التفاعل من المستخدم الحالي
        reactions.removeWhere((r) => r.userId == 'current_user');

        // تحديث المنشور
        _posts[postIndex] = post.copyWith(reactions: reactions);

        // تحديث خريطة التفاعلات
        _postReactions[postId]?.remove('current_user');
        if (_postReactions[postId]?.isEmpty == true) {
          _postReactions.remove(postId);
        }

        // حفظ التحديثات
        await _saveUpdatedPostsToStorage();
      }

      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إزالة التفاعل';
      notifyListeners();
    }
  }

  // حفظ المنشورات المحدثة في التخزين المحلي
  Future<void> _saveUpdatedPostsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final postsJson = _posts.map((post) => jsonEncode(post.toJson())).toList();
      await prefs.setStringList('posts', postsJson);
    } catch (e) {
      // تجاهل الأخطاء في الحفظ
    }
  }

  // حفظ التفاعلات في التخزين المحلي
  Future<void> _saveReactionsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reactionsJson = <String, dynamic>{};

      _postReactions.forEach((postId, reactions) {
        reactionsJson[postId] = reactions.map((userId, reaction) =>
          MapEntry(userId, reaction.name));
      });

      await prefs.setString('post_reactions', jsonEncode(reactionsJson));
    } catch (e) {
      // تجاهل الأخطاء في الحفظ
    }
  }

  // تحميل التفاعلات من التخزين المحلي
  Future<void> _loadReactionsFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reactionsString = prefs.getString('post_reactions');

      if (reactionsString != null) {
        final reactionsJson = jsonDecode(reactionsString) as Map<String, dynamic>;

        _postReactions.clear();
        reactionsJson.forEach((postId, reactions) {
          _postReactions[postId] = {};
          (reactions as Map<String, dynamic>).forEach((userId, reactionName) {
            try {
              final reactionType = ReactionType.values.firstWhere(
                (r) => r.name == reactionName,
                orElse: () => ReactionType.like,
              );
              _postReactions[postId]![userId] = reactionType;
            } catch (e) {
              // تجاهل التفاعلات غير الصحيحة
            }
          });
        });
      }
    } catch (e) {
      print('خطأ في تحميل التفاعلات: $e');
    }
  }

  Future<void> repostPost(String postId) async {
    try {
      final originalPost = _posts.firstWhere((post) => post.id == postId);

      // إنشاء منشور جديد كإعادة نشر
      final repost = Post(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: 'أعاد نشر منشور',
        type: originalPost.type,
        media: originalPost.media,
        timestamp: DateTime.now(),
        reactions: [],
        comments: [],
        shareCount: 0,
        taggedUsers: [],
        location: null,
      );

      _posts.insert(0, repost); // إضافة في المقدمة

      // زيادة عداد المشاركات للمنشور الأصلي
      final originalIndex = _posts.indexWhere((post) => post.id == postId);
      if (originalIndex != -1) {
        _posts[originalIndex] = _posts[originalIndex].copyWith(
          shareCount: _posts[originalIndex].shareCount + 1,
        );
      }

      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إعادة نشر المنشور';
      notifyListeners();
    }
  }

  Future<void> repostWithComment(String postId, String comment) async {
    try {
      final originalPost = _posts.firstWhere((post) => post.id == postId);

      // إنشاء منشور جديد مع تعليق المستخدم
      final repost = Post(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: comment.isNotEmpty ? comment : 'أعاد نشر منشور',
        type: originalPost.type,
        media: originalPost.media,
        timestamp: DateTime.now(),
        reactions: [],
        comments: [],
        shareCount: 0,
        taggedUsers: [],
        location: null,
      );

      _posts.insert(0, repost);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إعادة نشر المنشور مع التعليق';
      notifyListeners();
    }
  }

  Future<void> sharePostAsNewPost(String postId) async {
    try {
      final originalPost = _posts.firstWhere((post) => post.id == postId);

      final sharedPost = Post(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: 'شارك منشور',
        type: originalPost.type,
        media: originalPost.media,
        timestamp: DateTime.now(),
        reactions: [],
        comments: [],
        shareCount: 0,
        taggedUsers: [],
        location: null,
      );

      _posts.insert(0, sharedPost);
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في مشاركة المنشور';
      notifyListeners();
    }
  }

  Future<void> sendPostInMessage(String postId, String recipientName) async {
    try {
      // محاكاة إرسال المنشور في رسالة
      await Future.delayed(const Duration(milliseconds: 500));
      print('تم إرسال المنشور $postId إلى $recipientName');
    } catch (e) {
      _errorMessage = 'خطأ في إرسال المنشور';
      notifyListeners();
    }
  }

  Future<void> sharePostInGroup(String postId, String groupName) async {
    try {
      // محاكاة مشاركة المنشور في مجموعة
      await Future.delayed(const Duration(milliseconds: 500));
      print('تم مشاركة المنشور $postId في مجموعة $groupName');
    } catch (e) {
      _errorMessage = 'خطأ في مشاركة المنشور في المجموعة';
      notifyListeners();
    }
  }

  Future<void> createRepost({
    required Post originalPost,
    required String userComment,
    File? imageFile,
    File? videoFile,
    LinkPreview? linkPreview,
  }) async {
    try {
      // معالجة الوسائط الجديدة
      List<PostMedia> newMedia = [];

      if (imageFile != null) {
        newMedia.add(PostMedia(
          id: _uuid.v4(),
          type: PostType.image,
          url: imageFile.path,
          thumbnail: imageFile.path,
        ));
      }

      if (videoFile != null) {
        newMedia.add(PostMedia(
          id: _uuid.v4(),
          type: PostType.video,
          url: videoFile.path,
          thumbnail: videoFile.path,
        ));
      }

      // إنشاء منشور إعادة النشر مع تضمين المنشور الأصلي في المحتوى
      String repostContent = '';
      if (userComment.isNotEmpty) {
        repostContent = '$userComment\n\n--- إعادة نشر ---\n${originalPost.content}';
      } else {
        repostContent = '--- إعادة نشر ---\n${originalPost.content}';
      }

      final repost = Post(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: repostContent,
        type: newMedia.isNotEmpty ? newMedia.first.type : originalPost.type,
        media: newMedia.isNotEmpty ? newMedia : originalPost.media,
        timestamp: DateTime.now(),
        reactions: [],
        comments: [],
        shareCount: 0,
        taggedUsers: [],
        location: originalPost.location,
        feeling: originalPost.feeling,
        activity: originalPost.activity,
        linkPreview: linkPreview ?? originalPost.linkPreview,
      );

      _posts.insert(0, repost);

      // زيادة عداد المشاركات للمنشور الأصلي
      final originalIndex = _posts.indexWhere((post) => post.id == originalPost.id);
      if (originalIndex != -1) {
        _posts[originalIndex] = _posts[originalIndex].copyWith(
          shareCount: _posts[originalIndex].shareCount + 1,
        );
      }

      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إعادة نشر المنشور';
      notifyListeners();
    }
  }

  Future<void> refreshAll() async {
    await Future.wait([
      loadPosts(),
      loadStories(),
      loadNotifications(),
      loadGroups(),
      loadVideoFeed(),
    ]);
  }

  // دوال المجموعات المتقدمة
  Future<void> loadGroupDetails(String groupId) async {
    try {
      // محاكاة تحميل البيانات من الخادم
      await Future.delayed(const Duration(milliseconds: 500));

      // تحديث آخر نشاط للمجموعة
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        _groups[groupIndex] = _groups[groupIndex].copyWith(
          lastActivity: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحميل تفاصيل المجموعة';
      notifyListeners();
    }
  }

  // تحميل منشورات المجموعة
  Future<void> loadGroupPosts(String groupId) async {
    try {
      // محاكاة تحميل منشورات المجموعة
      await Future.delayed(const Duration(milliseconds: 500));
      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحميل منشورات المجموعة';
      notifyListeners();
    }
  }

  // الحصول على منشورات المجموعة
  List<dynamic> getGroupPosts(String groupId) {
    try {
      // إرجاع المنشورات المرتبطة بالمجموعة
      return _posts.where((post) => post.groupId == groupId).toList();
    } catch (e) {
      return [];
    }
  }

  // الحصول على أحداث المجموعة
  List<dynamic> getGroupEvents(String groupId) {
    try {
      // TODO: إضافة دعم الأحداث الحقيقي
      return [];
    } catch (e) {
      return [];
    }
  }

  // إنشاء منشور في المجموعة
  Future<void> createGroupPost({
    required String groupId,
    required String content,
    String? feeling,
    String? activity,
    String? location,
    String? postType,
  }) async {
    try {
      final post = Post(
        id: _uuid.v4(),
        authorId: 'current_user',
        content: content,
        type: PostType.text,
        groupId: groupId,
        feeling: feeling,
        activity: activity,
        location: location,
        timestamp: DateTime.now(),
        reactions: [],
        comments: [],
        shareCount: 0,
      );

      _posts.insert(0, post);

      // تحديث عدد المنشورات في المجموعة
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        _groups[groupIndex] = _groups[groupIndex].copyWith(
          lastActivity: DateTime.now(),
        );
      }

      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في إنشاء المنشور';
      notifyListeners();
      rethrow;
    }
  }
}
