class SavedPost {
  final String id;
  final String postId;
  final String userId;
  final DateTime savedAt;

  SavedPost({
    required this.id,
    required this.postId,
    required this.userId,
    required this.savedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'postId': postId,
      'userId': userId,
      'savedAt': savedAt.toIso8601String(),
    };
  }

  factory SavedPost.fromJson(Map<String, dynamic> json) {
    return SavedPost(
      id: json['id'],
      postId: json['postId'],
      userId: json['userId'],
      savedAt: DateTime.parse(json['savedAt']),
    );
  }
}

class BlockedUser {
  final String id;
  final String blockedUserId;
  final String blockerUserId;
  final DateTime blockedAt;
  final String reason;

  BlockedUser({
    required this.id,
    required this.blockedUserId,
    required this.blockerUserId,
    required this.blockedAt,
    required this.reason,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'blockedUserId': blockedUserId,
      'blockerUserId': blockerUserId,
      'blockedAt': blockedAt.toIso8601String(),
      'reason': reason,
    };
  }

  factory BlockedUser.fromJson(Map<String, dynamic> json) {
    return BlockedUser(
      id: json['id'],
      blockedUserId: json['blockedUserId'],
      blockerUserId: json['blockerUserId'],
      blockedAt: DateTime.parse(json['blockedAt']),
      reason: json['reason'] ?? '',
    );
  }
}

class HiddenPost {
  final String id;
  final String postId;
  final String userId;
  final DateTime hiddenAt;

  HiddenPost({
    required this.id,
    required this.postId,
    required this.userId,
    required this.hiddenAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'postId': postId,
      'userId': userId,
      'hiddenAt': hiddenAt.toIso8601String(),
    };
  }

  factory HiddenPost.fromJson(Map<String, dynamic> json) {
    return HiddenPost(
      id: json['id'],
      postId: json['postId'],
      userId: json['userId'],
      hiddenAt: DateTime.parse(json['hiddenAt']),
    );
  }
}

class PostReport {
  final String id;
  final String postId;
  final String reporterId;
  final String reason;
  final String description;
  final DateTime reportedAt;
  final ReportStatus status;

  PostReport({
    required this.id,
    required this.postId,
    required this.reporterId,
    required this.reason,
    required this.description,
    required this.reportedAt,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'postId': postId,
      'reporterId': reporterId,
      'reason': reason,
      'description': description,
      'reportedAt': reportedAt.toIso8601String(),
      'status': status.toString(),
    };
  }

  factory PostReport.fromJson(Map<String, dynamic> json) {
    return PostReport(
      id: json['id'],
      postId: json['postId'],
      reporterId: json['reporterId'],
      reason: json['reason'],
      description: json['description'],
      reportedAt: DateTime.parse(json['reportedAt']),
      status: ReportStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => ReportStatus.pending,
      ),
    );
  }
}

enum ReportStatus {
  pending,
  reviewed,
  resolved,
  dismissed,
}
