import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/connectivity_provider.dart';
import '../models/connectivity_status.dart';
import '../widgets/connectivity_banner.dart';

class ConnectivitySettingsScreen extends StatefulWidget {
  const ConnectivitySettingsScreen({super.key});

  @override
  State<ConnectivitySettingsScreen> createState() => _ConnectivitySettingsScreenState();
}

class _ConnectivitySettingsScreenState extends State<ConnectivitySettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'إعدادات الاتصال',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<ConnectivityProvider>(
        builder: (context, connectivityProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // حالة الاتصال الحالية
              _buildCurrentStatusCard(connectivityProvider),
              
              const SizedBox(height: 20),
              
              // إحصائيات الاتصال
              _buildConnectionStats(connectivityProvider),
              
              const SizedBox(height: 20),
              
              // الإجراءات المعلقة
              _buildPendingActions(connectivityProvider),
              
              const SizedBox(height: 20),
              
              // إعدادات التخزين المؤقت
              _buildCacheSettings(connectivityProvider),
              
              const SizedBox(height: 20),
              
              // أدوات التشخيص
              _buildDiagnosticTools(connectivityProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCurrentStatusCard(ConnectivityProvider provider) {
    final networkState = provider.networkState;
    
    Color statusColor;
    IconData statusIcon;
    
    switch (networkState.status) {
      case ConnectivityStatus.connected:
        statusColor = Colors.green;
        statusIcon = Icons.wifi;
        break;
      case ConnectivityStatus.disconnected:
        statusColor = Colors.red;
        statusIcon = Icons.wifi_off;
        break;
      case ConnectivityStatus.reconnecting:
        statusColor = Colors.orange;
        statusIcon = Icons.wifi_find;
        break;
      case ConnectivityStatus.unknown:
        statusColor = Colors.grey;
        statusIcon = Icons.help_outline;
        break;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 24),
                const SizedBox(width: 12),
                Text(
                  'حالة الاتصال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الحالة',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        networkState.statusDisplayName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نوع الاتصال',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        networkState.typeDisplayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            if (networkState.reconnectAttempts > 0) ...[
              const SizedBox(height: 12),
              Text(
                'محاولات إعادة الاتصال: ${networkState.reconnectAttempts}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.orange[700],
                ),
              ),
            ],
            
            if (!networkState.isOnline) ...[
              const SizedBox(height: 12),
              Text(
                'آخر اتصال: ${networkState.formattedTimeSinceLastConnection}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStats(ConnectivityProvider provider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات الاتصال',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            
            const SizedBox(height: 16),
            
            FutureBuilder<Map<String, dynamic>>(
              future: provider.getCacheInfo(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                final cacheInfo = snapshot.data!;
                final stats = cacheInfo['statistics'] ?? {};
                
                return Column(
                  children: [
                    _buildStatRow('نجح التخزين المؤقت', '${stats['hits'] ?? 0}'),
                    _buildStatRow('فشل التخزين المؤقت', '${stats['misses'] ?? 0}'),
                    _buildStatRow('معدل النجاح', '${((stats['hitRate'] ?? 0.0) * 100).toStringAsFixed(1)}%'),
                    _buildStatRow('حجم التخزين المؤقت', '${cacheInfo['memoryCache']?['size'] ?? 0} عنصر'),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingActions(ConnectivityProvider provider) {
    final pendingCount = provider.pendingActionsCount;
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الإجراءات المعلقة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                if (pendingCount > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '$pendingCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (pendingCount == 0)
              Text(
                'لا توجد إجراءات معلقة',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              )
            else ...[
              Text(
                'هناك $pendingCount إجراء في انتظار الاتصال بالإنترنت',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.orange[700],
                ),
              ),
              
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: provider.isOnline ? () => provider.retryFailedActions() : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _showClearPendingDialog(provider),
                      child: const Text('مسح الكل'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCacheSettings(ConnectivityProvider provider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات التخزين المؤقت',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.storage, color: Colors.blue),
              title: const Text('مسح التخزين المؤقت'),
              subtitle: const Text('حذف جميع البيانات المحفوظة مؤقتاً'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showClearCacheDialog(provider),
            ),
            
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.info_outline, color: Colors.green),
              title: const Text('معلومات التخزين المؤقت'),
              subtitle: const Text('عرض تفاصيل التخزين المؤقت'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showCacheInfoDialog(provider),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticTools(ConnectivityProvider provider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أدوات التشخيص',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.refresh, color: Colors.blue),
              title: const Text('فحص الاتصال'),
              subtitle: const Text('التحقق من حالة الاتصال يدوياً'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => provider.forceConnectivityCheck(),
            ),
            
            const Divider(),
            
            ListTile(
              leading: const ConnectivityIndicator(size: 20),
              title: const Text('مؤشر الاتصال'),
              subtitle: const Text('عرض حالة الاتصال الحالية'),
              trailing: const ConnectivityIndicator(showLabel: true),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearPendingDialog(ConnectivityProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح الإجراءات المعلقة'),
        content: const Text('هل تريد حذف جميع الإجراءات المعلقة؟ لن يتم تنفيذها عند عودة الاتصال.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              provider.clearPendingActions();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح الإجراءات المعلقة')),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog(ConnectivityProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح التخزين المؤقت'),
        content: const Text('هل تريد حذف جميع البيانات المحفوظة مؤقتاً؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              await provider.clearCache();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح التخزين المؤقت')),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showCacheInfoDialog(ConnectivityProvider provider) async {
    final cacheInfo = await provider.getCacheInfo();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التخزين المؤقت'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('الذاكرة: ${cacheInfo['memoryCache']?['size'] ?? 0} عنصر'),
              Text('القرص: ${cacheInfo['diskCache']?['size'] ?? 0} عنصر'),
              Text('النجاح: ${cacheInfo['statistics']?['hits'] ?? 0}'),
              Text('الفشل: ${cacheInfo['statistics']?['misses'] ?? 0}'),
              Text('معدل النجاح: ${((cacheInfo['statistics']?['hitRate'] ?? 0.0) * 100).toStringAsFixed(1)}%'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
