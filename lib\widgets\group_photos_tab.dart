import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../models/post.dart';
import '../providers/social_provider.dart';

class GroupPhotosTab extends StatefulWidget {
  final Group group;

  const GroupPhotosTab({
    super.key,
    required this.group,
  });

  @override
  State<GroupPhotosTab> createState() => _GroupPhotosTabState();
}

class _GroupPhotosTabState extends State<GroupPhotosTab> {
  String _selectedFilter = 'all'; // all, recent, popular

  @override
  Widget build(BuildContext context) {
    return Consumer<SocialProvider>(
      builder: (context, socialProvider, child) {
        final groupPosts = socialProvider.getGroupPosts(widget.group.id);
        final photos = _extractPhotos(groupPosts);
        final filteredPhotos = _filterPhotos(photos);

        return Column(
          children: [
            // فلاتر الصور
            _buildPhotoFilters(),
            
            // عرض الصور
            Expanded(
              child: filteredPhotos.isEmpty
                  ? _buildEmptyState()
                  : _buildPhotosGrid(filteredPhotos),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPhotoFilters() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          _buildFilterChip('all', 'جميع الصور'),
          const SizedBox(width: 8),
          _buildFilterChip('recent', 'الأحدث'),
          const SizedBox(width: 8),
          _buildFilterChip('popular', 'الأكثر تفاعلاً'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  Widget _buildPhotosGrid(List<PhotoItem> photos) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: photos.length,
      itemBuilder: (context, index) {
        final photo = photos[index];
        return _buildPhotoItem(photo, index, photos);
      },
    );
  }

  Widget _buildPhotoItem(PhotoItem photo, int index, List<PhotoItem> allPhotos) {
    return GestureDetector(
      onTap: () => _openPhotoViewer(index, allPhotos),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // الصورة
              Image.network(
                photo.url,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.broken_image,
                      color: Colors.grey,
                      size: 32,
                    ),
                  );
                },
              ),
              
              // تدرج لوني للنص
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.5),
                    ],
                  ),
                ),
              ),
              
              // معلومات الصورة
              Positioned(
                bottom: 4,
                left: 4,
                right: 4,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // عدد الإعجابات
                    if (photo.likesCount > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.favorite,
                              color: Colors.red,
                              size: 10,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              '${photo.likesCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    
                    // عدد التعليقات
                    if (photo.commentsCount > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.comment,
                              color: Colors.blue,
                              size: 10,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              '${photo.commentsCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد صور',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم نشر أي صور في هذه المجموعة بعد',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.group.isUserMember('current_user')) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _uploadPhoto(),
              icon: const Icon(Icons.add_a_photo),
              label: const Text('رفع صورة'),
            ),
          ],
        ],
      ),
    );
  }

  List<PhotoItem> _extractPhotos(List<dynamic> posts) {
    List<PhotoItem> photos = [];
    
    for (final post in posts) {
      if (post is Post && post.media.isNotEmpty) {
        for (final media in post.media) {
          if (media.type == PostType.image) {
            photos.add(PhotoItem(
              url: media.url,
              postId: post.id,
              authorId: post.authorId,
              timestamp: post.timestamp,
              likesCount: post.reactions.length,
              commentsCount: post.comments.length,
              caption: post.content,
            ));
          }
        }
      }
    }
    
    return photos;
  }

  List<PhotoItem> _filterPhotos(List<PhotoItem> photos) {
    switch (_selectedFilter) {
      case 'recent':
        photos.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        break;
      case 'popular':
        photos.sort((a, b) => (b.likesCount + b.commentsCount)
            .compareTo(a.likesCount + a.commentsCount));
        break;
      default:
        // الترتيب الافتراضي حسب التاريخ
        photos.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    }
    
    return photos;
  }

  void _openPhotoViewer(int initialIndex, List<PhotoItem> photos) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoViewerScreen(
          photos: photos,
          initialIndex: initialIndex,
          group: widget.group,
        ),
      ),
    );
  }

  void _uploadPhoto() {
    // TODO: فتح شاشة رفع الصور
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('رفع صورة جديدة')),
    );
  }
}

class PhotoItem {
  final String url;
  final String postId;
  final String authorId;
  final DateTime timestamp;
  final int likesCount;
  final int commentsCount;
  final String caption;

  const PhotoItem({
    required this.url,
    required this.postId,
    required this.authorId,
    required this.timestamp,
    required this.likesCount,
    required this.commentsCount,
    required this.caption,
  });
}

class PhotoViewerScreen extends StatefulWidget {
  final List<PhotoItem> photos;
  final int initialIndex;
  final Group group;

  const PhotoViewerScreen({
    super.key,
    required this.photos,
    required this.initialIndex,
    required this.group,
  });

  @override
  State<PhotoViewerScreen> createState() => _PhotoViewerScreenState();
}

class _PhotoViewerScreenState extends State<PhotoViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text('${_currentIndex + 1} من ${widget.photos.length}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _sharePhoto(),
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () => _downloadPhoto(),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'save',
                child: ListTile(
                  leading: Icon(Icons.bookmark),
                  title: Text('حفظ الصورة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'report',
                child: ListTile(
                  leading: Icon(Icons.report),
                  title: Text('الإبلاغ'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemCount: widget.photos.length,
        itemBuilder: (context, index) {
          final photo = widget.photos[index];
          return _buildPhotoPage(photo);
        },
      ),
    );
  }

  Widget _buildPhotoPage(PhotoItem photo) {
    return Column(
      children: [
        // الصورة
        Expanded(
          child: Center(
            child: InteractiveViewer(
              child: Image.network(
                photo.url,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      color: Colors.white,
                      size: 64,
                    ),
                  );
                },
              ),
            ),
          ),
        ),
        
        // معلومات الصورة
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اسم المؤلف والتاريخ
              Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Theme.of(context).primaryColor,
                    child: Text(
                      _getUserName(photo.authorId)[0].toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getUserName(photo.authorId),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _formatDate(photo.timestamp),
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              if (photo.caption.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  photo.caption,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
              
              const SizedBox(height: 12),
              
              // إحصائيات التفاعل
              Row(
                children: [
                  Icon(
                    Icons.favorite,
                    color: Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${photo.likesCount}',
                    style: const TextStyle(color: Colors.white70),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.comment,
                    color: Colors.blue,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${photo.commentsCount}',
                    style: const TextStyle(color: Colors.white70),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getUserName(String userId) {
    switch (userId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays < 1) {
      return 'اليوم';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _sharePhoto() {
    // TODO: مشاركة الصورة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة الصورة')),
    );
  }

  void _downloadPhoto() {
    // TODO: تحميل الصورة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحميل الصورة')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'save':
        // TODO: حفظ الصورة
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ الصورة')),
        );
        break;
      case 'report':
        // TODO: الإبلاغ عن الصورة
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم الإبلاغ عن الصورة')),
        );
        break;
    }
  }
}
