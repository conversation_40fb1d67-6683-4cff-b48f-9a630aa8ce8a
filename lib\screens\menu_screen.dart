import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'saved_items_screen.dart';
import 'settings_screen.dart';
import 'profile_screen.dart';
import '../services/auth_service.dart';
import 'facebook_login_screen.dart';

class MenuScreen extends StatelessWidget {
  const MenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // الأقسام الرئيسية
            _buildMenuSection(
              title: 'حسابي',
              items: [
                _MenuItem(
                  icon: Icons.person,
                  title: 'الملف الشخصي',
                  subtitle: 'عرض وتعديل ملفك الشخصي',
                  onTap: () => _navigateToProfile(context),
                ),
                _MenuItem(
                  icon: Icons.bookmark,
                  title: 'العناصر المحفوظة',
                  subtitle: 'المنشورات والفيديوهات المحفوظة',
                  onTap: () => _navigateToSavedItems(context),
                ),
              ],
            ),

            const SizedBox(height: 20),

            _buildMenuSection(
              title: 'الإعدادات',
              items: [
                _MenuItem(
                  icon: Icons.settings,
                  title: 'إعدادات التطبيق',
                  subtitle: 'تخصيص تجربة الاستخدام',
                  onTap: () => _navigateToSettings(context),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            _buildMenuSection(
              title: 'المساعدة والدعم',
              items: [
                _MenuItem(
                  icon: Icons.help_outline,
                  title: 'مركز المساعدة',
                  subtitle: 'الأسئلة الشائعة والدعم',
                  onTap: () => _showHelpCenter(context),
                ),
                _MenuItem(
                  icon: Icons.info_outline,
                  title: 'حول التطبيق',
                  subtitle: 'معلومات عن Arzawo',
                  onTap: () => _showAboutApp(context),
                ),
                _MenuItem(
                  icon: Icons.feedback_outlined,
                  title: 'إرسال ملاحظات',
                  subtitle: 'شاركنا رأيك لتحسين التطبيق',
                  onTap: () => _showFeedback(context),
                ),
              ],
            ),
            
            const SizedBox(height: 30),
            
            // زر تسجيل الخروج
            _buildLogoutButton(context),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuSection({
    required String title,
    required List<_MenuItem> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8, bottom: 12),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isLast = index == items.length - 1;
              
              return Column(
                children: [
                  ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        item.icon,
                        color: AppTheme.primaryColor,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      item.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    subtitle: Text(
                      item.subtitle,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    trailing: Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.grey[400],
                      size: 16,
                    ),
                    onTap: item.onTap,
                  ),
                  if (!isLast)
                    Divider(
                      height: 1,
                      color: Colors.grey[200],
                      indent: 72,
                    ),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: const Icon(
            Icons.logout,
            color: Colors.red,
            size: 20,
          ),
        ),
        title: const Text(
          'تسجيل الخروج',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: Colors.red,
          ),
        ),
        subtitle: const Text(
          'الخروج من حسابك الحالي',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
        onTap: () => _showLogoutDialog(context),
      ),
    );
  }

  void _navigateToProfile(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProfileScreen(
          userId: 'current_user',
          userName: 'أنت',
        ),
      ),
    );
  }

  void _navigateToSavedItems(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SavedItemsScreen()),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  void _showHelpCenter(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مركز المساعدة قريباً!')),
    );
  }

  void _showAboutApp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول Arzawo'),
        content: const Text(
          'Arzawo هو تطبيق شامل للتواصل الاجتماعي يجمع بين الدردشة والمنشورات والفيديوهات والمجموعات في مكان واحد.\n\nالإصدار: 1.0.0\nتم التطوير بـ Flutter',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showFeedback(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نموذج الملاحظات قريباً!')),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              // استخدام AuthService الجديد
              final authService = AuthService();
              await authService.logout();

              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const FacebookLoginScreen()),
                  (route) => false,
                );
              }
            },
            child: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}

class _MenuItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _MenuItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });
}
