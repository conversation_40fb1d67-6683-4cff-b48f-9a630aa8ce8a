import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../models/group.dart';
import '../providers/social_provider.dart';
import '../theme/app_theme.dart';

class CreateGroupPostDialog extends StatefulWidget {
  final Group group;
  final String? initialType;

  const CreateGroupPostDialog({
    super.key,
    required this.group,
    this.initialType,
  });

  @override
  State<CreateGroupPostDialog> createState() => _CreateGroupPostDialogState();
}

class _CreateGroupPostDialogState extends State<CreateGroupPostDialog> {
  final TextEditingController _contentController = TextEditingController();
  final FocusNode _contentFocus = FocusNode();
  String _selectedPostType = 'text';
  String? _selectedFeeling;
  String? _selectedActivity;
  String? _selectedLocation;
  bool _isSubmitting = false;

  // متغيرات الوسائط
  File? _selectedImage;
  File? _selectedVideo;
  final ImagePicker _picker = ImagePicker();

  // متغيرات الاستطلاع
  final List<TextEditingController> _pollOptions = [];
  bool _allowMultipleChoices = false;

  final List<String> _feelings = [
    'سعيد', 'متحمس', 'ممتن', 'مسترخي', 'متعب', 'حزين'
  ];

  final List<String> _activities = [
    'احتفال', 'أكل', 'حضور', 'استماع', 'تفكير', 'ألعاب',
    'مشاهدة', 'شرب', 'سفر', 'بحث', 'قراءة', 'دعم'
  ];

  final List<String> _locations = [
    'المنزل', 'العمل', 'المدرسة', 'المقهى', 'المطعم', 'الحديقة'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialType != null) {
      _selectedPostType = widget.initialType!;
    }
    
    // التركيز على حقل النص
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _contentFocus.requestFocus();
    });
  }

  @override
  void dispose() {
    _contentController.dispose();
    _contentFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // شريط العنوان
          _buildHeader(),
          
          // محتوى الحوار
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المستخدم والمجموعة
                  _buildUserInfo(),
                  
                  const SizedBox(height: 16),
                  
                  // حقل النص
                  _buildContentField(),
                  
                  const SizedBox(height: 16),
                  
                  // خيارات المشاعر والأنشطة
                  _buildEmotionsAndActivities(),
                  
                  const SizedBox(height: 16),
                  
                  // خيارات نوع المنشور
                  _buildPostTypeOptions(),

                  const SizedBox(height: 16),

                  // عرض الوسائط المختارة
                  if (_selectedImage != null || _selectedVideo != null)
                    _buildSelectedMedia(),

                  if (_selectedImage != null || _selectedVideo != null)
                    const SizedBox(height: 16),
                  
                  // إعدادات المنشور
                  _buildPostSettings(),
                ],
              ),
            ),
          ),
          
          // شريط الإجراءات
          _buildActionBar(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // زر الإغلاق
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
          ),
          
          const SizedBox(width: 8),
          
          // العنوان
          Expanded(
            child: Text(
              'إنشاء منشور في ${widget.group.name}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // زر النشر
          ElevatedButton(
            onPressed: _isSubmitting ? null : _submitPost,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: _isSubmitting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('نشر'),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return Row(
      children: [
        // صورة المستخدم
        CircleAvatar(
          radius: 24,
          backgroundColor: AppTheme.primaryColor,
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 24,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // معلومات المستخدم
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'أنت',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  Text(
                    'في مجموعة ${widget.group.name}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.public,
                    size: 12,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentField() {
    return TextField(
      controller: _contentController,
      focusNode: _contentFocus,
      maxLines: null,
      minLines: 3,
      decoration: InputDecoration(
        hintText: 'ما الذي تريد مشاركته مع المجموعة؟',
        hintStyle: TextStyle(
          color: Colors.grey[500],
          fontSize: 16,
        ),
        border: InputBorder.none,
      ),
      style: const TextStyle(fontSize: 16),
    );
  }

  Widget _buildEmotionsAndActivities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // المشاعر
        Row(
          children: [
            Icon(Icons.mood, color: AppTheme.primaryColor, size: 20),
            const SizedBox(width: 8),
            const Text(
              'كيف تشعر؟',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _feelings.map((feeling) {
            final isSelected = _selectedFeeling == feeling;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedFeeling = isSelected ? null : feeling;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppTheme.primaryColor.withValues(alpha: 0.1)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected 
                        ? AppTheme.primaryColor 
                        : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  feeling,
                  style: TextStyle(
                    color: isSelected ? AppTheme.primaryColor : Colors.grey[700],
                    fontSize: 12,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        
        const SizedBox(height: 16),
        
        // الأنشطة
        Row(
          children: [
            Icon(Icons.local_activity, color: AppTheme.primaryColor, size: 20),
            const SizedBox(width: 8),
            const Text(
              'ماذا تفعل؟',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _activities.map((activity) {
            final isSelected = _selectedActivity == activity;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedActivity = isSelected ? null : activity;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppTheme.primaryColor.withValues(alpha: 0.1)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected 
                        ? AppTheme.primaryColor 
                        : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  activity,
                  style: TextStyle(
                    color: isSelected ? AppTheme.primaryColor : Colors.grey[700],
                    fontSize: 12,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPostTypeOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إضافة إلى منشورك',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            _buildPostTypeButton(
              icon: Icons.photo_library,
              label: 'صورة',
              type: 'photo',
              color: Colors.green,
            ),
            
            _buildPostTypeButton(
              icon: Icons.videocam,
              label: 'فيديو',
              type: 'video',
              color: Colors.red,
            ),
            
            _buildPostTypeButton(
              icon: Icons.poll,
              label: 'استطلاع',
              type: 'poll',
              color: Colors.blue,
            ),
            
            _buildPostTypeButton(
              icon: Icons.location_on,
              label: 'موقع',
              type: 'location',
              color: Colors.orange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPostTypeButton({
    required IconData icon,
    required String label,
    required String type,
    required Color color,
  }) {
    final isSelected = _selectedPostType == type;
    
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPostType = type;
          });

          // تنفيذ الإجراءات حسب النوع
          if (type == 'photo') {
            _pickImage();
          } else if (type == 'video') {
            _pickVideo();
          } else if (type == 'location') {
            _showLocationPicker();
          } else if (type == 'poll') {
            _initializePoll();
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? color.withValues(alpha: 0.1) : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? color : Colors.grey[300]!,
            ),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? color : Colors.grey[600],
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? color : Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إعدادات المنشور',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // إعدادات الخصوصية والموافقة
        Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                ListTile(
                  leading: Icon(Icons.visibility, color: AppTheme.primaryColor),
                  title: const Text('مرئي لجميع أعضاء المجموعة'),
                  subtitle: const Text('سيتمكن جميع الأعضاء من رؤية هذا المنشور'),
                  contentPadding: EdgeInsets.zero,
                ),
                
                if (widget.group.requiresPostApproval) ...[
                  const Divider(),
                  ListTile(
                    leading: Icon(Icons.approval, color: Colors.orange),
                    title: const Text('يتطلب موافقة المشرف'),
                    subtitle: const Text('سيتم مراجعة المنشور قبل النشر'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedMedia() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _selectedImage != null ? Icons.image : Icons.videocam,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _selectedImage != null ? 'صورة مختارة' : 'فيديو مختار',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedImage = null;
                    _selectedVideo = null;
                    _selectedPostType = 'text';
                  });
                },
                icon: const Icon(Icons.close, size: 20),
                tooltip: 'إزالة',
              ),
            ],
          ),

          const SizedBox(height: 8),

          if (_selectedImage != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                _selectedImage!,
                height: 150,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),

          if (_selectedVideo != null)
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.play_circle_outline,
                    size: 48,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'فيديو جاهز للنشر',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          // معاينة المحتوى
          if (_contentController.text.isNotEmpty ||
              _selectedFeeling != null ||
              _selectedActivity != null)
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _buildPreviewText(),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          
          const SizedBox(width: 12),
          
          // زر النشر الكبير
          ElevatedButton(
            onPressed: _isSubmitting ? null : _submitPost,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isSubmitting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('نشر في المجموعة'),
          ),
        ],
      ),
    );
  }

  String _buildPreviewText() {
    List<String> parts = [];
    
    if (_contentController.text.isNotEmpty) {
      parts.add(_contentController.text);
    }
    
    if (_selectedFeeling != null) {
      parts.add('يشعر بـ $_selectedFeeling');
    }
    
    if (_selectedActivity != null) {
      parts.add('$_selectedActivity');
    }
    
    return parts.join(' • ');
  }

  void _submitPost() async {
    if (_contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة محتوى المنشور')),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final socialProvider = Provider.of<SocialProvider>(context, listen: false);
      
      await socialProvider.createGroupPost(
        groupId: widget.group.id,
        content: _contentController.text.trim(),
        feeling: _selectedFeeling,
        activity: _selectedActivity,
        location: _selectedLocation,
        postType: _selectedPostType,
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(widget.group.requiresPostApproval
                    ? 'تم إرسال المنشور للمراجعة'
                    : 'تم نشر المنشور بنجاح'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في نشر المنشور: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  // وظائف رفع الوسائط
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _selectedVideo = null; // إلغاء الفيديو إذا كان محدد
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text('تم اختيار الصورة بنجاح! 📸'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        setState(() {
          _selectedVideo = File(video.path);
          _selectedImage = null; // إلغاء الصورة إذا كانت محددة
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text('تم اختيار الفيديو بنجاح! 🎥'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الفيديو: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showLocationPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الموقع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _locations.map((location) {
            return ListTile(
              leading: Icon(Icons.location_on, color: AppTheme.primaryColor),
              title: Text(location),
              onTap: () {
                setState(() {
                  _selectedLocation = location;
                });
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم اختيار الموقع: $location 📍'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _initializePoll() {
    setState(() {
      _pollOptions.clear();
      _pollOptions.addAll([
        TextEditingController(),
        TextEditingController(),
      ]);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تفعيل وضع الاستطلاع! أضف خيارات الاستطلاع 📊'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
