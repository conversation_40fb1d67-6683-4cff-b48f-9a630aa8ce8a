import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:share_plus/share_plus.dart';
import 'package:dio/dio.dart';
import 'dart:io';

class RealVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final bool isLocalFile;
  final bool autoPlay;
  final String? authorId;
  final VoidCallback? onDelete;

  const RealVideoPlayer({
    super.key,
    required this.videoUrl,
    this.isLocalFile = false,
    this.autoPlay = false,
    this.authorId,
    this.onDelete,
  });

  @override
  State<RealVideoPlayer> createState() => _RealVideoPlayerState();
}

class _RealVideoPlayerState extends State<RealVideoPlayer> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true; // إظهار الأزرار افتراضياً
  bool _isMuted = false;
  bool _hasEnded = false; // لتتبع انتهاء الفيديو
  double _playbackSpeed = 1.0;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
    _startControlsTimer();
  }

  Future<void> _initializeVideo() async {
    try {
      if (widget.isLocalFile) {
        _controller = VideoPlayerController.file(File(widget.videoUrl));
      } else {
        _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      }

      await _controller.initialize();
      
      setState(() {
        _isInitialized = true;
        _duration = _controller.value.duration;
        _isMuted = _controller.value.volume == 0;
      });

      _controller.addListener(_videoListener);

      if (widget.autoPlay) {
        await _controller.play();
        setState(() {
          _isPlaying = true;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الفيديو: $e');
    }
  }

  void _videoListener() {
    if (mounted && _controller.value.isInitialized) {
      setState(() {
        _position = _controller.value.position;
        _isPlaying = _controller.value.isPlaying;

        // تحقق من انتهاء الفيديو
        if (_position >= _duration && _duration.inMilliseconds > 0) {
          _hasEnded = true;
          _isPlaying = false;
        } else {
          _hasEnded = false;
        }
      });
    }
  }

  void _startControlsTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.removeListener(_videoListener);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Container(
        height: 300,
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
            strokeWidth: 2,
          ),
        ),
      );
    }

    // تحديد نسبة العرض إلى الارتفاع
    final aspectRatio = _controller.value.aspectRatio;
    final isVertical = aspectRatio < 1.0;
    
    // تحديد الارتفاع حسب نوع الفيديو
    double videoHeight;
    if (isVertical) {
      // فيديو عمودي (مثل فيديوهات الهاتف)
      videoHeight = MediaQuery.of(context).size.height * 0.7;
    } else {
      // فيديو أفقي
      videoHeight = 250;
    }

    return Container(
      height: videoHeight,
      width: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // مشغل الفيديو
          Center(
            child: AspectRatio(
              aspectRatio: aspectRatio,
              child: VideoPlayer(_controller),
            ),
          ),

          // طبقة شفافة للنقر
          Positioned.fill(
            child: GestureDetector(
              onTap: _toggleControls,
              onDoubleTap: _togglePlayPause,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // أزرار انتهاء الفيديو - حجم صغير وفي سطر واحد
          if (_hasEnded) ...[
            Container(
              color: Colors.black.withValues(alpha: 0.7),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // زر إعادة التشغيل (يمين)
                    GestureDetector(
                      onTap: _restartVideo,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.replay,
                          color: Colors.black,
                          size: 20,
                        ),
                      ),
                    ),

                    const SizedBox(width: 20),

                    // زر المشاركة (يسار)
                    GestureDetector(
                      onTap: _showVideoShareOptions,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.share,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // أدوات التحكم (مخفية افتراضياً)
          if (_showControls && !_hasEnded) ...[
            // خلفية متدرجة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.8),
                  ],
                ),
              ),
            ),



            // قائمة الخيارات (أعلى يمين)
            Positioned(
              top: 16,
              right: 16,
              child: _buildOptionsMenu(),
            ),

            // أدوات التحكم السفلية
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomControls(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOptionsMenu() {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.more_vert,
          color: Colors.white,
          size: 24,
        ),
      ),
      color: Colors.grey[900],
      onSelected: _handleMenuOption,
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'speed',
          child: Row(
            children: [
              const Icon(Icons.speed, color: Colors.white),
              const SizedBox(width: 12),
              const Text('سرعة التشغيل', style: TextStyle(color: Colors.white)),
              const Spacer(),
              Text('${_playbackSpeed}x', style: const TextStyle(color: Colors.grey)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'quality',
          child: Row(
            children: [
              Icon(Icons.high_quality, color: Colors.white),
              SizedBox(width: 12),
              Text('جودة الفيديو', style: TextStyle(color: Colors.white)),
              Spacer(),
              Text('تلقائي', style: TextStyle(color: Colors.grey)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'fullscreen',
          child: Row(
            children: [
              Icon(Icons.fullscreen, color: Colors.white),
              SizedBox(width: 12),
              Text('عرض كامل', style: TextStyle(color: Colors.white)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'download',
          child: Row(
            children: [
              Icon(Icons.download, color: Colors.white),
              SizedBox(width: 12),
              Text('تحميل', style: TextStyle(color: Colors.white)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'share',
          child: Row(
            children: [
              Icon(Icons.share, color: Colors.white),
              SizedBox(width: 12),
              Text('مشاركة', style: TextStyle(color: Colors.white)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'report',
          child: Row(
            children: [
              Icon(Icons.report, color: Colors.orange),
              SizedBox(width: 12),
              Text('إبلاغ', style: TextStyle(color: Colors.orange)),
            ],
          ),
        ),
        if (widget.authorId == 'current_user')
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, color: Colors.red),
                SizedBox(width: 12),
                Text('حذف', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // شريط التقدم
          Row(
            children: [
              Text(
                _formatDuration(_position),
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: Colors.red,
                    inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                    thumbColor: Colors.red,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                    overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
                  ),
                  child: Slider(
                    value: _position.inMilliseconds.toDouble().clamp(0.0, _duration.inMilliseconds.toDouble()),
                    max: _duration.inMilliseconds.toDouble(),
                    onChanged: (value) {
                      _controller.seekTo(Duration(milliseconds: value.toInt()));
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                _formatDuration(_duration),
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // أزرار التحكم
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // زر الصوت
              GestureDetector(
                onTap: _toggleMute,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _isMuted ? Icons.volume_off : Icons.volume_up,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
              
              // زر التشغيل/الإيقاف
              GestureDetector(
                onTap: _togglePlayPause,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
              
              // زر العرض الكامل
              GestureDetector(
                onTap: _openFullscreen,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.fullscreen,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _startControlsTimer();
    }
  }

  void _togglePlayPause() {
    setState(() {
      if (_isPlaying) {
        _controller.pause();
      } else {
        _controller.play();
        _hasEnded = false; // إعادة تعيين حالة الانتهاء
        _startControlsTimer();
      }
    });
  }

  void _restartVideo() {
    setState(() {
      _hasEnded = false;
    });
    _controller.seekTo(Duration.zero);
    _controller.play();
  }

  void _showVideoShareOptions() {
    // استخدام نفس نافذة المشاركة من PostCard
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            // العنوان
            const Text(
              'مشاركة الفيديو',
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // خيارات المشاركة
            _buildShareOption(
              icon: Icons.share,
              title: 'مشاركة خارجية',
              subtitle: 'مشاركة عبر التطبيقات الأخرى',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _shareVideoExternal();
              },
            ),

            _buildShareOption(
              icon: Icons.message,
              title: 'إرسال في رسالة',
              subtitle: 'إرسال لصديق في المحادثات',
              color: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _sendVideoInMessage();
              },
            ),

            _buildShareOption(
              icon: Icons.group,
              title: 'مشاركة في مجموعة',
              subtitle: 'نشر في إحدى المجموعات',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                _shareVideoInGroup();
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _shareVideoExternal() {
    // مشاركة خارجية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('مشاركة خارجية للفيديو'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _sendVideoInMessage() {
    // إرسال في رسالة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إرسال الفيديو في رسالة'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareVideoInGroup() {
    // مشاركة في مجموعة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('مشاركة الفيديو في مجموعة'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
      _controller.setVolume(_isMuted ? 0.0 : 1.0);
    });
  }

  void _openFullscreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullscreenVideoPlayer(
          videoUrl: widget.videoUrl,
          isLocalFile: widget.isLocalFile,
          authorId: widget.authorId,
        ),
      ),
    );
  }

  void _handleMenuOption(String option) {
    switch (option) {
      case 'speed':
        _showSpeedDialog();
        break;
      case 'quality':
        _showQualityDialog();
        break;
      case 'fullscreen':
        _openFullscreen();
        break;
      case 'download':
        _downloadVideo();
        break;
      case 'share':
        _shareVideo();
        break;
      case 'report':
        _showReportDialog();
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  void _showSpeedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'سرعة التشغيل',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0].map((speed) {
            return RadioListTile<double>(
              title: Text(
                '${speed}x',
                style: const TextStyle(color: Colors.white),
              ),
              value: speed,
              groupValue: _playbackSpeed,
              activeColor: Colors.red,
              onChanged: (value) {
                setState(() {
                  _playbackSpeed = value!;
                });

                // تطبيق السرعة على الفيديو فوراً
                _controller.setPlaybackSpeed(_playbackSpeed);

                Navigator.pop(context);

                // إظهار رسالة تأكيد
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تغيير السرعة إلى ${_playbackSpeed}x'),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showQualityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'جودة الفيديو',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('تلقائي', style: TextStyle(color: Colors.white)),
              subtitle: const Text('أفضل جودة متاحة', style: TextStyle(color: Colors.grey)),
              leading: const Icon(Icons.auto_awesome, color: Colors.blue),
              onTap: () {
                Navigator.pop(context);
                _showMessage('تم تعيين الجودة إلى تلقائي');
              },
            ),
            ListTile(
              title: const Text('1080p HD', style: TextStyle(color: Colors.white)),
              subtitle: const Text('جودة عالية', style: TextStyle(color: Colors.grey)),
              leading: const Icon(Icons.hd, color: Colors.green),
              onTap: () {
                Navigator.pop(context);
                _showMessage('تم تعيين الجودة إلى 1080p');
              },
            ),
            ListTile(
              title: const Text('720p', style: TextStyle(color: Colors.white)),
              subtitle: const Text('جودة متوسطة', style: TextStyle(color: Colors.grey)),
              leading: const Icon(Icons.high_quality, color: Colors.orange),
              onTap: () {
                Navigator.pop(context);
                _showMessage('تم تعيين الجودة إلى 720p');
              },
            ),
            ListTile(
              title: const Text('480p', style: TextStyle(color: Colors.white)),
              subtitle: const Text('جودة منخفضة', style: TextStyle(color: Colors.grey)),
              leading: const Icon(Icons.sd, color: Colors.red),
              onTap: () {
                Navigator.pop(context);
                _showMessage('تم تعيين الجودة إلى 480p');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'إبلاغ عن المحتوى',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'لماذا تريد الإبلاغ عن هذا الفيديو؟',
              style: TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.warning, color: Colors.orange),
              title: const Text('محتوى غير مناسب', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _submitReport('محتوى غير مناسب');
              },
            ),
            ListTile(
              leading: const Icon(Icons.copyright, color: Colors.red),
              title: const Text('انتهاك حقوق الطبع', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _submitReport('انتهاك حقوق الطبع');
              },
            ),
            ListTile(
              leading: const Icon(Icons.block, color: Colors.purple),
              title: const Text('محتوى مزعج أو سبام', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _submitReport('محتوى مزعج أو سبام');
              },
            ),
            ListTile(
              leading: const Icon(Icons.dangerous, color: Colors.red),
              title: const Text('محتوى ضار أو خطير', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _submitReport('محتوى ضار أو خطير');
              },
            ),
            ListTile(
              leading: const Icon(Icons.report_problem, color: Colors.orange),
              title: const Text('أخرى', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _submitReport('أخرى');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _submitReport(String reason) {
    // هنا يمكن إرسال الإبلاغ إلى الخادم
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'تأكيد الإبلاغ',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'هل أنت متأكد من الإبلاغ عن هذا الفيديو بسبب: $reason؟',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // إرسال الإبلاغ (محاكاة)
              _showMessage('تم إرسال الإبلاغ بنجاح. شكراً لك! 🛡️');

              // هنا يمكن إضافة كود إرسال الإبلاغ إلى الخادم
              debugPrint('تم الإبلاغ عن الفيديو: ${widget.videoUrl} - السبب: $reason');
            },
            child: const Text(
              'إبلاغ',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'حذف الفيديو',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.delete_forever,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            const Text(
              'هل أنت متأكد من حذف هذا الفيديو؟',
              style: TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'لا يمكن التراجع عن هذا الإجراء',
              style: TextStyle(color: Colors.grey, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteVideo();
            },
            child: const Text(
              'حذف',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteVideo() {
    // إيقاف الفيديو أولاً
    _controller.pause();

    // استدعاء دالة الحذف
    widget.onDelete?.call();

    // إظهار رسالة تأكيد
    _showMessage('تم حذف الفيديو بنجاح! 🗑️');

    // يمكن إضافة منطق إضافي هنا مثل:
    // - إرسال طلب حذف إلى الخادم
    // - حذف الملف المحلي إذا كان محلياً
    // - تحديث قاعدة البيانات

    debugPrint('تم حذف الفيديو: ${widget.videoUrl}');
  }

  Future<void> _downloadVideo() async {
    try {
      _showMessage('جاري تحميل الفيديو...');

      if (widget.isLocalFile) {
        // فيديو محلي - إظهار رسالة
        _showMessage('الفيديو محفوظ محلياً بالفعل! 📱');
      } else {
        // فيديو من الشبكة - تحميل إلى مجلد التحميلات
        final downloadsDir = Directory('/storage/emulated/0/Download');
        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }

        final fileName = 'arzawo_video_${DateTime.now().millisecondsSinceEpoch}.mp4';
        final filePath = '${downloadsDir.path}/$fileName';

        // تحميل الفيديو
        Dio dio = Dio();
        await dio.download(
          widget.videoUrl,
          filePath,
          onReceiveProgress: (received, total) {
            if (total != -1) {
              final progress = (received / total * 100).toStringAsFixed(0);
              debugPrint('تقدم التحميل: $progress%');
            }
          },
        );

        _showMessage('تم تحميل الفيديو إلى مجلد التحميلات! 📱');
        debugPrint('تم حفظ الفيديو في: $filePath');
      }
    } catch (e) {
      _showMessage('حدث خطأ أثناء تحميل الفيديو: ${e.toString()}');
      debugPrint('خطأ التحميل: $e');
    }
  }

  void _shareVideo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'مشاركة الفيديو',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.share, color: Colors.blue),
              title: const Text('مشاركة عامة', style: TextStyle(color: Colors.white)),
              subtitle: const Text('مشاركة رابط الفيديو', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                Share.share(
                  'شاهد هذا الفيديو الرائع!\n${widget.videoUrl}',
                  subject: 'فيديو من تطبيق Arzawo',
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.facebook, color: Colors.blue),
              title: const Text('Facebook', style: TextStyle(color: Colors.white)),
              subtitle: const Text('مشاركة على فيسبوك', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                Share.share(
                  'شاهد هذا الفيديو الرائع!\n${widget.videoUrl}',
                  subject: 'فيديو من تطبيق Arzawo',
                );
                _showMessage('تم فتح خيارات المشاركة');
              },
            ),
            ListTile(
              leading: const Icon(Icons.message, color: Colors.green),
              title: const Text('WhatsApp', style: TextStyle(color: Colors.white)),
              subtitle: const Text('مشاركة على واتساب', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                Share.share(
                  'شاهد هذا الفيديو الرائع!\n${widget.videoUrl}',
                  subject: 'فيديو من تطبيق Arzawo',
                );
                _showMessage('تم فتح خيارات المشاركة');
              },
            ),
            ListTile(
              leading: const Icon(Icons.telegram, color: Colors.blue),
              title: const Text('Telegram', style: TextStyle(color: Colors.white)),
              subtitle: const Text('مشاركة على تيليجرام', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                Share.share(
                  'شاهد هذا الفيديو الرائع!\n${widget.videoUrl}',
                  subject: 'فيديو من تطبيق Arzawo',
                );
                _showMessage('تم فتح خيارات المشاركة');
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy, color: Colors.orange),
              title: const Text('نسخ الرابط', style: TextStyle(color: Colors.white)),
              subtitle: const Text('نسخ رابط الفيديو', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                Clipboard.setData(ClipboardData(text: widget.videoUrl));
                _showMessage('تم نسخ رابط الفيديو! 📋');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: message.contains('نجاح') ? Colors.green : Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    
    if (hours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }
}

// شاشة العرض الكامل
class FullscreenVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final bool isLocalFile;
  final String? authorId;

  const FullscreenVideoPlayer({
    super.key,
    required this.videoUrl,
    this.isLocalFile = false,
    this.authorId,
  });

  @override
  State<FullscreenVideoPlayer> createState() => _FullscreenVideoPlayerState();
}

class _FullscreenVideoPlayerState extends State<FullscreenVideoPlayer> {
  @override
  void initState() {
    super.initState();
    // إخفاء شريط الحالة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    // السماح بجميع الاتجاهات
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    // إعادة تعيين إعدادات النظام
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // مشغل الفيديو بملء الشاشة
          Center(
            child: RealVideoPlayer(
              videoUrl: widget.videoUrl,
              isLocalFile: widget.isLocalFile,
              autoPlay: true,
              authorId: widget.authorId,
            ),
          ),

          // زر الإغلاق
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
