import 'post.dart';
import 'link_preview.dart';

enum VideoQuality {
  low,
  medium,
  high,
  hd,
}

enum VideoOrientation {
  portrait,
  landscape,
  square,
}

class VideoMetadata {
  final Duration duration;
  final VideoQuality quality;
  final VideoOrientation orientation;
  final int width;
  final int height;
  final double aspectRatio;
  final int fileSize; // in bytes
  final String format; // mp4, mov, etc.

  const VideoMetadata({
    required this.duration,
    required this.quality,
    required this.orientation,
    required this.width,
    required this.height,
    required this.aspectRatio,
    required this.fileSize,
    required this.format,
  });

  bool get isShortVideo => duration.inSeconds <= 60; // فيديو قصير (ريلز)
  bool get isLongVideo => duration.inSeconds > 60; // فيديو طويل
  
  String get durationText {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    if (minutes > 0) {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '0:${seconds.toString().padLeft(2, '0')}';
    }
  }

  String get fileSizeText {
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'duration': duration.inSeconds,
      'quality': quality.name,
      'orientation': orientation.name,
      'width': width,
      'height': height,
      'aspectRatio': aspectRatio,
      'fileSize': fileSize,
      'format': format,
    };
  }

  factory VideoMetadata.fromJson(Map<String, dynamic> json) {
    return VideoMetadata(
      duration: Duration(seconds: json['duration']),
      quality: VideoQuality.values.firstWhere(
        (e) => e.name == json['quality'],
        orElse: () => VideoQuality.medium,
      ),
      orientation: VideoOrientation.values.firstWhere(
        (e) => e.name == json['orientation'],
        orElse: () => VideoOrientation.portrait,
      ),
      width: json['width'],
      height: json['height'],
      aspectRatio: json['aspectRatio'],
      fileSize: json['fileSize'],
      format: json['format'],
    );
  }
}

class VideoPost extends Post {
  final VideoMetadata videoMetadata;
  final String videoUrl;
  final String thumbnailUrl;
  final int viewCount;
  final List<String> hashtags;
  final String? musicTitle;
  final String? musicArtist;
  final bool isReels; // true للفيديوهات القصيرة (ريلز)

  const VideoPost({
    required super.id,
    required super.authorId,
    required super.content,
    super.type = PostType.video,
    super.media = const [],
    super.background = PostBackground.none,
    super.taggedUsers = const [],
    super.feeling,
    super.activity,
    super.location,
    super.groupId,
    super.feelingDisplay,
    super.activityDisplay,
    super.locationDisplay,
    required super.timestamp,
    super.reactions = const [],
    super.comments = const [],
    super.shareCount = 0,
    super.shares = const [],
    super.reposts = const [],
    super.isPublic = true,
    super.linkPreview,
    required this.videoMetadata,
    required this.videoUrl,
    required this.thumbnailUrl,
    this.viewCount = 0,
    this.hashtags = const [],
    this.musicTitle,
    this.musicArtist,
    required this.isReels,
  });

  bool get isShortVideo => videoMetadata.isShortVideo;
  bool get isLongVideo => videoMetadata.isLongVideo;
  
  String get videoTypeText => isReels ? 'ريلز' : 'فيديو';
  
  @override
  VideoPost copyWith({
    String? id,
    String? authorId,
    String? content,
    PostType? type,
    List<PostMedia>? media,
    PostBackground? background,
    List<String>? taggedUsers,
    String? feeling,
    String? activity,
    String? location,
    String? groupId,
    String? feelingDisplay,
    String? activityDisplay,
    String? locationDisplay,
    DateTime? timestamp,
    List<PostReaction>? reactions,
    List<PostComment>? comments,
    int? shareCount,
    List<PostShare>? shares,
    List<PostRepost>? reposts,
    bool? isPublic,
    LinkPreview? linkPreview,
    VideoMetadata? videoMetadata,
    String? videoUrl,
    String? thumbnailUrl,
    int? viewCount,
    List<String>? hashtags,
    String? musicTitle,
    String? musicArtist,
    bool? isReels,
  }) {
    return VideoPost(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      content: content ?? this.content,
      type: type ?? this.type,
      media: media ?? this.media,
      background: background ?? this.background,
      taggedUsers: taggedUsers ?? this.taggedUsers,
      feeling: feeling ?? this.feeling,
      activity: activity ?? this.activity,
      location: location ?? this.location,
      groupId: groupId ?? this.groupId,
      feelingDisplay: feelingDisplay ?? this.feelingDisplay,
      activityDisplay: activityDisplay ?? this.activityDisplay,
      locationDisplay: locationDisplay ?? this.locationDisplay,
      timestamp: timestamp ?? this.timestamp,
      reactions: reactions ?? this.reactions,
      comments: comments ?? this.comments,
      shareCount: shareCount ?? this.shareCount,
      shares: shares ?? this.shares,
      reposts: reposts ?? this.reposts,
      isPublic: isPublic ?? this.isPublic,
      linkPreview: linkPreview ?? this.linkPreview,
      videoMetadata: videoMetadata ?? this.videoMetadata,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      viewCount: viewCount ?? this.viewCount,
      hashtags: hashtags ?? this.hashtags,
      musicTitle: musicTitle ?? this.musicTitle,
      musicArtist: musicArtist ?? this.musicArtist,
      isReels: isReels ?? this.isReels,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'videoMetadata': videoMetadata.toJson(),
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'viewCount': viewCount,
      'hashtags': hashtags,
      'musicTitle': musicTitle,
      'musicArtist': musicArtist,
      'isReels': isReels,
    });
    return json;
  }

  factory VideoPost.fromJson(Map<String, dynamic> json) {
    final post = Post.fromJson(json);
    return VideoPost(
      id: post.id,
      authorId: post.authorId,
      content: post.content,
      type: post.type,
      media: post.media,
      background: post.background,
      taggedUsers: post.taggedUsers,
      feeling: post.feeling,
      activity: post.activity,
      location: post.location,
      groupId: post.groupId,
      timestamp: post.timestamp,
      reactions: post.reactions,
      comments: post.comments,
      shareCount: post.shareCount,
      isPublic: post.isPublic,
      linkPreview: post.linkPreview,
      videoMetadata: VideoMetadata.fromJson(json['videoMetadata']),
      videoUrl: json['videoUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      viewCount: json['viewCount'] ?? 0,
      hashtags: List<String>.from(json['hashtags'] ?? []),
      musicTitle: json['musicTitle'],
      musicArtist: json['musicArtist'],
      isReels: json['isReels'] ?? false,
    );
  }
}

// فئة لتجميع الفيديوهات حسب النوع
class VideoFeed {
  final List<VideoPost> reelsVideos; // الفيديوهات القصيرة
  final List<VideoPost> longVideos; // الفيديوهات الطويلة
  final List<VideoPost> allVideos; // جميع الفيديوهات

  const VideoFeed({
    required this.reelsVideos,
    required this.longVideos,
    required this.allVideos,
  });

  factory VideoFeed.fromVideos(List<VideoPost> videos) {
    final reels = videos.where((v) => v.isReels).toList();
    final long = videos.where((v) => !v.isReels).toList();
    
    return VideoFeed(
      reelsVideos: reels,
      longVideos: long,
      allVideos: videos,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reelsVideos': reelsVideos.map((v) => v.toJson()).toList(),
      'longVideos': longVideos.map((v) => v.toJson()).toList(),
      'allVideos': allVideos.map((v) => v.toJson()).toList(),
    };
  }

  factory VideoFeed.fromJson(Map<String, dynamic> json) {
    return VideoFeed(
      reelsVideos: (json['reelsVideos'] as List)
          .map((v) => VideoPost.fromJson(v))
          .toList(),
      longVideos: (json['longVideos'] as List)
          .map((v) => VideoPost.fromJson(v))
          .toList(),
      allVideos: (json['allVideos'] as List)
          .map((v) => VideoPost.fromJson(v))
          .toList(),
    );
  }
}
