import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:arzawo/main.dart';
import 'package:arzawo/providers/auth_provider.dart';
import 'package:arzawo/providers/chat_provider.dart';
import 'package:arzawo/models/user.dart';
import 'package:arzawo/models/message.dart';

void main() {
  group('Arzawo App Tests', () {
    testWidgets('App should show login screen initially', (WidgetTester tester) async {
      // بناء التطبيق
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ChatProvider()),
          ],
          child: const A<PERSON>awoApp(),
        ),
      );

      // انتظار انتهاء التحميل
      await tester.pumpAndSettle();

      // التحقق من وجود شاشة تسجيل الدخول
      expect(find.text('Arzawo'), findsWidgets);
      expect(find.text('البريد الإلكتروني'), findsOneWidget);
      expect(find.text('كلمة المرور'), findsOneWidget);
      expect(find.text('تسجيل الدخول'), findsWidgets);
    });

    testWidgets('Login form validation works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ChatProvider()),
          ],
          child: const ArzawoApp(),
        ),
      );

      await tester.pumpAndSettle();

      // محاولة تسجيل الدخول بدون بيانات
      final loginButton = find.text('تسجيل الدخول').last;
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // التحقق من ظهور رسائل الخطأ
      expect(find.text('أدخل البريد الإلكتروني'), findsOneWidget);
      expect(find.text('أدخل كلمة المرور'), findsOneWidget);
    });

    testWidgets('Login with valid credentials', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ChatProvider()),
          ],
          child: const ArzawoApp(),
        ),
      );

      await tester.pumpAndSettle();

      // إدخال بيانات صحيحة
      await tester.enterText(
        find.byType(TextFormField).first,
        '<EMAIL>',
      );
      await tester.enterText(
        find.byType(TextFormField).last,
        'password123',
      );

      // الضغط على زر تسجيل الدخول
      final loginButton = find.text('تسجيل الدخول').last;
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // التحقق من الانتقال إلى شاشة المحادثات
      expect(find.text('المحادثات'), findsOneWidget);
    });

    testWidgets('Chat list displays correctly', (WidgetTester tester) async {
      // إنشاء AuthProvider مع مستخدم مسجل دخول
      final authProvider = AuthProvider();
      await authProvider.login('<EMAIL>', 'password123');

      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider.value(value: authProvider),
            ChangeNotifierProvider(create: (_) => ChatProvider()),
          ],
          child: const ArzawoApp(),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود شاشة المحادثات
      expect(find.text('المحادثات'), findsOneWidget);

      // انتظار تحميل البيانات
      await tester.pump(const Duration(seconds: 2));

      // التحقق من وجود قائمة المحادثات أو رسالة عدم وجود محادثات
      final hasChats = find.textContaining('أحمد');
      final noChatsMessage = find.text('لا توجد محادثات');

      expect(
        hasChats.evaluate().isNotEmpty || noChatsMessage.evaluate().isNotEmpty,
        isTrue,
      );
    });
  });

  group('Model Tests', () {
    test('User model serialization works', () {
      final user = User(
        id: '1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        isOnline: true,
        joinDate: DateTime.now(),
      );

      final json = user.toJson();
      final userFromJson = User.fromJson(json);

      expect(userFromJson.id, equals(user.id));
      expect(userFromJson.name, equals(user.name));
      expect(userFromJson.email, equals(user.email));
      expect(userFromJson.isOnline, equals(user.isOnline));
    });

    test('Message model serialization works', () {
      final message = Message(
        id: '1',
        senderId: 'sender1',
        receiverId: 'receiver1',
        content: 'مرحباً',
        timestamp: DateTime.now(),
      );

      final json = message.toJson();
      final messageFromJson = Message.fromJson(json);

      expect(messageFromJson.id, equals(message.id));
      expect(messageFromJson.senderId, equals(message.senderId));
      expect(messageFromJson.receiverId, equals(message.receiverId));
      expect(messageFromJson.content, equals(message.content));
    });
  });
}
