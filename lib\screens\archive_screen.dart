import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;

class ArchiveScreen extends StatefulWidget {
  const ArchiveScreen({super.key});

  @override
  State<ArchiveScreen> createState() => _ArchiveScreenState();
}

class _ArchiveScreenState extends State<ArchiveScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  
  final List<ArchivedPost> _archivedPosts = [
    ArchivedPost(
      id: '1',
      content: 'ذكريات جميلة من رحلة العام الماضي إلى تركيا 🇹🇷',
      type: ArchiveType.post,
      archivedAt: DateTime.now().subtract(const Duration(days: 30)),
      originalDate: DateTime.now().subtract(const Duration(days: 365)),
      imageUrl: 'https://picsum.photos/300/200?random=1',
    ),
    ArchivedPost(
      id: '2',
      content: 'تهنئة بمناسبة التخرج 🎓',
      type: ArchiveType.post,
      archivedAt: DateTime.now().subtract(const Duration(days: 45)),
      originalDate: DateTime.now().subtract(const Duration(days: 400)),
    ),
    ArchivedPost(
      id: '3',
      content: 'صورة من حفل الزفاف',
      type: ArchiveType.story,
      archivedAt: DateTime.now().subtract(const Duration(days: 60)),
      originalDate: DateTime.now().subtract(const Duration(days: 500)),
      imageUrl: 'https://picsum.photos/300/200?random=3',
    ),
  ];

  final List<ArchivedChat> _archivedChats = [
    ArchivedChat(
      id: '1',
      contactName: 'أحمد محمد',
      lastMessage: 'شكراً لك على المساعدة',
      archivedAt: DateTime.now().subtract(const Duration(days: 15)),
      messageCount: 45,
      avatarUrl: 'https://picsum.photos/50/50?random=10',
    ),
    ArchivedChat(
      id: '2',
      contactName: 'فاطمة أحمد',
      lastMessage: 'نتحدث لاحقاً',
      archivedAt: DateTime.now().subtract(const Duration(days: 20)),
      messageCount: 23,
      avatarUrl: 'https://picsum.photos/50/50?random=11',
    ),
    ArchivedChat(
      id: '3',
      contactName: 'مجموعة العمل',
      lastMessage: 'تم إنجاز المشروع بنجاح',
      archivedAt: DateTime.now().subtract(const Duration(days: 25)),
      messageCount: 156,
      isGroup: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'الأرشيف',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF1877F2),
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: const Color(0xFF1877F2),
          tabs: const [
            Tab(
              icon: Icon(Icons.post_add),
              text: 'المنشورات',
            ),
            Tab(
              icon: Icon(Icons.chat),
              text: 'المحادثات',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildArchivedPosts(),
          _buildArchivedChats(),
        ],
      ),
    );
  }

  Widget _buildArchivedPosts() {
    if (_archivedPosts.isEmpty) {
      return _buildEmptyState(
        icon: Icons.archive,
        title: 'لا توجد منشورات مؤرشفة',
        subtitle: 'المنشورات التي تقوم بأرشفتها ستظهر هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _archivedPosts.length,
      itemBuilder: (context, index) {
        final post = _archivedPosts[index];
        return _buildArchivedPostItem(post);
      },
    );
  }

  Widget _buildArchivedChats() {
    if (_archivedChats.isEmpty) {
      return _buildEmptyState(
        icon: Icons.chat_bubble_outline,
        title: 'لا توجد محادثات مؤرشفة',
        subtitle: 'المحادثات التي تقوم بأرشفتها ستظهر هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _archivedChats.length,
      itemBuilder: (context, index) {
        final chat = _archivedChats[index];
        return _buildArchivedChatItem(chat);
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildArchivedPostItem(ArchivedPost post) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المنشور
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: post.type == ArchiveType.post 
                        ? Colors.blue.withValues(alpha: 0.1)
                        : Colors.purple.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    post.type == ArchiveType.post ? Icons.post_add : Icons.auto_stories,
                    color: post.type == ArchiveType.post ? Colors.blue : Colors.purple,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.type == ArchiveType.post ? 'منشور مؤرشف' : 'قصة مؤرشفة',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'أُرشف ${timeago.format(post.archivedAt, locale: 'ar')}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handlePostAction(value, post),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'restore',
                      child: Row(
                        children: [
                          Icon(Icons.restore, size: 16),
                          SizedBox(width: 8),
                          Text('استعادة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete_forever, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف نهائياً', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // محتوى المنشور
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              post.content,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          
          // صورة المنشور (إن وجدت)
          if (post.imageUrl != null) ...[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              height: 200,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: DecorationImage(
                  image: NetworkImage(post.imageUrl!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // تاريخ النشر الأصلي
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'نُشر في ${timeago.format(post.originalDate, locale: 'ar')}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildArchivedChatItem(ArchivedChat chat) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          radius: 25,
          backgroundColor: chat.isGroup ? Colors.red : Colors.blue,
          backgroundImage: chat.avatarUrl != null ? NetworkImage(chat.avatarUrl!) : null,
          child: chat.avatarUrl == null
              ? Icon(
                  chat.isGroup ? Icons.group : Icons.person,
                  color: Colors.white,
                )
              : null,
        ),
        title: Text(
          chat.contactName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              chat.lastMessage,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              '${chat.messageCount} رسالة • أُرشف ${timeago.format(chat.archivedAt, locale: 'ar')}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleChatAction(value, chat),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'restore',
              child: Row(
                children: [
                  Icon(Icons.restore, size: 16),
                  SizedBox(width: 8),
                  Text('استعادة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_forever, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف نهائياً', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handlePostAction(String action, ArchivedPost post) {
    switch (action) {
      case 'restore':
        _restorePost(post);
        break;
      case 'delete':
        _deletePostPermanently(post);
        break;
    }
  }

  void _handleChatAction(String action, ArchivedChat chat) {
    switch (action) {
      case 'restore':
        _restoreChat(chat);
        break;
      case 'delete':
        _deleteChatPermanently(chat);
        break;
    }
  }

  void _restorePost(ArchivedPost post) {
    setState(() {
      _archivedPosts.remove(post);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم استعادة المنشور')),
    );
  }

  void _restoreChat(ArchivedChat chat) {
    setState(() {
      _archivedChats.remove(chat);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم استعادة المحادثة')),
    );
  }

  void _deletePostPermanently(ArchivedPost post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف نهائي'),
        content: const Text('هل أنت متأكد من الحذف النهائي؟ لن تتمكن من استعادة هذا المنشور.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _archivedPosts.remove(post);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم الحذف نهائياً')),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteChatPermanently(ArchivedChat chat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف نهائي'),
        content: const Text('هل أنت متأكد من الحذف النهائي؟ لن تتمكن من استعادة هذه المحادثة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _archivedChats.remove(chat);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم الحذف نهائياً')),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

enum ArchiveType { post, story }

class ArchivedPost {
  final String id;
  final String content;
  final ArchiveType type;
  final DateTime archivedAt;
  final DateTime originalDate;
  final String? imageUrl;

  ArchivedPost({
    required this.id,
    required this.content,
    required this.type,
    required this.archivedAt,
    required this.originalDate,
    this.imageUrl,
  });
}

class ArchivedChat {
  final String id;
  final String contactName;
  final String lastMessage;
  final DateTime archivedAt;
  final int messageCount;
  final String? avatarUrl;
  final bool isGroup;

  ArchivedChat({
    required this.id,
    required this.contactName,
    required this.lastMessage,
    required this.archivedAt,
    required this.messageCount,
    this.avatarUrl,
    this.isGroup = false,
  });
}
