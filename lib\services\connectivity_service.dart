import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/connectivity_status.dart';
import '../models/pending_action.dart' as pending;

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  final Dio _dio = Dio();

  final StreamController<NetworkState> _networkStateController =
      StreamController<NetworkState>.broadcast();

  NetworkState _currentState = NetworkState.initial;
  Timer? _reconnectTimer;
  Timer? _healthCheckTimer;
  bool _isInitializing = true;

  static const Duration _healthCheckInterval = Duration(seconds: 30);
  static const Duration _reconnectBaseDelay = Duration(seconds: 1);
  static const int _maxReconnectAttempts = 5;
  static const String _healthCheckUrl = 'https://www.google.com';

  static const String _pendingActionsKey = 'pending_actions';
  static const String _cachedDataKey = 'cached_data';

  Stream<NetworkState> get networkStateStream => _networkStateController.stream;
  NetworkState get currentState => _currentState;
  bool get isOnline => _currentState.isOnline;
  bool get isOffline => !_currentState.isOnline;

  Future<void> initialize() async {
    try {
      _configureDio();
      await _checkInitialConnectivity();

      _connectivity.onConnectivityChanged.listen((results) {
        // Handle the case where results might be a List
        final result = results is List<ConnectivityResult> 
            ? results.first 
            : results as ConnectivityResult;
        _onConnectivityChanged(result);
      });

      _startHealthCheckTimer();
      await _loadPendingActions();

      _networkStateController.add(_currentState);

      print('ConnectivityService initialized successfully');
    } catch (e) {
      print('Error initializing ConnectivityService: $e');
    }
  }

  void _configureDio() {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          options.headers['User-Agent'] = 'Arzawo/1.0.0';
          handler.next(options);
        },
        onResponse: (response, handler) {
          handler.next(response);
        },
        onError: (error, handler) async {
          if (_isNetworkError(error)) {
            await _handleNetworkError();
          }
          handler.next(error);
        },
      ),
    );
  }

  Future<void> _checkInitialConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final result = connectivityResults is List<ConnectivityResult>
          ? connectivityResults.first
          : connectivityResults as ConnectivityResult;
          
      final isReallyOnline = await _performHealthCheck();

      _updateNetworkState(
        status: isReallyOnline ? ConnectivityStatus.connected : ConnectivityStatus.disconnected,
        type: _mapConnectivityResult(result),
        isOnline: isReallyOnline,
        isInitialCheck: true,
      );

      _isInitializing = false;
    } catch (e) {
      print('Error checking initial connectivity: $e');
      _updateNetworkState(
        status: ConnectivityStatus.disconnected,
        type: ConnectionType.none,
        isOnline: false,
        isInitialCheck: true,
      );
      _isInitializing = false;
    }
  }

  void _onConnectivityChanged(ConnectivityResult result) async {
    final connectionType = _mapConnectivityResult(result);

    if (result == ConnectivityResult.none) {
      await _handleDisconnection();
    } else {
      await _handlePotentialConnection(connectionType);
    }
  }

  Future<void> _handleDisconnection() async {
    _updateNetworkState(
      status: ConnectivityStatus.disconnected,
      type: ConnectionType.none,
      isOnline: false,
      lastDisconnected: DateTime.now(),
    );

    _stopHealthCheckTimer();
    _startReconnectAttempts();
  }

  Future<void> _handlePotentialConnection(ConnectionType type) async {
    _updateNetworkState(
      status: ConnectivityStatus.reconnecting,
      type: type,
      isOnline: false,
    );

    final isReallyOnline = await _performHealthCheck();

    if (isReallyOnline) {
      await _handleSuccessfulConnection(type);
    } else {
      _updateNetworkState(
        status: ConnectivityStatus.disconnected,
        type: type,
        isOnline: false,
      );
      _startReconnectAttempts();
    }
  }

  Future<void> _handleSuccessfulConnection(ConnectionType type) async {
    _updateNetworkState(
      status: ConnectivityStatus.connected,
      type: type,
      isOnline: true,
      lastConnected: DateTime.now(),
      reconnectAttempts: 0,
    );

    _stopReconnectTimer();
    _startHealthCheckTimer();

    await _processPendingActions();
  }

  Future<bool> _performHealthCheck() async {
    try {
      final response = await _dio.get(
        _healthCheckUrl,
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  void _startReconnectAttempts() {
    _stopReconnectTimer();

    if (_currentState.reconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    final delay = Duration(
      seconds: (_reconnectBaseDelay.inSeconds *
          (1 << _currentState.reconnectAttempts)).clamp(1, 60),
    );

    _reconnectTimer = Timer(delay, () async {
      final connectivityResults = await _connectivity.checkConnectivity();
      final result = connectivityResults is List<ConnectivityResult>
          ? connectivityResults.first
          : connectivityResults as ConnectivityResult;

      if (result != ConnectivityResult.none) {
        final type = _mapConnectivityResult(result);

        _updateNetworkState(
          status: ConnectivityStatus.reconnecting,
          type: type,
          reconnectAttempts: _currentState.reconnectAttempts + 1,
        );

        final isOnline = await _performHealthCheck();

        if (isOnline) {
          await _handleSuccessfulConnection(type);
        } else {
          _startReconnectAttempts();
        }
      } else {
        _updateNetworkState(
          reconnectAttempts: _currentState.reconnectAttempts + 1,
        );
        _startReconnectAttempts();
      }
    });
  }

  void _startHealthCheckTimer() {
    _stopHealthCheckTimer();

    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (timer) async {
      if (_currentState.isOnline) {
        final isStillOnline = await _performHealthCheck();

        if (!isStillOnline) {
          await _handleNetworkError();
        }
      }
    });
  }

  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  void _stopHealthCheckTimer() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }

  Future<void> _handleNetworkError() async {
    if (_currentState.isOnline) {
      _updateNetworkState(
        status: ConnectivityStatus.disconnected,
        isOnline: false,
        lastDisconnected: DateTime.now(),
      );

      _stopHealthCheckTimer();
      _startReconnectAttempts();
    }
  }

  void _updateNetworkState({
    ConnectivityStatus? status,
    ConnectionType? type,
    bool? isOnline,
    DateTime? lastConnected,
    DateTime? lastDisconnected,
    int? reconnectAttempts,
    String? errorMessage,
    bool isInitialCheck = false,
  }) {
    _currentState = _currentState.copyWith(
      status: status,
      type: type,
      isOnline: isOnline,
      lastConnected: lastConnected,
      lastDisconnected: lastDisconnected,
      reconnectAttempts: reconnectAttempts,
      errorMessage: errorMessage,
    );

    if (!_isInitializing || !isInitialCheck) {
      _networkStateController.add(_currentState);
    }
  }

  ConnectionType _mapConnectivityResult(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return ConnectionType.wifi;
      case ConnectivityResult.mobile:
        return ConnectionType.mobile;
      case ConnectivityResult.ethernet:
        return ConnectionType.ethernet;
      case ConnectivityResult.bluetooth:
        return ConnectionType.bluetooth;
      case ConnectivityResult.vpn:
        return ConnectionType.vpn;
      case ConnectivityResult.other:
        return ConnectionType.other;
      case ConnectivityResult.none:
        return ConnectionType.none;
    }
  }

  bool _isNetworkError(DioException error) {
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.connectionError ||
        (error.error is SocketException);
  }

  Future<void> _loadPendingActions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final actionsJson = prefs.getStringList(_pendingActionsKey) ?? [];

      if (_currentState.isOnline && actionsJson.isNotEmpty) {
        await _processPendingActions();
      }
    } catch (e) {
      print('Error loading pending actions: $e');
    }
  }

  Future<void> _processPendingActions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final actionsJson = prefs.getStringList(_pendingActionsKey) ?? [];

      if (actionsJson.isEmpty) return;

      final actions = actionsJson
          .map((json) => pending.PendingAction.fromJson(jsonDecode(json)))
          .toList();

      final successfulActions = <String>[];

      for (final action in actions) {
        if (action.canRetry) {
          final success = await _executeAction(action);
          if (success) {
            successfulActions.add(action.id);
          }
        } else {
          successfulActions.add(action.id);
        }
      }

      final remainingActions = actions
          .where((action) => !successfulActions.contains(action.id))
          .map((action) => jsonEncode(action.toJson()))
          .toList();

      await prefs.setStringList(_pendingActionsKey, remainingActions);
    } catch (e) {
      print('Error processing pending actions: $e');
    }
  }

  Future<bool> _executeAction(pending.PendingAction action) async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      return true;
    } catch (e) {
      print('Error executing action ${action.id}: $e');
      return false;
    }
  }

  Future<void> addPendingAction(pending.PendingAction action) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final actionsJson = prefs.getStringList(_pendingActionsKey) ?? [];

      actionsJson.add(jsonEncode(action.toJson()));
      await prefs.setStringList(_pendingActionsKey, actionsJson);

      if (_currentState.isOnline) {
        await _executeAction(action);
      }
    } catch (e) {
      print('Error adding pending action: $e');
    }
  }

  void dispose() {
    _stopReconnectTimer();
    _stopHealthCheckTimer();
    _networkStateController.close();
    _dio.close();
  }
}
