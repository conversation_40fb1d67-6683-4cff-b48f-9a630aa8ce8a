class PendingAction {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final bool canRetry;

  PendingAction({
    required this.id,
    required this.type,
    required this.data,
    required this.createdAt,
    this.canRetry = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'data': data,
      'createdAt': createdAt.toIso8601String(),
      'canRetry': canRetry,
    };
  }

  factory PendingAction.fromJson(Map<String, dynamic> json) {
    return PendingAction(
      id: json['id'],
      type: json['type'],
      data: json['data'],
      createdAt: DateTime.parse(json['createdAt']),
      canRetry: json['canRetry'] ?? true,
    );
  }
}
