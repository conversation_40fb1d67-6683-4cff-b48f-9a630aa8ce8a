import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/messenger_provider.dart';
import '../theme/app_theme.dart';

class MessengerSettingsScreen extends StatelessWidget {
  const MessengerSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: const Text('إعدادات الماسنجر'),
      ),
      body: Consumer<MessengerProvider>(
        builder: (context, messengerProvider, child) {
          final settings = messengerProvider.settings;
          
          return ListView(
            children: [
              // إعدادات الإشعارات
              _buildSectionHeader('الإشعارات'),
              _buildSwitchTile(
                context,
                title: 'إشعارات الرسائل',
                subtitle: 'تلقي إشعارات للرسائل الجديدة',
                value: settings.notificationsEnabled,
                onChanged: (value) => messengerProvider.updateSetting('notifications_enabled', value),
                icon: Icons.notifications,
              ),
              _buildSwitchTile(
                context,
                title: 'معاينة الرسائل',
                subtitle: 'إظهار محتوى الرسالة في الإشعار',
                value: settings.messagePreview,
                onChanged: (value) => messengerProvider.updateSetting('message_preview', value),
                icon: Icons.preview,
                enabled: settings.notificationsEnabled,
              ),
              _buildSwitchTile(
                context,
                title: 'الصوت',
                subtitle: 'تشغيل صوت عند وصول رسالة',
                value: settings.soundEnabled,
                onChanged: (value) => messengerProvider.updateSetting('sound_enabled', value),
                icon: Icons.volume_up,
                enabled: settings.notificationsEnabled,
              ),
              _buildSwitchTile(
                context,
                title: 'الاهتزاز',
                subtitle: 'اهتزاز الجهاز عند وصول رسالة',
                value: settings.vibrationEnabled,
                onChanged: (value) => messengerProvider.updateSetting('vibration_enabled', value),
                icon: Icons.vibration,
                enabled: settings.notificationsEnabled,
              ),
              
              const Divider(),
              
              // إعدادات الخصوصية
              _buildSectionHeader('الخصوصية'),
              _buildSwitchTile(
                context,
                title: 'إيصالات القراءة',
                subtitle: 'إرسال إيصال عند قراءة الرسائل',
                value: settings.readReceipts,
                onChanged: (value) => messengerProvider.updateSetting('read_receipts', value),
                icon: Icons.done_all,
              ),
              _buildSwitchTile(
                context,
                title: 'آخر ظهور',
                subtitle: 'إظهار وقت آخر ظهور للآخرين',
                value: settings.lastSeen,
                onChanged: (value) => messengerProvider.updateSetting('last_seen', value),
                icon: Icons.access_time,
              ),
              _buildSwitchTile(
                context,
                title: 'الحالة المتصلة',
                subtitle: 'إظهار حالة الاتصال للآخرين',
                value: settings.onlineStatus,
                onChanged: (value) => messengerProvider.updateSetting('online_status', value),
                icon: Icons.circle,
              ),
              
              const Divider(),
              
              // إعدادات التحميل التلقائي
              _buildSectionHeader('التحميل التلقائي'),
              _buildSwitchTile(
                context,
                title: 'الصور',
                subtitle: 'تحميل الصور تلقائياً',
                value: settings.autoDownloadImages,
                onChanged: (value) => messengerProvider.updateSetting('auto_download_images', value),
                icon: Icons.image,
              ),
              _buildSwitchTile(
                context,
                title: 'الفيديوهات',
                subtitle: 'تحميل الفيديوهات تلقائياً',
                value: settings.autoDownloadVideos,
                onChanged: (value) => messengerProvider.updateSetting('auto_download_videos', value),
                icon: Icons.videocam,
              ),
              _buildSwitchTile(
                context,
                title: 'الرسائل الصوتية',
                subtitle: 'تحميل الرسائل الصوتية تلقائياً',
                value: settings.autoDownloadAudio,
                onChanged: (value) => messengerProvider.updateSetting('auto_download_audio', value),
                icon: Icons.mic,
              ),
              _buildSwitchTile(
                context,
                title: 'المستندات',
                subtitle: 'تحميل المستندات تلقائياً',
                value: settings.autoDownloadDocuments,
                onChanged: (value) => messengerProvider.updateSetting('auto_download_documents', value),
                icon: Icons.description,
              ),
              
              const Divider(),
              
              // إعدادات المظهر
              _buildSectionHeader('المظهر'),
              _buildDropdownTile(
                context,
                title: 'حجم الخط',
                subtitle: 'حجم النص في المحادثات',
                value: settings.fontSize,
                items: ['صغير', 'متوسط', 'كبير', 'كبير جداً'],
                onChanged: (value) => messengerProvider.updateSetting('font_size', value),
                icon: Icons.text_fields,
              ),
              _buildDropdownTile(
                context,
                title: 'خلفية المحادثة',
                subtitle: 'خلفية شاشة المحادثة',
                value: settings.chatWallpaper,
                items: ['افتراضي', 'أزرق فاتح', 'أخضر فاتح', 'رمادي', 'مخصص'],
                onChanged: (value) => messengerProvider.updateSetting('chat_wallpaper', value),
                icon: Icons.wallpaper,
              ),
              _buildSwitchTile(
                context,
                title: 'الوضع الليلي',
                subtitle: 'تفعيل المظهر الداكن',
                value: settings.darkMode,
                onChanged: (value) => messengerProvider.updateSetting('dark_mode', value),
                icon: Icons.dark_mode,
              ),
              
              const Divider(),
              
              // إعدادات عامة
              _buildSectionHeader('عام'),
              _buildDropdownTile(
                context,
                title: 'اللغة',
                subtitle: 'لغة التطبيق',
                value: settings.language,
                items: ['العربية', 'English', 'Français'],
                onChanged: (value) => messengerProvider.updateSetting('language', value),
                icon: Icons.language,
              ),
              _buildDropdownTile(
                context,
                title: 'النسخ الاحتياطي',
                subtitle: 'تكرار النسخ الاحتياطي',
                value: settings.backupFrequency,
                items: ['لا يوجد', 'يومياً', 'أسبوعياً', 'شهرياً'],
                onChanged: (value) => messengerProvider.updateSetting('backup_frequency', value),
                icon: Icons.backup,
              ),
              
              const Divider(),
              
              // إعدادات متقدمة
              _buildSectionHeader('متقدم'),
              _buildActionTile(
                context,
                title: 'مسح ذاكرة التخزين المؤقت',
                subtitle: 'حذف الملفات المؤقتة لتوفير مساحة',
                icon: Icons.cleaning_services,
                onTap: () => _clearCache(context),
              ),
              _buildActionTile(
                context,
                title: 'تصدير المحادثات',
                subtitle: 'حفظ المحادثات كملف نصي',
                icon: Icons.file_download,
                onTap: () => _exportChats(context),
              ),
              _buildActionTile(
                context,
                title: 'إعادة تعيين الإعدادات',
                subtitle: 'استعادة الإعدادات الافتراضية',
                icon: Icons.restore,
                onTap: () => _resetSettings(context, messengerProvider),
                textColor: Colors.red,
              ),
              
              const SizedBox(height: 20),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildSwitchTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
    bool enabled = true,
  }) {
    return Container(
      color: Colors.white,
      child: ListTile(
        leading: Icon(
          icon,
          color: enabled ? AppTheme.primaryColor : Colors.grey,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: enabled ? Colors.black : Colors.grey,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: enabled ? Colors.grey[600] : Colors.grey[400],
          ),
        ),
        trailing: Switch(
          value: enabled ? value : false,
          onChanged: enabled ? onChanged : null,
          activeColor: AppTheme.primaryColor,
        ),
        enabled: enabled,
      ),
    );
  }

  Widget _buildDropdownTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData icon,
  }) {
    return Container(
      color: Colors.white,
      child: ListTile(
        leading: Icon(icon, color: AppTheme.primaryColor),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: DropdownButton<String>(
          value: value,
          items: items.map((item) => DropdownMenuItem(
            value: item,
            child: Text(item),
          )).toList(),
          onChanged: onChanged,
          underline: const SizedBox(),
        ),
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return Container(
      color: Colors.white,
      child: ListTile(
        leading: Icon(
          icon,
          color: textColor ?? AppTheme.primaryColor,
        ),
        title: Text(
          title,
          style: TextStyle(color: textColor),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _clearCache(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح ذاكرة التخزين المؤقت'),
        content: const Text('هل تريد حذف جميع الملفات المؤقتة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح ذاكرة التخزين المؤقت')),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _exportChats(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير المحادثات...')),
    );
  }

  void _resetSettings(BuildContext context, MessengerProvider messengerProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل تريد استعادة جميع الإعدادات الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // إعادة تعيين الإعدادات للقيم الافتراضية
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إعادة تعيين الإعدادات')),
              );
            },
            child: const Text('إعادة تعيين', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
