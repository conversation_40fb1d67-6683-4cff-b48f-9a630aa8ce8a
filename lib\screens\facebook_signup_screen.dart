import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import '../services/auth_service.dart';
import '../widgets/facebook_form_field.dart';
import '../widgets/country_city_picker.dart';
import '../widgets/date_picker_field.dart';
import 'main_navigation_screen.dart';

class FacebookSignupScreen extends StatefulWidget {
  const FacebookSignupScreen({super.key});

  @override
  State<FacebookSignupScreen> createState() => _FacebookSignupScreenState();
}

class _FacebookSignupScreenState extends State<FacebookSignupScreen> {
  final PageController _pageController = PageController();
  final AuthService _authService = AuthService();
  final RegistrationData _registrationData = RegistrationData();
  
  int _currentStep = 0;
  bool _isLoading = false;
  String? _errorMessage;

  // Controllers للخطوة الأولى
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();

  // Controllers للخطوة الثالثة
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _pageController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: _currentStep > 0
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: _goToPreviousStep,
              )
            : null,
        title: Text(
          'إنشاء حساب جديد',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // مؤشر التقدم
          _buildProgressIndicator(),
          
          // محتوى الخطوات
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildStep1(),
                _buildStep2(),
                _buildStep3(),
              ],
            ),
          ),
          
          // رسالة الخطأ
          if (_errorMessage != null) _buildErrorMessage(),
          
          // زر التالي/إنشاء الحساب
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: List.generate(3, (index) {
          final isActive = index <= _currentStep;
          final isCompleted = index < _currentStep;
          
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: index < 2 ? 8 : 0),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: isActive 
                            ? const Color(0xFF1877F2)
                            : Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  if (index < 2)
                    Container(
                      width: 20,
                      height: 20,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        color: isCompleted 
                            ? const Color(0xFF1877F2)
                            : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: isCompleted
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 12,
                            )
                          : Text(
                              '${index + 1}',
                              style: TextStyle(
                                color: isActive ? Colors.white : Colors.grey[600],
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                    ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildStep1() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          const Text(
            'ما اسمك؟',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أدخل الاسم الذي تستخدمه في الحياة الحقيقية',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 30),
          
          FacebookFormField(
            controller: _firstNameController,
            label: 'الاسم الأول',
            onChanged: (value) {
              setState(() {
                _registrationData.firstName = value;
                _errorMessage = null;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          FacebookFormField(
            controller: _lastNameController,
            label: 'الاسم الثاني',
            onChanged: (value) {
              setState(() {
                _registrationData.lastName = value;
                _errorMessage = null;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStep2() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          const Text(
            'معلومات إضافية',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ساعدنا في التعرف عليك أكثر',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 30),
          
          // اختيار الجنس
          const Text(
            'الجنس',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildGenderOption(Gender.male, 'ذكر'),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildGenderOption(Gender.female, 'أنثى'),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // تاريخ الميلاد
          DatePickerField(
            label: 'تاريخ الميلاد',
            selectedDate: _registrationData.birthDate,
            onDateSelected: (date) {
              setState(() {
                _registrationData.birthDate = date;
                _errorMessage = null;
              });
            },
          ),
          
          const SizedBox(height: 24),
          
          // البلد والمدينة
          CountryCityPicker(
            selectedCountry: _registrationData.country,
            selectedCity: _registrationData.city,
            onCountryChanged: (country) {
              setState(() {
                _registrationData.country = country;
                _registrationData.city = ''; // إعادة تعيين المدينة
                _errorMessage = null;
              });
            },
            onCityChanged: (city) {
              setState(() {
                _registrationData.city = city;
                _errorMessage = null;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStep3() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          const Text(
            'معلومات تسجيل الدخول',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستحتاج هذه المعلومات لتسجيل الدخول',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 30),
          
          FacebookFormField(
            controller: _emailController,
            label: 'البريد الإلكتروني',
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) {
              setState(() {
                _registrationData.email = value;
                _errorMessage = null;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          FacebookFormField(
            controller: _passwordController,
            label: 'كلمة المرور',
            isPassword: true,
            onChanged: (value) {
              setState(() {
                _registrationData.password = value;
                _errorMessage = null;
              });
            },
          ),
          
          const SizedBox(height: 12),
          
          Text(
            'يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderOption(Gender gender, String label) {
    final isSelected = _registrationData.gender == gender;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _registrationData.gender = gender;
          _errorMessage = null;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? const Color(0xFF1877F2) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? const Color(0xFF1877F2).withValues(alpha: 0.1) : Colors.white,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isSelected ? const Color(0xFF1877F2) : Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    final isLastStep = _currentStep == 2;
    final canProceed = _canProceedToNextStep();
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: FacebookButton(
        text: isLastStep ? 'إنشاء الحساب' : 'التالي',
        onPressed: canProceed ? (isLastStep ? _createAccount : _goToNextStep) : null,
        isLoading: _isLoading,
      ),
    );
  }

  bool _canProceedToNextStep() {
    switch (_currentStep) {
      case 0:
        return _registrationData.isStep1Valid;
      case 1:
        return _registrationData.isStep2Valid;
      case 2:
        return _registrationData.isStep3Valid;
      default:
        return false;
    }
  }

  void _goToNextStep() {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
        _errorMessage = null;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
        _errorMessage = null;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _createAccount() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _authService.register(_registrationData);
      
      if (response.success) {
        // نجح التسجيل - الانتقال للصفحة الرئيسية
        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
            (route) => false,
          );
        }
      } else {
        setState(() {
          _errorMessage = response.errorMessage;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ غير متوقع';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
