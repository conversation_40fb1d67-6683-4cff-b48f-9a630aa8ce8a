import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../models/user.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';

class VideoCallScreen extends StatefulWidget {
  final User otherUser;
  final bool isIncoming;

  const VideoCallScreen({
    super.key,
    required this.otherUser,
    this.isIncoming = false,
  });

  @override
  State<VideoCallScreen> createState() => _VideoCallScreenState();
}

class _VideoCallScreenState extends State<VideoCallScreen>
    with TickerProviderStateMixin {
  bool _isMuted = false;
  bool _isCameraOn = true;
  bool _isFrontCamera = true;
  bool _isCallConnected = false;
  bool _showControls = true;
  Duration _callDuration = Duration.zero;
  Timer? _callTimer;
  Timer? _hideControlsTimer;
  
  late AnimationController _pulseController;
  late AnimationController _connectingController;
  late AnimationController _controlsController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _connectingAnimation;
  late Animation<double> _controlsAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _connectingController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _controlsController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _connectingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _connectingController,
      curve: Curves.easeInOut,
    ));
    
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsController,
      curve: Curves.easeInOut,
    ));

    if (!widget.isIncoming) {
      _startConnecting();
    } else {
      _pulseController.repeat(reverse: true);
    }
    
    _controlsController.forward();
    _startHideControlsTimer();
  }

  @override
  void dispose() {
    _callTimer?.cancel();
    _hideControlsTimer?.cancel();
    _pulseController.dispose();
    _connectingController.dispose();
    _controlsController.dispose();
    super.dispose();
  }

  void _startConnecting() {
    _connectingController.repeat();
    
    // محاكاة الاتصال
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        _connectCall();
      }
    });
  }

  void _connectCall() {
    setState(() {
      _isCallConnected = true;
    });
    
    _connectingController.stop();
    _pulseController.stop();
    
    // بدء عداد المكالمة
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _callDuration = Duration(seconds: _callDuration.inSeconds + 1);
        });
      }
    });
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _isCallConnected) {
        setState(() {
          _showControls = false;
        });
        _controlsController.reverse();
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _controlsController.forward();
      _startHideControlsTimer();
    } else {
      _controlsController.reverse();
      _hideControlsTimer?.cancel();
    }
  }

  void _endCall() {
    _callTimer?.cancel();
    _hideControlsTimer?.cancel();
    Navigator.pop(context);
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
    HapticFeedback.lightImpact();
  }

  void _toggleCamera() {
    setState(() {
      _isCameraOn = !_isCameraOn;
    });
    HapticFeedback.lightImpact();
  }

  void _switchCamera() {
    setState(() {
      _isFrontCamera = !_isFrontCamera;
    });
    HapticFeedback.lightImpact();
  }

  void _acceptCall() {
    _connectCall();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _isCallConnected ? _toggleControls : null,
        child: Stack(
          children: [
            // فيديو المستخدم الآخر (ملء الشاشة)
            _buildRemoteVideo(),
            
            // فيديو المستخدم الحالي (صغير في الزاوية)
            if (_isCallConnected && _isCameraOn)
              _buildLocalVideo(),
            
            // أزرار التحكم
            AnimatedBuilder(
              animation: _controlsAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _controlsAnimation.value,
                  child: _buildControls(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRemoteVideo() {
    if (!_isCallConnected) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: AppTheme.primaryColor,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // صورة المستخدم مع تأثير النبض
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.3),
                          blurRadius: 20,
                          spreadRadius: 10,
                        ),
                      ],
                    ),
                    child: SmartAvatarWithText(
                      user: widget.otherUser,
                      radius: 100,
                    ),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // اسم المستخدم
            Text(
              widget.otherUser.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // حالة المكالمة
            AnimatedBuilder(
              animation: _connectingAnimation,
              builder: (context, child) {
                if (widget.isIncoming) {
                  return const Text(
                    'مكالمة فيديو واردة...',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 18,
                    ),
                  );
                } else {
                  return Text(
                    'جاري الاتصال${'.' * ((_connectingAnimation.value * 3).floor() + 1)}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 18,
                    ),
                  );
                }
              },
            ),
          ],
        ),
      );
    }

    // محاكاة فيديو المستخدم الآخر
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.blue.shade900,
            Colors.purple.shade900,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SmartAvatarWithText(
              user: widget.otherUser,
              radius: 80,
            ),
            const SizedBox(height: 16),
            Text(
              widget.otherUser.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'الكاميرا مغلقة',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalVideo() {
    return Positioned(
      top: 60,
      right: 16,
      child: Container(
        width: 120,
        height: 160,
        decoration: BoxDecoration(
          color: Colors.grey.shade800,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.green.shade700,
                  Colors.teal.shade700,
                ],
              ),
            ),
            child: const Center(
              child: Text(
                'أنت',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControls() {
    return Column(
      children: [
        // شريط علوي
        Container(
          padding: const EdgeInsets.only(top: 40, left: 16, right: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withValues(alpha: 0.7),
                Colors.transparent,
              ],
            ),
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
              ),
              const Spacer(),
              if (_isCallConnected)
                Text(
                  _formatDuration(_callDuration),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              const Spacer(),
              IconButton(
                onPressed: _switchCamera,
                icon: const Icon(Icons.flip_camera_ios, color: Colors.white),
              ),
            ],
          ),
        ),
        
        const Spacer(),
        
        // أزرار التحكم السفلية
        Container(
          padding: const EdgeInsets.only(bottom: 40, left: 32, right: 32),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Colors.black.withValues(alpha: 0.7),
                Colors.transparent,
              ],
            ),
          ),
          child: widget.isIncoming && !_isCallConnected
              ? _buildIncomingCallButtons()
              : _buildCallControlButtons(),
        ),
      ],
    );
  }

  Widget _buildIncomingCallButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // رفض المكالمة
        GestureDetector(
          onTap: _endCall,
          child: Container(
            width: 70,
            height: 70,
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
        
        // قبول المكالمة
        GestureDetector(
          onTap: _acceptCall,
          child: Container(
            width: 70,
            height: 70,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.videocam,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCallControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // كتم الصوت
        _buildControlButton(
          icon: _isMuted ? Icons.mic_off : Icons.mic,
          isActive: _isMuted,
          onTap: _toggleMute,
        ),
        
        // تشغيل/إيقاف الكاميرا
        _buildControlButton(
          icon: _isCameraOn ? Icons.videocam : Icons.videocam_off,
          isActive: !_isCameraOn,
          onTap: _toggleCamera,
        ),
        
        // إنهاء المكالمة
        GestureDetector(
          onTap: _endCall,
          child: Container(
            width: 70,
            height: 70,
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: isActive ? Colors.red : Colors.white.withValues(alpha: 0.3),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
