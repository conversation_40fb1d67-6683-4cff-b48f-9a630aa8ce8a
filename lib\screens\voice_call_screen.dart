import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../models/user.dart';
import '../widgets/smart_avatar.dart';
import '../theme/app_theme.dart';

class VoiceCallScreen extends StatefulWidget {
  final User user;
  final bool isIncoming;

  const VoiceCallScreen({
    super.key,
    required this.user,
    this.isIncoming = false,
  });

  @override
  State<VoiceCallScreen> createState() => _VoiceCallScreenState();
}

class _VoiceCallScreenState extends State<VoiceCallScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _avatarController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _avatarAnimation;
  
  Timer? _callTimer;
  Duration _callDuration = Duration.zero;
  bool _isConnected = false;
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isCallEnded = false;

  @override
  void initState() {
    super.initState();
    
    // إخفاء شريط الحالة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    
    // إعداد الرسوم المتحركة
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _avatarController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _avatarAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _avatarController,
      curve: Curves.elasticOut,
    ));
    
    // بدء الرسوم المتحركة
    _pulseController.repeat(reverse: true);
    _avatarController.forward();
    
    // محاكاة الاتصال
    if (widget.isIncoming) {
      _simulateIncomingCall();
    } else {
      _simulateOutgoingCall();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _avatarController.dispose();
    _callTimer?.cancel();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  void _simulateIncomingCall() {
    // محاكاة مكالمة واردة
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isConnected = false;
        });
      }
    });
  }

  void _simulateOutgoingCall() {
    // محاكاة مكالمة صادرة
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _connectCall();
      }
    });
  }

  void _connectCall() {
    setState(() {
      _isConnected = true;
    });
    
    // بدء عداد المكالمة
    _startCallTimer();
    
    // إيقاف رسوم النبض
    _pulseController.stop();
  }

  void _startCallTimer() {
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _callDuration = Duration(seconds: _callDuration.inSeconds + 1);
        });
      }
    });
  }

  void _endCall() {
    setState(() {
      _isCallEnded = true;
    });
    
    _callTimer?.cancel();
    _pulseController.stop();
    
    // العودة للشاشة السابقة بعد ثانية
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }

  void _acceptCall() {
    _connectCall();
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
    
    HapticFeedback.lightImpact();
  }

  void _toggleSpeaker() {
    setState(() {
      _isSpeakerOn = !_isSpeakerOn;
    });
    
    HapticFeedback.lightImpact();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.8),
              Colors.black,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط علوي
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                    Text(
                      _getCallStatus(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 48),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // صورة المستخدم
              AnimatedBuilder(
                animation: _avatarAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _avatarAnimation.value,
                    child: AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _isConnected ? 1.0 : _pulseAnimation.value,
                          child: Container(
                            width: 200,
                            height: 200,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: SmartAvatarWithText(
                              user: widget.user,
                              radius: 100,
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 30),
              
              // اسم المستخدم
              Text(
                widget.user.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 10),
              
              // حالة المكالمة أو مدتها
              Text(
                _isConnected 
                    ? _formatDuration(_callDuration)
                    : _getCallStatusText(),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 18,
                ),
              ),
              
              const Spacer(),
              
              // أزرار التحكم
              if (!_isCallEnded) ...[
                if (widget.isIncoming && !_isConnected) ...[
                  // أزرار المكالمة الواردة
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // رفض المكالمة
                      GestureDetector(
                        onTap: _endCall,
                        child: Container(
                          width: 70,
                          height: 70,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.call_end,
                            color: Colors.white,
                            size: 35,
                          ),
                        ),
                      ),
                      
                      // قبول المكالمة
                      GestureDetector(
                        onTap: _acceptCall,
                        child: Container(
                          width: 70,
                          height: 70,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.call,
                            color: Colors.white,
                            size: 35,
                          ),
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // أزرار التحكم أثناء المكالمة
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // كتم الصوت
                      GestureDetector(
                        onTap: _toggleMute,
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: _isMuted 
                                ? Colors.red 
                                : Colors.white.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _isMuted ? Icons.mic_off : Icons.mic,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      ),
                      
                      // إنهاء المكالمة
                      GestureDetector(
                        onTap: _endCall,
                        child: Container(
                          width: 70,
                          height: 70,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.call_end,
                            color: Colors.white,
                            size: 35,
                          ),
                        ),
                      ),
                      
                      // مكبر الصوت
                      GestureDetector(
                        onTap: _toggleSpeaker,
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: _isSpeakerOn 
                                ? AppTheme.primaryColor 
                                : Colors.white.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ] else ...[
                // رسالة انتهاء المكالمة
                const Text(
                  'انتهت المكالمة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              
              const SizedBox(height: 50),
            ],
          ),
        ),
      ),
    );
  }

  String _getCallStatus() {
    if (_isCallEnded) return 'انتهت المكالمة';
    if (_isConnected) return 'مكالمة صوتية';
    if (widget.isIncoming) return 'مكالمة واردة';
    return 'مكالمة صادرة';
  }

  String _getCallStatusText() {
    if (widget.isIncoming) return 'مكالمة صوتية واردة...';
    if (_isConnected) return 'متصل';
    return 'جاري الاتصال...';
  }
}
