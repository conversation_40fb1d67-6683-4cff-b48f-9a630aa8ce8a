import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;

class ActivityLogScreen extends StatefulWidget {
  const ActivityLogScreen({super.key});

  @override
  State<ActivityLogScreen> createState() => _ActivityLogScreenState();
}

class _ActivityLogScreenState extends State<ActivityLogScreen> {
  String _selectedFilter = 'all';
  
  final List<ActivityItem> _activities = [
    ActivityItem(
      type: ActivityType.post,
      title: 'نشرت منشوراً جديداً',
      description: 'شاركت صورة من رحلتي إلى دبي',
      timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
      icon: Icons.post_add,
      color: Colors.blue,
    ),
    ActivityItem(
      type: ActivityType.like,
      title: 'أعجبت بمنشور',
      description: 'أعجبت بمنشور أحمد محمد',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      icon: Icons.favorite,
      color: Colors.red,
    ),
    ActivityItem(
      type: ActivityType.comment,
      title: 'علقت على منشور',
      description: 'علقت على منشور فاطمة أحمد: "رائع جداً!"',
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      icon: Icons.comment,
      color: Colors.red,
    ),
    ActivityItem(
      type: ActivityType.share,
      title: 'شاركت منشوراً',
      description: 'شاركت منشور عن التكنولوجيا',
      timestamp: DateTime.now().subtract(const Duration(hours: 6)),
      icon: Icons.share,
      color: Colors.orange,
    ),
    ActivityItem(
      type: ActivityType.follow,
      title: 'تابعت مستخدماً جديداً',
      description: 'بدأت متابعة سارة علي',
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      icon: Icons.person_add,
      color: Colors.purple,
    ),
    ActivityItem(
      type: ActivityType.story,
      title: 'نشرت قصة جديدة',
      description: 'شاركت قصة عن يومك',
      timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
      icon: Icons.auto_stories,
      color: Colors.pink,
    ),
    ActivityItem(
      type: ActivityType.message,
      title: 'أرسلت رسالة',
      description: 'أرسلت رسالة إلى محمد أحمد',
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      icon: Icons.message,
      color: Colors.cyan,
    ),
    ActivityItem(
      type: ActivityType.login,
      title: 'سجلت الدخول',
      description: 'سجلت الدخول من جهاز Android',
      timestamp: DateTime.now().subtract(const Duration(days: 3)),
      icon: Icons.login,
      color: Colors.grey,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'سجل النشاط',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // فلاتر النشاط
          _buildFilterTabs(),
          
          // قائمة الأنشطة
          Expanded(
            child: _buildActivitiesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    final filters = [
      {'key': 'all', 'label': 'الكل', 'icon': Icons.all_inclusive},
      {'key': 'posts', 'label': 'المنشورات', 'icon': Icons.post_add},
      {'key': 'interactions', 'label': 'التفاعلات', 'icon': Icons.favorite},
      {'key': 'social', 'label': 'اجتماعي', 'icon': Icons.people},
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['key'];
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedFilter = filter['key'] as String;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFF1877F2) : Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.grey[600],
                  ),
                  const SizedBox(width: 6),
                  Text(
                    filter['label'] as String,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActivitiesList() {
    final filteredActivities = _getFilteredActivities();
    
    if (filteredActivities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد أنشطة في هذا القسم',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredActivities.length,
      itemBuilder: (context, index) {
        final activity = filteredActivities[index];
        return _buildActivityItem(activity);
      },
    );
  }

  Widget _buildActivityItem(ActivityItem activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // أيقونة النشاط
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: activity.color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              activity.icon,
              color: activity.color,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // تفاصيل النشاط
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  activity.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  timeago.format(activity.timestamp, locale: 'ar'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
          
          // زر الخيارات
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: Colors.grey[400]),
            onSelected: (value) => _handleActivityAction(value, activity),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'view',
                child: Row(
                  children: [
                    Icon(Icons.visibility, size: 16),
                    SizedBox(width: 8),
                    Text('عرض'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<ActivityItem> _getFilteredActivities() {
    switch (_selectedFilter) {
      case 'posts':
        return _activities.where((a) => 
            a.type == ActivityType.post || a.type == ActivityType.story).toList();
      case 'interactions':
        return _activities.where((a) => 
            a.type == ActivityType.like || 
            a.type == ActivityType.comment || 
            a.type == ActivityType.share).toList();
      case 'social':
        return _activities.where((a) => 
            a.type == ActivityType.follow || 
            a.type == ActivityType.message).toList();
      default:
        return _activities;
    }
  }

  void _handleActivityAction(String action, ActivityItem activity) {
    switch (action) {
      case 'view':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('عرض: ${activity.title}')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(activity);
        break;
    }
  }

  void _showDeleteConfirmation(ActivityItem activity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف النشاط'),
        content: const Text('هل أنت متأكد من حذف هذا النشاط من السجل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _activities.remove(activity);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف النشاط')),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في سجل النشاط'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'ابحث عن نشاط معين...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }
}

enum ActivityType {
  post,
  like,
  comment,
  share,
  follow,
  story,
  message,
  login,
}

class ActivityItem {
  final ActivityType type;
  final String title;
  final String description;
  final DateTime timestamp;
  final IconData icon;
  final Color color;

  ActivityItem({
    required this.type,
    required this.title,
    required this.description,
    required this.timestamp,
    required this.icon,
    required this.color,
  });
}
