import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/social_provider.dart';
import '../models/story.dart';

class StoryLongPressMenu extends StatelessWidget {
  final UserStories userStories;
  final VoidCallback? onViewProfile;
  final VoidCallback? onHideStory;
  final VoidCallback? onMuteUser;
  final VoidCallback? onReportStory;
  final VoidCallback? onCopyLink;

  const StoryLongPressMenu({
    super.key,
    required this.userStories,
    this.onViewProfile,
    this.onHideStory,
    this.onMuteUser,
    this.onReportStory,
    this.onCopyLink,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        mainAxisSize: MainAxisSize.min,
        children: [
          // معلومات المستخدم
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.grey[300],
                  backgroundImage: userStories.user.avatar != null
                      ? NetworkImage(userStories.user.avatar!)
                      : null,
                  child: userStories.user.avatar == null
                      ? Icon(
                          Icons.person,
                          color: Colors.grey[600],
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userStories.user.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '${userStories.stories.length} قصة',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // قائمة الخيارات
          _buildMenuItem(
            icon: Icons.person,
            title: 'عرض الملف الشخصي',
            onTap: () {
              Navigator.pop(context);
              onViewProfile?.call();
            },
          ),

          _buildMenuItem(
            icon: Icons.visibility_off,
            title: 'إخفاء قصة ${userStories.user.name}',
            subtitle: 'لن تظهر قصص هذا الشخص في المستقبل',
            onTap: () {
              Navigator.pop(context);
              onHideStory?.call();
              _showHideConfirmation(context);
            },
          ),

          _buildMenuItem(
            icon: Icons.volume_off,
            title: 'كتم ${userStories.user.name}',
            subtitle: 'لن تحصل على إشعارات من هذا الشخص',
            onTap: () {
              Navigator.pop(context);
              onMuteUser?.call();
              _showMuteConfirmation(context);
            },
          ),

          _buildMenuItem(
            icon: Icons.link,
            title: 'نسخ رابط القصة',
            onTap: () {
              Navigator.pop(context);
              onCopyLink?.call();
              _showCopyConfirmation(context);
            },
          ),

          const Divider(height: 1),

          _buildMenuItem(
            icon: Icons.report,
            title: 'الإبلاغ عن القصة',
            subtitle: 'إبلاغ عن محتوى غير مناسب',
            textColor: Colors.red,
            onTap: () {
              Navigator.pop(context);
              onReportStory?.call();
              _showReportDialog(context);
            },
          ),

          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Color? textColor,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              color: textColor ?? Colors.grey[700],
              size: 22,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: textColor ?? Colors.black,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showHideConfirmation(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إخفاء قصص ${userStories.user.name}'),
        action: SnackBarAction(
          label: 'تراجع',
          onPressed: () {
            // إلغاء الإخفاء
          },
        ),
      ),
    );
  }

  void _showMuteConfirmation(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم كتم ${userStories.user.name}'),
        action: SnackBarAction(
          label: 'تراجع',
          onPressed: () {
            // إلغاء الكتم
          },
        ),
      ),
    );
  }

  void _showCopyConfirmation(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ رابط القصة'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showReportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن القصة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('لماذا تريد الإبلاغ عن قصة ${userStories.user.name}؟'),
            const SizedBox(height: 16),
            ..._buildReportOptions(context),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildReportOptions(BuildContext context) {
    final options = [
      'محتوى غير مناسب',
      'عنف أو تهديد',
      'تحرش أو إزعاج',
      'معلومات خاطئة',
      'انتهاك حقوق الطبع',
      'أخرى',
    ];

    return options.map((option) => 
      ListTile(
        title: Text(option),
        onTap: () {
          Navigator.pop(context);
          _submitReport(context, option);
        },
      ),
    ).toList();
  }

  void _submitReport(BuildContext context, String reason) {
    // هنا يتم إرسال التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال التقرير. شكراً لك!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

// Widget للنقر المستمر على القصة
class StoryLongPressWrapper extends StatelessWidget {
  final Widget child;
  final UserStories userStories;
  final VoidCallback? onTap;

  const StoryLongPressWrapper({
    super.key,
    required this.child,
    required this.userStories,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: () => _showStoryMenu(context),
      child: child,
    );
  }

  void _showStoryMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        child: StoryLongPressMenu(
          userStories: userStories,
          onViewProfile: () => _navigateToProfile(context),
          onHideStory: () => _hideStory(context),
          onMuteUser: () => _muteUser(context),
          onReportStory: () => _reportStory(context),
          onCopyLink: () => _copyStoryLink(context),
        ),
      ),
    );
  }

  void _navigateToProfile(BuildContext context) {
    // التنقل إلى الملف الشخصي
    Navigator.pushNamed(
      context,
      '/profile',
      arguments: {
        'userId': userStories.user.id,
        'userName': userStories.user.name,
      },
    );
  }

  void _hideStory(BuildContext context) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    // إخفاء قصص هذا المستخدم
    // يمكن إضافة منطق الإخفاء هنا
  }

  void _muteUser(BuildContext context) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    // كتم المستخدم
    // يمكن إضافة منطق الكتم هنا
  }

  void _reportStory(BuildContext context) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    // الإبلاغ عن القصة
    // يمكن إضافة منطق الإبلاغ هنا
  }

  void _copyStoryLink(BuildContext context) {
    // نسخ رابط القصة
    // يمكن إضافة منطق النسخ هنا
  }
}
