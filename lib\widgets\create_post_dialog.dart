import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/social_provider.dart';
import '../models/post.dart';
import '../models/link_preview.dart';
import '../theme/app_theme.dart';
import 'link_preview_widget.dart';
import 'feelings_activities_picker.dart';
import '../screens/start_live_stream_screen.dart';

class CreatePostDialog extends StatefulWidget {
  final PostType? initialType;
  final bool showFeelings;
  final bool showLocation;

  const CreatePostDialog({
    super.key,
    this.initialType,
    this.showFeelings = false,
    this.showLocation = false,
  });

  @override
  State<CreatePostDialog> createState() => _CreatePostDialogState();
}

class _CreatePostDialogState extends State<CreatePostDialog> {
  final TextEditingController _contentController = TextEditingController();
  PostType _selectedType = PostType.text;
  PostBackground _selectedBackground = PostBackground.none;
  String? _selectedFeeling;
  String? _selectedActivity;
  String? _selectedLocation;

  // نصوص العرض للمشاعر والأنشطة
  String? _feelingDisplay;
  String? _activityDisplay;
  String? _locationDisplay;
  final List<String> _taggedUsers = [];
  bool _isPosting = false;
  File? _selectedImage;
  File? _selectedVideo;
  final ImagePicker _picker = ImagePicker();

  // متغيرات معاينة الروابط
  LinkPreview? _linkPreview;
  bool _isGeneratingPreview = false;

  final List<String> _feelings = [
    'سعيد', 'حزين', 'متحمس', 'ممتن', 'مرتاح', 'متعب', 
    'فخور', 'محبط', 'متفائل', 'قلق', 'واثق', 'مندهش'
  ];

  final List<String> _activities = [
    'يأكل', 'يشرب', 'يسافر', 'يعمل', 'يدرس', 'يقرأ',
    'يشاهد', 'يلعب', 'يمارس الرياضة', 'يطبخ', 'يتسوق', 'يستمع للموسيقى'
  ];

  final List<String> _locations = [
    'المنزل', 'العمل', 'المدرسة', 'المطعم', 'المقهى', 'الحديقة',
    'المول', 'الشاطئ', 'الجبل', 'المكتبة', 'المستشفى', 'المطار'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialType != null) {
      _selectedType = widget.initialType!;
    }

    // إضافة listener لكشف الروابط
    _contentController.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    final text = _contentController.text;
    _checkForLinks(text);
  }

  void _checkForLinks(String text) {
    final urlRegex = RegExp(
      r'https?://[^\s]+',
      caseSensitive: false,
    );

    final match = urlRegex.firstMatch(text);
    if (match != null) {
      final url = match.group(0)!;
      _generateLinkPreview(url);
    } else {
      if (_linkPreview != null) {
        setState(() {
          _linkPreview = null;
        });
      }
    }
  }

  void _generateLinkPreview(String url) async {
    if (_isGeneratingPreview) return;

    setState(() {
      _isGeneratingPreview = true;
    });

    // محاكاة تحميل المعاينة
    await Future.delayed(const Duration(milliseconds: 800));

    final preview = LinkPreview.generatePreview(url);

    if (mounted) {
      setState(() {
        _linkPreview = preview;
        _isGeneratingPreview = false;
      });
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // شريط العنوان
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
                const Expanded(
                  child: Text(
                    'إنشاء منشور',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                ElevatedButton(
                  onPressed: _isPosting ? null : _createPost,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: _isPosting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('نشر'),
                ),
              ],
            ),
          ),

          // محتوى الحوار
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المستخدم
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: AppTheme.primaryColor,
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المستخدم الحالي',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            'عام',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // حقل النص
                  Container(
                    decoration: BoxDecoration(
                      color: _getBackgroundColor(),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextField(
                      controller: _contentController,
                      maxLines: null,
                      minLines: 3,
                      decoration: InputDecoration(
                        hintText: 'بما تفكر؟',
                        hintStyle: TextStyle(
                          color: _selectedBackground != PostBackground.none 
                              ? Colors.white70 
                              : Colors.grey[600],
                          fontSize: 18,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(16),
                      ),
                      style: TextStyle(
                        fontSize: 18,
                        color: _selectedBackground != PostBackground.none
                            ? Colors.black  // تغيير لون النص إلى أسود
                            : Colors.black,
                        shadows: _selectedBackground != PostBackground.none
                            ? [
                                const Shadow(
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                  color: Colors.white,
                                ),
                                const Shadow(
                                  offset: Offset(-1, -1),
                                  blurRadius: 1,
                                  color: Colors.white,
                                ),
                              ]
                            : null,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // معاينة الروابط
                  if (_isGeneratingPreview) ...[
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          const SizedBox(width: 12),
                          const Text('جاري تحميل معاينة الرابط...'),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  if (_linkPreview != null) ...[
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Stack(
                        children: [
                          LinkPreviewWidget(
                            linkPreview: _linkPreview!,
                            showInPost: false,
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _linkPreview = null;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.7),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // عرض الصورة المختارة
                  if (_selectedImage != null) ...[
                    Container(
                      height: 200,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        image: DecorationImage(
                          image: FileImage(_selectedImage!),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            top: 8,
                            right: 8,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedImage = null;
                                  _selectedType = PostType.text;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.7),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // عرض الفيديو المختار
                  if (_selectedVideo != null) ...[
                    Container(
                      height: 200,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Stack(
                        children: [
                          Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.play_circle_filled,
                                  color: Colors.white,
                                  size: 50,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'فيديو محدد',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedVideo = null;
                                  _selectedType = PostType.text;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.7),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // خيارات الخلفية
                  if (_selectedType == PostType.text && _selectedImage == null && _selectedVideo == null) ...[
                    const Text(
                      'خلفية المنشور:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 60,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: PostBackground.values.length,
                        itemBuilder: (context, index) {
                          final background = PostBackground.values[index];
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedBackground = background;
                              });
                            },
                            child: Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.only(left: 8),
                              decoration: BoxDecoration(
                                color: _getBackgroundColorForType(background),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: _selectedBackground == background
                                      ? AppTheme.primaryColor
                                      : Colors.grey[300]!,
                                  width: _selectedBackground == background ? 3 : 1,
                                ),
                              ),
                              child: background == PostBackground.none
                                  ? const Icon(Icons.format_color_text)
                                  : null,
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // المشاعر والأنشطة
                  if (_selectedFeeling != null || widget.showFeelings) ...[
                    _buildSectionTitle('المشاعر'),
                    _buildChipSection(_feelings, _selectedFeeling, (feeling) {
                      setState(() {
                        _selectedFeeling = _selectedFeeling == feeling ? null : feeling;
                      });
                    }),
                    const SizedBox(height: 16),
                  ],

                  if (_selectedActivity != null) ...[
                    _buildSectionTitle('النشاط'),
                    _buildChipSection(_activities, _selectedActivity, (activity) {
                      setState(() {
                        _selectedActivity = _selectedActivity == activity ? null : activity;
                      });
                    }),
                    const SizedBox(height: 16),
                  ],

                  if (_selectedLocation != null || widget.showLocation) ...[
                    _buildSectionTitle('الموقع'),
                    _buildChipSection(_locations, _selectedLocation, (location) {
                      setState(() {
                        _selectedLocation = _selectedLocation == location ? null : location;
                      });
                    }),
                    const SizedBox(height: 16),
                  ],

                  // خيارات إضافية
                  _buildOptionsRow(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildChipSection(
    List<String> items,
    String? selectedItem,
    Function(String) onSelected,
  ) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: items.map((item) {
        final isSelected = selectedItem == item;
        return FilterChip(
          label: Text(item),
          selected: isSelected,
          onSelected: (_) => onSelected(item),
          selectedColor: AppTheme.primaryLightColor,
          checkmarkColor: Colors.white,
        );
      }).toList(),
    );
  }

  Widget _buildOptionsRow() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // الصف الأول
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildOptionButton(
                icon: Icons.photo_library,
                label: 'صورة',
                color: Colors.green,
                onTap: _pickImage,
              ),
              _buildOptionButton(
                icon: Icons.videocam,
                label: 'فيديو',
                color: Colors.red,
                onTap: _pickVideo,
              ),
              _buildOptionButton(
                icon: Icons.emoji_emotions,
                label: 'شعور',
                color: Colors.orange,
                onTap: _showFeelingPicker,
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildOptionButton(
                icon: Icons.location_on,
                label: 'موقع',
                color: Colors.blue,
                onTap: _showFeelingPicker,
              ),
              _buildOptionButton(
                icon: Icons.live_tv,
                label: 'بث مباشر',
                color: Colors.red,
                onTap: _startLiveStream,
              ),
              const SizedBox(width: 60), // مساحة فارغة للتوازن
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOptionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    return _getBackgroundColorForType(_selectedBackground);
  }

  Color _getBackgroundColorForType(PostBackground background) {
    switch (background) {
      case PostBackground.none:
        return Colors.transparent;
      case PostBackground.gradient1:
        return Colors.blue;
      case PostBackground.gradient2:
        return Colors.purple;
      case PostBackground.gradient3:
        return Colors.green;
      case PostBackground.gradient4:
        return Colors.orange;
      case PostBackground.gradient5:
        return Colors.red;
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _selectedVideo = null; // إزالة الفيديو إذا كان موجود
          _selectedType = PostType.image;
          _selectedBackground = PostBackground.none; // إزالة الخلفية
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في اختيار الصورة')),
        );
      }
    }
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        setState(() {
          _selectedVideo = File(video.path);
          _selectedImage = null; // إزالة الصورة إذا كانت موجودة
          _selectedType = PostType.video;
          _selectedBackground = PostBackground.none; // إزالة الخلفية
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في اختيار الفيديو')),
        );
      }
    }
  }

  void _showFeelingPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FeelingsActivitiesPicker(
        onSelected: (type, id, name, display) {
          setState(() {
            if (type == 'feeling') {
              _selectedFeeling = id;
              _feelingDisplay = display;
            } else if (type == 'activity') {
              _selectedActivity = id;
              _activityDisplay = display;
            } else if (type == 'location') {
              _selectedLocation = id;
              _locationDisplay = display;
            }
          });
        },
      ),
    );
  }

  void _startLiveStream() {
    // إغلاق حوار إنشاء المنشور
    Navigator.pop(context);

    // فتح شاشة بدء البث المباشر
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StartLiveStreamScreen(),
      ),
    );
  }



  Future<void> _createPost() async {
    if (_contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة محتوى المنشور')),
      );
      return;
    }

    setState(() {
      _isPosting = true;
    });

    try {
      await Provider.of<SocialProvider>(context, listen: false).createPost(
        content: _contentController.text.trim(),
        type: _selectedType,
        background: _selectedBackground,
        feeling: _selectedFeeling,
        activity: _selectedActivity,
        location: _selectedLocation,
        feelingDisplay: _feelingDisplay,
        activityDisplay: _activityDisplay,
        locationDisplay: _locationDisplay,
        taggedUsers: _taggedUsers,
        imageFile: _selectedImage,
        videoFile: _selectedVideo,
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم نشر المنشور بنجاح!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في نشر المنشور')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPosting = false;
        });
      }
    }
  }
}
