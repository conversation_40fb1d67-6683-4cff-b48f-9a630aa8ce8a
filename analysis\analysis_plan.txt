تحليل مشروع Arzawo - خطة شاملة

1. نظرة عامة على هيكل المشروع:
- المشروع مبني باستخدام Flutter.
- يحتوي على مجلد lib الذي يحتوي على نماذج (models)، مزودات (providers)، شاشات (screens)، خدمات (services)، وعناصر واجهة مستخدم (widgets).
- يستخدم مزودات (Provider) لإدارة الحالة.

2. المكونات الرئيسية:
- main.dart: نقطة الدخول، يستخدم MultiProvider لإدارة الحالة، يدعم اللغة العربية واتجاه النص من اليمين إلى اليسار.
- AuthProvider: مسؤول عن إدارة حالة المصادقة باستخدام MockDataService.
- AuthService: مسؤول عن تسجيل المستخدمين، تسجيل الدخول، تسجيل الخروج، والتحقق من حالة تسجيل الدخول باستخدام SharedPreferences.
- شاشات تسجيل الدخول والتنقل الرئيسية: FacebookLoginScreen و MainNavigationScreen.

3. نقاط الدخول والتنقل:
- يبدأ التطبيق بفحص حالة المصادقة.
- يعرض شاشة تسجيل الدخول أو الشاشة الرئيسية بناءً على حالة المصادقة.

4. إدارة الحالة والمصادقة:
- يستخدم Provider لإدارة حالة المصادقة وحالة التطبيق.
- يدعم تسجيل الدخول، تسجيل الخروج، والتحقق من حالة المستخدم.

5. اللغات والاتجاهات:
- يدعم اللغة العربية بالكامل.
- يستخدم اتجاه النص من اليمين إلى اليسار.

6. الخطوات التالية المقترحة:
- قراءة ملفات مزودات أخرى لفهم إدارة الحالة بشكل أعمق.
- قراءة ملفات الشاشات الرئيسية لفهم واجهة المستخدم.
- قراءة ملفات الخدمات الأخرى لفهم الوظائف المساعدة.
- تقديم تقرير تحليل مفصل أو تنفيذ طلبات تطوير بناءً على متطلبات المستخدم.

يرجى تأكيد الخطة أو طلب أي تعديلات أو إضافات.
