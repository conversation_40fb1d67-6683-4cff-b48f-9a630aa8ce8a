import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../providers/social_provider.dart';
import '../models/story.dart';
import '../theme/app_theme.dart';
import 'create_story_dialog.dart';
import 'story_viewer.dart';
import '../screens/live_streams_screen.dart';
import 'story_long_press_menu.dart';

class StoriesSection extends StatelessWidget {
  const StoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SocialProvider>(
      builder: (context, socialProvider, child) {
        final stories = socialProvider.stories;
        
        return Container(
          height: 220, // زيادة الارتفاع لتكون مثل فيسبوك
          margin: const EdgeInsets.symmetric(vertical: 16),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            itemCount: stories.length + 2, // +2 لإضافة قصة جديدة وزر البثوث
            itemBuilder: (context, index) {
              if (index == 0) {
                // زر إضافة قصة جديدة
                return _AddStoryCard(
                  onTap: () => _showCreateStoryDialog(context),
                );
              } else if (index == 1) {
                // زر البثوث المباشرة
                return _LiveStreamsCard(
                  onTap: () => _openLiveStreams(context),
                );
              }

              final userStories = stories[index - 2];
              return StoryLongPressWrapper(
                userStories: userStories,
                onTap: () => _viewStories(context, userStories),
                child: _StoryCard(
                  userStories: userStories,
                  onTap: () => _viewStories(context, userStories),
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _showCreateStoryDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const CreateStoryDialog(),
    );
  }

  void _viewStories(BuildContext context, UserStories userStories) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryViewerWidget(userStories: userStories),
      ),
    );
  }

  void _openLiveStreams(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LiveStreamsScreen(),
      ),
    );
  }
}

class _LiveStreamsCard extends StatelessWidget {
  final VoidCallback onTap;

  const _LiveStreamsCard({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 130, // عرض أكبر لتظهر 3 قصص فقط في الشاشة
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.red,
              Colors.pink,
              Colors.purple,
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withValues(alpha: 0.3),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          children: [
            // منطقة الأيقونة
            Expanded(
              flex: 4, // زيادة المساحة
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                child: Stack(
                  children: [
                    // أيقونة البث المباشر
                    const Center(
                      child: Icon(
                        Icons.live_tv,
                        color: Colors.white,
                        size: 40, // أيقونة أكبر
                      ),
                    ),
                    // مؤشر البث المباشر
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 2),
                            const Text(
                              'مباشر',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // النص
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: const Center(
                  child: Text(
                    'البثوث',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AddStoryCard extends StatelessWidget {
  final VoidCallback onTap;

  const _AddStoryCard({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 130, // عرض أكبر لتظهر 3 قصص فقط في الشاشة
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          children: [
            // منطقة الصورة
            Expanded(
              flex: 4, // زيادة المساحة
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                child: Stack(
                  children: [
                    // صورة المستخدم
                    Center(
                      child: CircleAvatar(
                        radius: 30, // صورة أكبر
                        backgroundColor: AppTheme.primaryColor,
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 30, // أيقونة أكبر
                        ),
                      ),
                    ),
                    // زر الإضافة
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Container(
                        width: 32, // زر أكبر
                        height: 32,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 3),
                        ),
                        child: const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 20, // أيقونة أكبر
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // النص
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: const Center(
                  child: Text(
                    'قصتك',
                    style: TextStyle(
                      fontSize: 14, // نص أكبر
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _StoryCard extends StatelessWidget {
  final UserStories userStories;
  final VoidCallback onTap;

  const _StoryCard({
    required this.userStories,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final latestStory = userStories.latestStory;
    final hasUnviewedStories = userStories.activeStories
        .any((story) => !story.viewers.any((v) => v.userId == 'current_user'));

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 130, // عرض أكبر لتظهر 3 قصص فقط في الشاشة
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Stack(
          children: [
            // الخلفية مع معاينة المحتوى
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: latestStory?.type == StoryType.text
                    ? _getStoryGradient(latestStory!.background)
                    : null,
                image: latestStory?.type != StoryType.text && latestStory?.mediaUrl != null
                    ? DecorationImage(
                        image: _getImageProvider(latestStory!.mediaUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
                color: latestStory?.type == StoryType.text
                    ? null
                    : Colors.grey[300],
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.4),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // معاينة النص للقصص النصية
                    if (latestStory?.type == StoryType.text && latestStory!.content.isNotEmpty)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Text(
                            latestStory.content,
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                  color: Colors.white,
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                    // أيقونة نوع القصة
                    Positioned(
                      top: 6,
                      right: 6,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getStoryTypeIcon(latestStory?.type),
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),

                    // أيقونة تشغيل للفيديوهات
                    if (latestStory?.type == StoryType.video)
                      Center(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.play_arrow,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // المحتوى
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // صورة المستخدم مع حدود
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: hasUnviewedStories 
                            ? AppTheme.primaryColor 
                            : Colors.grey,
                        width: 2,
                      ),
                    ),
                    child: CircleAvatar(
                      radius: 20, // صورة أكبر
                      backgroundColor: AppTheme.primaryLightColor,
                      child: Text(
                        userStories.user.name.isNotEmpty
                            ? userStories.user.name[0].toUpperCase()
                            : '؟',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16, // نص أكبر
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // اسم المستخدم
                  Text(
                    userStories.user.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 13, // نص أكبر
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black87,
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            // مؤشر عدد القصص
            if (userStories.activeStories.length > 1)
              Positioned(
                top: 4,
                left: 4,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${userStories.activeStories.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // دالة لتحديد نوع مزود الصورة (محلي أم شبكة)
  ImageProvider _getImageProvider(String imageUrl) {
    if (imageUrl.startsWith('http')) {
      return NetworkImage(imageUrl);
    } else {
      return FileImage(File(imageUrl));
    }
  }

  // دالة لتحديد أيقونة نوع القصة
  IconData _getStoryTypeIcon(StoryType? type) {
    switch (type) {
      case StoryType.text:
        return Icons.text_fields;
      case StoryType.image:
        return Icons.image;
      case StoryType.video:
        return Icons.play_circle_filled;
      case null:
        return Icons.help_outline;
    }
  }

  LinearGradient? _getStoryGradient(StoryBackground background) {
    switch (background) {
      case StoryBackground.none:
        return null;
      case StoryBackground.gradient1:
        return const LinearGradient(
          colors: [Colors.blue, Colors.purple],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient2:
        return const LinearGradient(
          colors: [Colors.pink, Colors.orange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient3:
        return const LinearGradient(
          colors: [Colors.green, Colors.teal],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient4:
        return const LinearGradient(
          colors: [Colors.orange, Colors.red],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient5:
        return const LinearGradient(
          colors: [Colors.purple, Colors.indigo],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient6:
        return const LinearGradient(
          colors: [Colors.teal, Colors.blue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }
}
