import 'user.dart';
import 'reaction_types.dart';

enum CommentType {
  text,
  image,
  video,
  gif,
  sticker,
  link,
}

class CommentReaction {
  final String id;
  final String userId;
  final String userName;
  final ReactionType type;
  final DateTime timestamp;

  const CommentReaction({
    required this.id,
    required this.userId,
    required this.userName,
    required this.type,
    required this.timestamp,
  });

  factory CommentReaction.fromJson(Map<String, dynamic> json) {
    return CommentReaction(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      type: ReactionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  String get emoji {
    switch (type) {
      case ReactionType.like:
        return '👍';
      case ReactionType.love:
        return '❤️';
      case ReactionType.haha:
        return '😂';
      case ReactionType.wow:
        return '😮';
      case ReactionType.sad:
        return '😢';
      case ReactionType.angry:
        return '😡';
    }
  }

  String get label {
    switch (type) {
      case ReactionType.like:
        return 'إعجاب';
      case ReactionType.love:
        return 'حب';
      case ReactionType.haha:
        return 'ضحك';
      case ReactionType.wow:
        return 'تعجب';
      case ReactionType.sad:
        return 'حزن';
      case ReactionType.angry:
        return 'غضب';
    }
  }
}

class Comment {
  final String id;
  final String postId;
  final String authorId;
  final User author;
  final String content;
  final CommentType type;
  final String? mediaUrl;
  final String? linkUrl;
  final String? linkTitle;
  final String? linkDescription;
  final String? linkImage;
  final DateTime timestamp;
  final DateTime? editedAt;
  final bool isEdited;
  final String? parentCommentId; // للردود
  final List<Comment> replies;
  final List<CommentReaction> reactions;
  final Map<ReactionType, int> reactionCounts;
  final int totalReactions;
  final int repliesCount;
  final bool isDeleted;
  final String? deletedReason;

  const Comment({
    required this.id,
    required this.postId,
    required this.authorId,
    required this.author,
    required this.content,
    this.type = CommentType.text,
    this.mediaUrl,
    this.linkUrl,
    this.linkTitle,
    this.linkDescription,
    this.linkImage,
    required this.timestamp,
    this.editedAt,
    this.isEdited = false,
    this.parentCommentId,
    this.replies = const [],
    this.reactions = const [],
    this.reactionCounts = const {},
    this.totalReactions = 0,
    this.repliesCount = 0,
    this.isDeleted = false,
    this.deletedReason,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'],
      postId: json['postId'],
      authorId: json['authorId'],
      author: User.fromJson(json['author']),
      content: json['content'],
      type: CommentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CommentType.text,
      ),
      mediaUrl: json['mediaUrl'],
      linkUrl: json['linkUrl'],
      linkTitle: json['linkTitle'],
      linkDescription: json['linkDescription'],
      linkImage: json['linkImage'],
      timestamp: DateTime.parse(json['timestamp']),
      editedAt: json['editedAt'] != null ? DateTime.parse(json['editedAt']) : null,
      isEdited: json['isEdited'] ?? false,
      parentCommentId: json['parentCommentId'],
      replies: (json['replies'] as List<dynamic>?)
          ?.map((reply) => Comment.fromJson(reply))
          .toList() ?? [],
      reactions: (json['reactions'] as List<dynamic>?)
          ?.map((reaction) => CommentReaction.fromJson(reaction))
          .toList() ?? [],
      reactionCounts: Map<ReactionType, int>.from(
        (json['reactionCounts'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(
            ReactionType.values.firstWhere((e) => e.name == key),
            value as int,
          ),
        ) ?? {},
      ),
      totalReactions: json['totalReactions'] ?? 0,
      repliesCount: json['repliesCount'] ?? 0,
      isDeleted: json['isDeleted'] ?? false,
      deletedReason: json['deletedReason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'postId': postId,
      'authorId': authorId,
      'author': author.toJson(),
      'content': content,
      'type': type.name,
      'mediaUrl': mediaUrl,
      'linkUrl': linkUrl,
      'linkTitle': linkTitle,
      'linkDescription': linkDescription,
      'linkImage': linkImage,
      'timestamp': timestamp.toIso8601String(),
      'editedAt': editedAt?.toIso8601String(),
      'isEdited': isEdited,
      'parentCommentId': parentCommentId,
      'replies': replies.map((reply) => reply.toJson()).toList(),
      'reactions': reactions.map((reaction) => reaction.toJson()).toList(),
      'reactionCounts': reactionCounts.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'totalReactions': totalReactions,
      'repliesCount': repliesCount,
      'isDeleted': isDeleted,
      'deletedReason': deletedReason,
    };
  }

  Comment copyWith({
    String? id,
    String? postId,
    String? authorId,
    User? author,
    String? content,
    CommentType? type,
    String? mediaUrl,
    String? linkUrl,
    String? linkTitle,
    String? linkDescription,
    String? linkImage,
    DateTime? timestamp,
    DateTime? editedAt,
    bool? isEdited,
    String? parentCommentId,
    List<Comment>? replies,
    List<CommentReaction>? reactions,
    Map<ReactionType, int>? reactionCounts,
    int? totalReactions,
    int? repliesCount,
    bool? isDeleted,
    String? deletedReason,
  }) {
    return Comment(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      authorId: authorId ?? this.authorId,
      author: author ?? this.author,
      content: content ?? this.content,
      type: type ?? this.type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      linkUrl: linkUrl ?? this.linkUrl,
      linkTitle: linkTitle ?? this.linkTitle,
      linkDescription: linkDescription ?? this.linkDescription,
      linkImage: linkImage ?? this.linkImage,
      timestamp: timestamp ?? this.timestamp,
      editedAt: editedAt ?? this.editedAt,
      isEdited: isEdited ?? this.isEdited,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      replies: replies ?? this.replies,
      reactions: reactions ?? this.reactions,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      totalReactions: totalReactions ?? this.totalReactions,
      repliesCount: repliesCount ?? this.repliesCount,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedReason: deletedReason ?? this.deletedReason,
    );
  }

  // دوال مساعدة
  bool get isReply => parentCommentId != null;
  bool get hasReplies => repliesCount > 0;
  bool get hasReactions => totalReactions > 0;
  bool get hasMedia => mediaUrl != null;
  bool get hasLink => linkUrl != null;

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
    }
  }

  // الحصول على أكثر التفاعلات
  ReactionType? get topReaction {
    if (reactionCounts.isEmpty) return null;
    
    return reactionCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  // الحصول على تفاعل المستخدم الحالي
  CommentReaction? getUserReaction(String userId) {
    try {
      return reactions.firstWhere((reaction) => reaction.userId == userId);
    } catch (e) {
      return null;
    }
  }

  // التحقق من وجود تفاعل للمستخدم
  bool hasUserReacted(String userId) {
    return getUserReaction(userId) != null;
  }

  // الحصول على نوع تفاعل المستخدم
  ReactionType? getUserReactionType(String userId) {
    return getUserReaction(userId)?.type;
  }
}
