import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import '../models/app_settings.dart';

class AppSettingsService {
  static const String _settingsKey = 'app_settings';
  static const String _blockedUsersKey = 'blocked_users';
  
  // حفظ الإعدادات
  Future<void> saveSettings(AppSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(settings.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      print('خطأ في حفظ الإعدادات: $e');
      throw Exception('فشل في حفظ الإعدادات');
    }
  }
  
  // تحميل الإعدادات
  Future<AppSettings> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        return AppSettings.fromJson(settingsMap);
      }
      
      return const AppSettings(); // الإعدادات الافتراضية
    } catch (e) {
      print('خطأ في تحميل الإعدادات: $e');
      return const AppSettings(); // الإعدادات الافتراضية في حالة الخطأ
    }
  }
  
  // حظر مستخدم
  Future<void> blockUser(String userId) async {
    try {
      final settings = await loadSettings();
      final blockedUsers = List<String>.from(settings.blockedUsers);
      
      if (!blockedUsers.contains(userId)) {
        blockedUsers.add(userId);
        final updatedSettings = settings.copyWith(blockedUsers: blockedUsers);
        await saveSettings(updatedSettings);
      }
    } catch (e) {
      print('خطأ في حظر المستخدم: $e');
      throw Exception('فشل في حظر المستخدم');
    }
  }
  
  // إلغاء حظر مستخدم
  Future<void> unblockUser(String userId) async {
    try {
      final settings = await loadSettings();
      final blockedUsers = List<String>.from(settings.blockedUsers);
      
      blockedUsers.remove(userId);
      final updatedSettings = settings.copyWith(blockedUsers: blockedUsers);
      await saveSettings(updatedSettings);
    } catch (e) {
      print('خطأ في إلغاء حظر المستخدم: $e');
      throw Exception('فشل في إلغاء حظر المستخدم');
    }
  }
  
  // التحقق من كون المستخدم محظور
  Future<bool> isUserBlocked(String userId) async {
    try {
      final settings = await loadSettings();
      return settings.blockedUsers.contains(userId);
    } catch (e) {
      print('خطأ في التحقق من حظر المستخدم: $e');
      return false;
    }
  }
  
  // الحصول على قائمة المستخدمين المحظورين
  Future<List<String>> getBlockedUsers() async {
    try {
      final settings = await loadSettings();
      return settings.blockedUsers;
    } catch (e) {
      print('خطأ في تحميل المستخدمين المحظورين: $e');
      return [];
    }
  }
  
  // مسح ذاكرة التخزين المؤقت
  Future<void> clearCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        await cacheDir.create();
      }
    } catch (e) {
      print('خطأ في مسح ذاكرة التخزين المؤقت: $e');
      throw Exception('فشل في مسح ذاكرة التخزين المؤقت');
    }
  }
  
  // حساب حجم ذاكرة التخزين المؤقت
  Future<double> getCacheSize() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      if (!await cacheDir.exists()) {
        return 0.0;
      }
      
      double totalSize = 0;
      await for (final entity in cacheDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      
      // تحويل من بايت إلى ميجابايت
      return totalSize / (1024 * 1024);
    } catch (e) {
      print('خطأ في حساب حجم ذاكرة التخزين المؤقت: $e');
      return 0.0;
    }
  }
  
  // إنشاء نسخة احتياطية
  Future<void> createBackup(String email) async {
    try {
      // محاكاة إنشاء نسخة احتياطية
      await Future.delayed(const Duration(seconds: 2));
      
      final settings = await loadSettings();
      final updatedSettings = settings.copyWith(
        backupEnabled: true,
        backupEmail: email,
        lastBackupDate: DateTime.now(),
      );
      
      await saveSettings(updatedSettings);
      
      // هنا يمكن إضافة منطق حقيقي لرفع البيانات إلى الخادم
      print('تم إنشاء نسخة احتياطية للبريد الإلكتروني: $email');
    } catch (e) {
      print('خطأ في إنشاء النسخة الاحتياطية: $e');
      throw Exception('فشل في إنشاء النسخة الاحتياطية');
    }
  }
  
  // استعادة النسخة الاحتياطية
  Future<void> restoreBackup(String email) async {
    try {
      // محاكاة استعادة النسخة الاحتياطية
      await Future.delayed(const Duration(seconds: 3));
      
      // هنا يمكن إضافة منطق حقيقي لتحميل البيانات من الخادم
      print('تم استعادة النسخة الاحتياطية من البريد الإلكتروني: $email');
    } catch (e) {
      print('خطأ في استعادة النسخة الاحتياطية: $e');
      throw Exception('فشل في استعادة النسخة الاحتياطية');
    }
  }
  
  // تصدير البيانات
  Future<String> exportData() async {
    try {
      final settings = await loadSettings();
      
      // إنشاء ملف JSON يحتوي على جميع البيانات
      final exportData = {
        'settings': settings.toJson(),
        'exportDate': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
      
      final documentsDir = await getApplicationDocumentsDirectory();
      final exportFile = File('${documentsDir.path}/arzawo_export.json');
      
      await exportFile.writeAsString(jsonEncode(exportData));
      
      return exportFile.path;
    } catch (e) {
      print('خطأ في تصدير البيانات: $e');
      throw Exception('فشل في تصدير البيانات');
    }
  }
  
  // استيراد البيانات
  Future<void> importData(String filePath) async {
    try {
      final importFile = File(filePath);
      
      if (!await importFile.exists()) {
        throw Exception('الملف غير موجود');
      }
      
      final fileContent = await importFile.readAsString();
      final importData = jsonDecode(fileContent) as Map<String, dynamic>;
      
      if (importData['settings'] != null) {
        final settings = AppSettings.fromJson(importData['settings']);
        await saveSettings(settings);
      }
      
      print('تم استيراد البيانات بنجاح');
    } catch (e) {
      print('خطأ في استيراد البيانات: $e');
      throw Exception('فشل في استيراد البيانات');
    }
  }
  
  // إعادة تعيين الإعدادات إلى الافتراضية
  Future<void> resetToDefaults() async {
    try {
      const defaultSettings = AppSettings();
      await saveSettings(defaultSettings);
    } catch (e) {
      print('خطأ في إعادة تعيين الإعدادات: $e');
      throw Exception('فشل في إعادة تعيين الإعدادات');
    }
  }
  
  // تحديث إعداد واحد
  Future<void> updateSetting<T>(String key, T value) async {
    try {
      final settings = await loadSettings();
      AppSettings updatedSettings;
      
      switch (key) {
        case 'themeMode':
          updatedSettings = settings.copyWith(themeMode: value as ThemeMode);
          break;
        case 'fontSize':
          updatedSettings = settings.copyWith(fontSize: value as FontSize);
          break;
        case 'lastSeenPrivacy':
          updatedSettings = settings.copyWith(lastSeenPrivacy: value as PrivacySetting);
          break;
        case 'profilePhotoPrivacy':
          updatedSettings = settings.copyWith(profilePhotoPrivacy: value as PrivacySetting);
          break;
        case 'statusPrivacy':
          updatedSettings = settings.copyWith(statusPrivacy: value as PrivacySetting);
          break;
        case 'readReceiptsEnabled':
          updatedSettings = settings.copyWith(readReceiptsEnabled: value as bool);
          break;
        case 'autoDownloadPhotos':
          updatedSettings = settings.copyWith(autoDownloadPhotos: value as AutoDownloadSetting);
          break;
        case 'autoDownloadVideos':
          updatedSettings = settings.copyWith(autoDownloadVideos: value as AutoDownloadSetting);
          break;
        case 'autoDownloadDocuments':
          updatedSettings = settings.copyWith(autoDownloadDocuments: value as AutoDownloadSetting);
          break;
        case 'cacheEnabled':
          updatedSettings = settings.copyWith(cacheEnabled: value as bool);
          break;
        case 'maxCacheSize':
          updatedSettings = settings.copyWith(maxCacheSize: value as int);
          break;
        case 'backupEnabled':
          updatedSettings = settings.copyWith(backupEnabled: value as bool);
          break;
        case 'notificationsEnabled':
          updatedSettings = settings.copyWith(notificationsEnabled: value as bool);
          break;
        default:
          throw Exception('إعداد غير معروف: $key');
      }
      
      await saveSettings(updatedSettings);
    } catch (e) {
      print('خطأ في تحديث الإعداد: $e');
      throw Exception('فشل في تحديث الإعداد');
    }
  }
  
  // إرسال تقرير مشكلة
  Future<void> reportProblem({
    required String title,
    required String description,
    required String category,
    List<String>? attachments,
  }) async {
    try {
      // محاكاة إرسال التقرير
      await Future.delayed(const Duration(seconds: 1));
      
      final reportData = {
        'title': title,
        'description': description,
        'category': category,
        'attachments': attachments ?? [],
        'timestamp': DateTime.now().toIso8601String(),
        'userId': 'current_user', // يمكن الحصول عليه من AuthProvider
      };
      
      // هنا يمكن إضافة منطق حقيقي لإرسال التقرير إلى الخادم
      print('تم إرسال تقرير المشكلة: ${jsonEncode(reportData)}');
    } catch (e) {
      print('خطأ في إرسال تقرير المشكلة: $e');
      throw Exception('فشل في إرسال تقرير المشكلة');
    }
  }
}
