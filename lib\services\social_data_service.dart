import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user.dart';
import '../models/post.dart';
import '../models/story.dart';
import '../models/notification.dart';
import '../models/group.dart';
import '../models/video.dart';

class SocialDataService {
  static const String _postsKey = 'social_posts';
  static const String _storiesKey = 'social_stories';
  static const String _notificationsKey = 'social_notifications';
  static const String _groupsKey = 'social_groups';
  static const String _videosKey = 'social_videos';
  
  final Uuid _uuid = const Uuid();

  // بيانات تجريبية للمنشورات
  List<Post> get mockPosts => [
    Post(
      id: _uuid.v4(),
      authorId: '1',
      content: 'مرحباً بكم في شبكة Arzawo الاجتماعية! 🎉',
      type: PostType.text,
      background: PostBackground.gradient1,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      reactions: [
        PostReaction(
          userId: '2',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        PostReaction(
          userId: '3',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        ),
        PostReaction(
          userId: '4',
          type: 'haha',
          timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
        ),
        PostReaction(
          userId: '5',
          type: 'wow',
          timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
        ),
        PostReaction(
          userId: '1',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        ),
      ],
      comments: [
        PostComment(
          id: _uuid.v4(),
          userId: '2',
          content: 'رائع! متحمس لاستخدام التطبيق 😍',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
      ],
    ),
    Post(
      id: _uuid.v4(),
      authorId: '2',
      content: 'يوم جميل للتصوير في الحديقة 📸',
      type: PostType.image,
      media: [
        PostMedia(
          id: _uuid.v4(),
          url: 'https://picsum.photos/400/300?random=1',
          type: PostType.image,
        ),
      ],
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      reactions: [
        PostReaction(
          userId: '1',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
        PostReaction(
          userId: '4',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        PostReaction(
          userId: '2',
          type: 'haha',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        PostReaction(
          userId: '3',
          type: 'sad',
          timestamp: DateTime.now().subtract(const Duration(minutes: 45)),
        ),
        PostReaction(
          userId: '5',
          type: 'angry',
          timestamp: DateTime.now().subtract(const Duration(minutes: 20)),
        ),
        PostReaction(
          userId: 'current_user',
          type: 'wow',
          timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
        ),
      ],
      shareCount: 2,
    ),
    Post(
      id: _uuid.v4(),
      authorId: '3',
      content: 'أشعر بالسعادة اليوم! 😊',
      type: PostType.text,
      feeling: 'سعيد',
      background: PostBackground.gradient2,
      timestamp: DateTime.now().subtract(const Duration(hours: 6)),
      reactions: [
        PostReaction(
          userId: '1',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        ),
        PostReaction(
          userId: '2',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
        PostReaction(
          userId: '5',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
      ],
      comments: [
        PostComment(
          id: _uuid.v4(),
          userId: '1',
          content: 'سعيد لسعادتك! 🎉',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        ),
        PostComment(
          id: _uuid.v4(),
          userId: '4',
          content: 'يوم رائع للجميع! ☀️',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
      ],
    ),
    Post(
      id: _uuid.v4(),
      authorId: '4',
      content: 'في المكتبة أقرأ كتاباً رائعاً 📚',
      type: PostType.text,
      activity: 'قراءة',
      location: 'المكتبة العامة',
      timestamp: DateTime.now().subtract(const Duration(hours: 8)),
      reactions: [
        PostReaction(
          userId: '3',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 7)),
        ),
      ],
    ),
    Post(
      id: _uuid.v4(),
      authorId: '5',
      content: 'شكراً لكم على الدعم المستمر! 🙏',
      type: PostType.text,
      background: PostBackground.gradient3,
      taggedUsers: ['1', '2'],
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      reactions: [
        PostReaction(
          userId: '1',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 20)),
        ),
        PostReaction(
          userId: '2',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 18)),
        ),
        PostReaction(
          userId: '3',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 16)),
        ),
      ],
      shareCount: 5,
      shares: [
        PostShare(
          userId: '1',
          shareType: 'external',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
        PostShare(
          userId: '2',
          shareType: 'story',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        PostShare(
          userId: '4',
          shareType: 'message',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          targetId: '5',
        ),
        PostShare(
          userId: '5',
          shareType: 'group',
          timestamp: DateTime.now().subtract(const Duration(minutes: 45)),
          targetId: 'group2',
        ),
        PostShare(
          userId: 'current_user',
          shareType: 'external',
          timestamp: DateTime.now().subtract(const Duration(minutes: 20)),
        ),
      ],
      reposts: [
        PostRepost(
          userId: '2',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
          customComment: 'صورة جميلة جداً! 📸✨',
        ),
        PostRepost(
          userId: '4',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        PostRepost(
          userId: '1',
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
          customComment: 'مناظر خلابة 🌅',
        ),
      ],
    ),
    // إضافة منشورات فيديو
    Post(
      id: _uuid.v4(),
      authorId: '1',
      content: 'شاهدوا هذا الفيديو الرائع! 🎬',
      type: PostType.video,
      media: [
        PostMedia(
          id: _uuid.v4(),
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
          type: PostType.video,
          thumbnail: 'https://picsum.photos/400/300?random=10',
          duration: const Duration(seconds: 30),
        ),
      ],
      timestamp: DateTime.now().subtract(const Duration(hours: 3)),
      reactions: [
        PostReaction(
          userId: '2',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        PostReaction(
          userId: '3',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
      ],
      comments: [
        PostComment(
          id: _uuid.v4(),
          userId: '2',
          content: 'فيديو رائع! 👏',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ],
      shareCount: 3,
      shares: [
        PostShare(
          userId: '2',
          shareType: 'external',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        PostShare(
          userId: '4',
          shareType: 'message',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          targetId: '3',
        ),
        PostShare(
          userId: '5',
          shareType: 'group',
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
          targetId: 'group1',
        ),
      ],
      reposts: [
        PostRepost(
          userId: '1',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
          customComment: 'منشور رائع! أحببت هذه الفكرة كثيراً',
        ),
        PostRepost(
          userId: '3',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
      ],
    ),
    Post(
      id: _uuid.v4(),
      authorId: '3',
      content: 'تجربة طبخ جديدة! 👨‍🍳',
      type: PostType.video,
      media: [
        PostMedia(
          id: _uuid.v4(),
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
          type: PostType.video,
          thumbnail: 'https://picsum.photos/400/300?random=11',
          duration: const Duration(minutes: 1, seconds: 15),
        ),
      ],
      activity: 'طبخ',
      location: 'المطبخ',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      reactions: [
        PostReaction(
          userId: '1',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
        PostReaction(
          userId: '4',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
        PostReaction(
          userId: '5',
          type: 'wow',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ],
      comments: [
        PostComment(
          id: _uuid.v4(),
          userId: '4',
          content: 'يبدو لذيذاً! 😋',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
        PostComment(
          id: _uuid.v4(),
          userId: '1',
          content: 'أريد الوصفة! 📝',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
      ],
      shareCount: 7,
    ),
    Post(
      id: _uuid.v4(),
      authorId: '5',
      content: 'رحلة جميلة إلى الشاطئ! 🏖️',
      type: PostType.video,
      media: [
        PostMedia(
          id: _uuid.v4(),
          url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
          type: PostType.video,
          thumbnail: 'https://picsum.photos/400/300?random=12',
          duration: const Duration(minutes: 2, seconds: 30),
        ),
      ],
      feeling: 'متحمس',
      location: 'الشاطئ',
      timestamp: DateTime.now().subtract(const Duration(hours: 7)),
      reactions: [
        PostReaction(
          userId: '1',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        ),
        PostReaction(
          userId: '2',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        ),
        PostReaction(
          userId: '3',
          type: 'wow',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
        PostReaction(
          userId: '4',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
      ],
      comments: [
        PostComment(
          id: _uuid.v4(),
          userId: '2',
          content: 'منظر خلاب! 🌊',
          timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        ),
        PostComment(
          id: _uuid.v4(),
          userId: '3',
          content: 'أتمنى أن أكون هناك! 😍',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        ),
        PostComment(
          id: _uuid.v4(),
          userId: '1',
          content: 'استمتع بوقتك! ☀️',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
      ],
      shareCount: 12,
    ),

    // منشورات المجموعات
    Post(
      id: _uuid.v4(),
      authorId: '2',
      content: 'مرحباً بجميع أعضاء مجموعة محبي التقنية! 💻 شاركونا آخر الأخبار التقنية',
      type: PostType.text,
      groupId: 'group_1',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      reactions: [
        PostReaction(
          userId: '1',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        PostReaction(
          userId: '3',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        ),
      ],
      comments: [
        PostComment(
          id: _uuid.v4(),
          userId: '1',
          content: 'مجموعة رائعة! 👏',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
      ],
      shareCount: 2,
    ),

    Post(
      id: _uuid.v4(),
      authorId: '3',
      content: 'نصائح للطبخ الصحي 🥗 من يريد مشاركة وصفاته المفضلة؟',
      type: PostType.text,
      groupId: 'group_2',
      feeling: 'متحمس',
      activity: 'طبخ',
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      reactions: [
        PostReaction(
          userId: '2',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
        PostReaction(
          userId: '4',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ],
      comments: [
        PostComment(
          id: _uuid.v4(),
          userId: '4',
          content: 'أحب الطبخ الصحي! 😍',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
        PostComment(
          id: _uuid.v4(),
          userId: '2',
          content: 'سأشارك وصفة السلطة الخضراء قريباً',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ],
      shareCount: 5,
    ),
  ];

  // بيانات تجريبية للقصص
  List<Story> get mockStories => [
    Story(
      id: _uuid.v4(),
      authorId: '1',
      content: 'صباح الخير! ☀️',
      type: StoryType.text,
      background: StoryBackground.gradient1,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      expiresAt: DateTime.now().add(const Duration(hours: 22)),
      viewers: [
        StoryViewer(
          userId: '2',
          viewedAt: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        StoryViewer(
          userId: '3',
          viewedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        ),
      ],
    ),
    Story(
      id: _uuid.v4(),
      authorId: '2',
      content: 'في الطريق إلى العمل 🚗',
      type: StoryType.image,
      mediaUrl: 'https://picsum.photos/300/500?random=2',
      timestamp: DateTime.now().subtract(const Duration(hours: 3)),
      expiresAt: DateTime.now().add(const Duration(hours: 21)),
      viewers: [
        StoryViewer(
          userId: '1',
          viewedAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ],
    ),
    Story(
      id: _uuid.v4(),
      authorId: '3',
      content: 'قهوة الصباح ☕',
      type: StoryType.image,
      mediaUrl: 'https://picsum.photos/300/500?random=3',
      timestamp: DateTime.now().subtract(const Duration(hours: 1)),
      expiresAt: DateTime.now().add(const Duration(hours: 23)),
    ),
    Story(
      id: _uuid.v4(),
      authorId: '4',
      content: 'يوم جميل للمشي 🚶‍♀️',
      type: StoryType.text,
      background: StoryBackground.gradient2,
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      expiresAt: DateTime.now().add(const Duration(hours: 20)),
      viewers: [
        StoryViewer(
          userId: '1',
          viewedAt: DateTime.now().subtract(const Duration(hours: 3)),
        ),
        StoryViewer(
          userId: '5',
          viewedAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ],
    ),
  ];

  // بيانات تجريبية للإشعارات
  List<AppNotification> get mockNotifications => [
    AppNotification.like(
      id: _uuid.v4(),
      userId: 'current_user',
      fromUserId: '1',
      fromUserName: 'أحمد محمد',
      postId: 'post1',
      imageUrl: 'https://picsum.photos/50/50?random=1',
    ),
    AppNotification.comment(
      id: _uuid.v4(),
      userId: 'current_user',
      fromUserId: '2',
      fromUserName: 'فاطمة علي',
      postId: 'post2',
      comment: 'منشور رائع!',
      imageUrl: 'https://picsum.photos/50/50?random=2',
    ),
    AppNotification.storyView(
      id: _uuid.v4(),
      userId: 'current_user',
      fromUserId: '3',
      fromUserName: 'محمد حسن',
      storyId: 'story1',
      imageUrl: 'https://picsum.photos/50/50?random=3',
    ),
    AppNotification.share(
      id: _uuid.v4(),
      userId: 'current_user',
      fromUserId: '4',
      fromUserName: 'عائشة أحمد',
      postId: 'post3',
      imageUrl: 'https://picsum.photos/50/50?random=4',
    ),
  ];

  Future<List<Post>> getPosts() async {
    // إرجاع البيانات التجريبية مباشرة لضمان العمل
    return mockPosts;
  }

  Future<List<UserStories>> getStories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storiesJson = prefs.getStringList(_storiesKey);

      List<Story> allStories = [];

      if (storiesJson != null && storiesJson.isNotEmpty) {
        // تحميل القصص المحفوظة
        allStories = storiesJson
            .map((json) => Story.fromJson(jsonDecode(json)))
            .toList();
      }

      // إضافة البيانات التجريبية إذا لم توجد قصص محفوظة
      if (allStories.isEmpty) {
        allStories = List.from(mockStories);
      } else {
        // دمج القصص المحفوظة مع البيانات التجريبية
        final savedStoryIds = allStories.map((s) => s.id).toSet();
        final mockStoriesFiltered = mockStories.where((s) => !savedStoryIds.contains(s.id)).toList();
        allStories.addAll(mockStoriesFiltered);
      }

      // ترتيب القصص حسب التاريخ (الأحدث أولاً)
      allStories.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return _groupStoriesByUser(allStories);
    } catch (e) {
      debugPrint('خطأ في تحميل القصص: $e');
      return _groupStoriesByUser(mockStories);
    }
  }

  Future<List<AppNotification>> getNotifications() async {
    // إرجاع البيانات التجريبية مباشرة لضمان العمل
    return mockNotifications;
  }

  Future<void> createPost(Post post) async {
    final posts = await getPosts();
    posts.insert(0, post);
    await _savePosts(posts);
  }

  Future<void> createStory(Story story) async {
    final prefs = await SharedPreferences.getInstance();
    final storiesJson = prefs.getStringList(_storiesKey) ?? [];

    // إضافة القصة الجديدة في المقدمة
    storiesJson.insert(0, jsonEncode(story.toJson()));
    await prefs.setStringList(_storiesKey, storiesJson);

    // طباعة للتأكد من الحفظ
    debugPrint('تم حفظ القصة: ${story.id} - النوع: ${story.type} - المحتوى: ${story.content}');
    if (story.mediaUrl != null) {
      debugPrint('رابط الوسائط: ${story.mediaUrl}');
    }
  }

  Future<void> addReaction(String postId, PostReaction reaction) async {
    final posts = await getPosts();
    final postIndex = posts.indexWhere((p) => p.id == postId);
    
    if (postIndex != -1) {
      final post = posts[postIndex];
      final reactions = List<PostReaction>.from(post.reactions);
      
      // إزالة التفاعل السابق من نفس المستخدم
      reactions.removeWhere((r) => r.userId == reaction.userId);
      reactions.add(reaction);
      
      posts[postIndex] = post.copyWith(reactions: reactions);
      await _savePosts(posts);
    }
  }

  Future<void> addComment(String postId, PostComment comment) async {
    final posts = await getPosts();
    final postIndex = posts.indexWhere((p) => p.id == postId);
    
    if (postIndex != -1) {
      final post = posts[postIndex];
      final comments = List<PostComment>.from(post.comments);
      comments.add(comment);
      
      posts[postIndex] = post.copyWith(comments: comments);
      await _savePosts(posts);
    }
  }

  Future<void> sharePost(String postId) async {
    final posts = await getPosts();
    final postIndex = posts.indexWhere((p) => p.id == postId);
    
    if (postIndex != -1) {
      final post = posts[postIndex];
      posts[postIndex] = post.copyWith(shareCount: post.shareCount + 1);
      await _savePosts(posts);
    }
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    final notifications = await getNotifications();
    final notificationIndex = notifications.indexWhere((n) => n.id == notificationId);
    
    if (notificationIndex != -1) {
      notifications[notificationIndex] = notifications[notificationIndex]
          .copyWith(status: NotificationStatus.read);
      await _saveNotifications(notifications);
    }
  }

  List<UserStories> _groupStoriesByUser(List<Story> stories) {
    final Map<String, List<Story>> groupedStories = {};
    
    for (final story in stories) {
      if (groupedStories[story.authorId] == null) {
        groupedStories[story.authorId] = [];
      }
      groupedStories[story.authorId]!.add(story);
    }
    
    // هنا يجب جلب معلومات المستخدمين - للتبسيط سنستخدم بيانات وهمية
    return groupedStories.entries.map((entry) {
      final user = User(
        id: entry.key,
        name: _getUserName(entry.key),
        email: '${entry.key}@example.com',
        isOnline: true,
        joinDate: DateTime.now().subtract(const Duration(days: 100)),
      );
      
      return UserStories(
        user: user,
        stories: entry.value,
      );
    }).toList();
  }

  String _getUserName(String userId) {
    switch (userId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      default: return 'مستخدم';
    }
  }

  Future<void> _initializeMockPosts() async {
    await _savePosts(mockPosts);
  }

  Future<void> _initializeMockStories() async {
    final prefs = await SharedPreferences.getInstance();
    final storiesJson = mockStories.map((s) => jsonEncode(s.toJson())).toList();
    await prefs.setStringList(_storiesKey, storiesJson);
  }

  Future<void> _initializeMockNotifications() async {
    await _saveNotifications(mockNotifications);
  }

  Future<void> _savePosts(List<Post> posts) async {
    final prefs = await SharedPreferences.getInstance();
    final postsJson = posts.map((p) => jsonEncode(p.toJson())).toList();
    await prefs.setStringList(_postsKey, postsJson);
  }

  Future<void> _saveNotifications(List<AppNotification> notifications) async {
    final prefs = await SharedPreferences.getInstance();
    final notificationsJson = notifications.map((n) => jsonEncode(n.toJson())).toList();
    await prefs.setStringList(_notificationsKey, notificationsJson);
  }

  // بيانات تجريبية للمجموعات
  List<Group> get mockGroups => [
    Group(
      id: 'group_1',
      name: 'محبي التقنية',
      description: 'مجموعة لمطورين Flutter الناطقين بالعربية لتبادل الخبرات والمساعدة',
      privacy: GroupPrivacy.public,
      createdBy: '1',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      members: [
        GroupMember(
          userId: '1',
          role: GroupMemberRole.admin,
          joinedAt: DateTime.now().subtract(const Duration(days: 30)),
        ),
        GroupMember(
          userId: '2',
          role: GroupMemberRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 25)),
        ),
        GroupMember(
          userId: '3',
          role: GroupMemberRole.moderator,
          joinedAt: DateTime.now().subtract(const Duration(days: 20)),
        ),
        GroupMember(
          userId: '4',
          role: GroupMemberRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 15)),
        ),
      ],
      tags: ['flutter', 'dart', 'mobile', 'programming'],
      postCount: 45,
      lastActivity: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    Group(
      id: 'group_2',
      name: 'عشاق الطبخ العربي',
      description: 'مجموعة لمحبي الطبخ العربي وتبادل الوصفات التقليدية',
      privacy: GroupPrivacy.public,
      createdBy: '2',
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      members: [
        GroupMember(
          userId: '2',
          role: GroupMemberRole.admin,
          joinedAt: DateTime.now().subtract(const Duration(days: 45)),
        ),
        GroupMember(
          userId: '1',
          role: GroupMemberRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 40)),
        ),
        GroupMember(
          userId: '4',
          role: GroupMemberRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 35)),
        ),
        GroupMember(
          userId: '5',
          role: GroupMemberRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 30)),
        ),
      ],
      tags: ['طبخ', 'وصفات', 'طعام', 'عربي'],
      postCount: 78,
      lastActivity: DateTime.now().subtract(const Duration(hours: 5)),
    ),
    Group(
      id: 'group_3',
      name: 'رياضة ولياقة',
      description: 'مجموعة للرياضيين ومحبي اللياقة البدنية',
      privacy: GroupPrivacy.public,
      createdBy: '3',
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      members: [
        GroupMember(
          userId: '3',
          role: GroupMemberRole.admin,
          joinedAt: DateTime.now().subtract(const Duration(days: 20)),
        ),
        GroupMember(
          userId: '1',
          role: GroupMemberRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 18)),
        ),
        GroupMember(
          userId: '5',
          role: GroupMemberRole.member,
          joinedAt: DateTime.now().subtract(const Duration(days: 15)),
        ),
      ],
      tags: ['رياضة', 'لياقة', 'صحة', 'تمارين'],
      postCount: 23,
      lastActivity: DateTime.now().subtract(const Duration(hours: 1)),
    ),
  ];

  // بيانات تجريبية للفيديوهات
  List<VideoPost> get mockVideos => [
    VideoPost(
      id: _uuid.v4(),
      authorId: '1',
      content: 'تعلم Flutter في دقيقة واحدة! 🚀 #flutter #programming',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      videoMetadata: VideoMetadata(
        duration: const Duration(seconds: 45),
        quality: VideoQuality.high,
        orientation: VideoOrientation.portrait,
        width: 720,
        height: 1280,
        aspectRatio: 9/16,
        fileSize: 5 * 1024 * 1024, // 5MB
        format: 'mp4',
      ),
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_720x480_1mb.mp4',
      thumbnailUrl: 'https://picsum.photos/720/1280?random=10',
      viewCount: 1250,
      hashtags: ['flutter', 'programming', 'tutorial'],
      isReels: true,
      reactions: [
        PostReaction(
          userId: '2',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        PostReaction(
          userId: '3',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        ),
      ],
    ),
    VideoPost(
      id: _uuid.v4(),
      authorId: '2',
      content: 'وصفة الكنافة الشامية التقليدية 🍰 #طبخ #حلويات',
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      videoMetadata: VideoMetadata(
        duration: const Duration(seconds: 30),
        quality: VideoQuality.high,
        orientation: VideoOrientation.portrait,
        width: 720,
        height: 1280,
        aspectRatio: 9/16,
        fileSize: 3 * 1024 * 1024, // 3MB
        format: 'mp4',
      ),
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_720x480_1mb.mp4',
      thumbnailUrl: 'https://picsum.photos/720/1280?random=11',
      viewCount: 890,
      hashtags: ['طبخ', 'حلويات', 'كنافة'],
      isReels: true,
      reactions: [
        PostReaction(
          userId: '1',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
      ],
    ),
    VideoPost(
      id: _uuid.v4(),
      authorId: '3',
      content: 'تمارين الصباح للياقة البدنية 💪 #رياضة #لياقة',
      timestamp: DateTime.now().subtract(const Duration(hours: 6)),
      videoMetadata: VideoMetadata(
        duration: const Duration(seconds: 55),
        quality: VideoQuality.high,
        orientation: VideoOrientation.portrait,
        width: 720,
        height: 1280,
        aspectRatio: 9/16,
        fileSize: 7 * 1024 * 1024, // 7MB
        format: 'mp4',
      ),
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_720x480_1mb.mp4',
      thumbnailUrl: 'https://picsum.photos/720/1280?random=12',
      viewCount: 2100,
      hashtags: ['رياضة', 'لياقة', 'تمارين'],
      isReels: true,
      reactions: [
        PostReaction(
          userId: '1',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        ),
        PostReaction(
          userId: '4',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
      ],
    ),
    VideoPost(
      id: _uuid.v4(),
      authorId: '4',
      content: 'رحلة إلى البحر الأحمر - فيديو طويل 🌊',
      timestamp: DateTime.now().subtract(const Duration(hours: 8)),
      videoMetadata: VideoMetadata(
        duration: const Duration(minutes: 3, seconds: 30),
        quality: VideoQuality.hd,
        orientation: VideoOrientation.landscape,
        width: 1920,
        height: 1080,
        aspectRatio: 16/9,
        fileSize: 25 * 1024 * 1024, // 25MB
        format: 'mp4',
      ),
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      thumbnailUrl: 'https://picsum.photos/1920/1080?random=13',
      viewCount: 567,
      hashtags: ['سفر', 'بحر', 'رحلة'],
      isReels: false, // فيديو طويل
      reactions: [
        PostReaction(
          userId: '2',
          type: 'love',
          timestamp: DateTime.now().subtract(const Duration(hours: 7)),
        ),
        PostReaction(
          userId: '5',
          type: 'like',
          timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        ),
      ],
    ),
  ];

  // وظائف المجموعات
  Future<List<Group>> getGroups() async {
    // إرجاع البيانات التجريبية مباشرة لضمان العمل
    return mockGroups;
  }

  Future<void> createGroup(Group group) async {
    final groups = await getGroups();
    groups.insert(0, group);
    await _saveGroups(groups);
  }

  Future<void> joinGroup(String groupId, String userId) async {
    final groups = await getGroups();
    final groupIndex = groups.indexWhere((g) => g.id == groupId);

    if (groupIndex != -1) {
      final group = groups[groupIndex];
      final members = List<GroupMember>.from(group.members);

      if (!members.any((m) => m.userId == userId)) {
        members.add(GroupMember(
          userId: userId,
          role: GroupMemberRole.member,
          joinedAt: DateTime.now(),
        ));

        groups[groupIndex] = group.copyWith(members: members);
        await _saveGroups(groups);
      }
    }
  }

  Future<void> leaveGroup(String groupId, String userId) async {
    final groups = await getGroups();
    final groupIndex = groups.indexWhere((g) => g.id == groupId);

    if (groupIndex != -1) {
      final group = groups[groupIndex];
      final members = List<GroupMember>.from(group.members);

      // إزالة العضو من المجموعة
      members.removeWhere((m) => m.userId == userId);

      groups[groupIndex] = group.copyWith(members: members);
      await _saveGroups(groups);
    }
  }

  // وظائف الفيديوهات
  Future<VideoFeed> getVideoFeed() async {
    final prefs = await SharedPreferences.getInstance();
    final videosJson = prefs.getStringList(_videosKey);

    List<VideoPost> videos;
    if (videosJson == null) {
      await _initializeMockVideos();
      videos = mockVideos;
    } else {
      videos = videosJson
          .map((json) => VideoPost.fromJson(jsonDecode(json)))
          .toList();
    }

    // ترتيب حسب التاريخ
    videos.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return VideoFeed.fromVideos(videos);
  }

  Future<void> incrementVideoViews(String videoId) async {
    final feed = await getVideoFeed();
    final videos = feed.allVideos;
    final videoIndex = videos.indexWhere((v) => v.id == videoId);

    if (videoIndex != -1) {
      final video = videos[videoIndex];
      videos[videoIndex] = video.copyWith(viewCount: video.viewCount + 1);
      await _saveVideos(videos);
    }
  }

  Future<void> _initializeMockGroups() async {
    await _saveGroups(mockGroups);
  }

  Future<void> _initializeMockVideos() async {
    await _saveVideos(mockVideos);
  }

  Future<void> _saveGroups(List<Group> groups) async {
    final prefs = await SharedPreferences.getInstance();
    final groupsJson = groups.map((g) => jsonEncode(g.toJson())).toList();
    await prefs.setStringList(_groupsKey, groupsJson);
  }

  Future<void> _saveVideos(List<VideoPost> videos) async {
    final prefs = await SharedPreferences.getInstance();
    final videosJson = videos.map((v) => jsonEncode(v.toJson())).toList();
    await prefs.setStringList(_videosKey, videosJson);
  }
}
