import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/messenger_provider.dart';
import '../models/user.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import 'messenger_chat_screen.dart';

class MessengerNewChatScreen extends StatefulWidget {
  const MessengerNewChatScreen({super.key});

  @override
  State<MessengerNewChatScreen> createState() => _MessengerNewChatScreenState();
}

class _MessengerNewChatScreenState extends State<MessengerNewChatScreen> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _addContactController = TextEditingController();
  String _searchQuery = '';
  bool _showAddContact = false;

  @override
  void dispose() {
    _searchController.dispose();
    _addContactController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: const Text('محادثة جديدة'),
        actions: [
          IconButton(
            onPressed: () {
              setState(() {
                _showAddContact = !_showAddContact;
              });
            },
            icon: Icon(_showAddContact ? Icons.close : Icons.person_add),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن جهات الاتصال...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // قسم إضافة جهة اتصال جديدة
          if (_showAddContact) ...[
            Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إضافة جهة اتصال جديدة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _addContactController,
                          decoration: const InputDecoration(
                            hintText: 'أدخل البريد الإلكتروني أو رقم الهاتف',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _addNewContact,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('إضافة'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],

          // قائمة جهات الاتصال
          Expanded(
            child: Consumer<MessengerProvider>(
              builder: (context, messengerProvider, child) {
                if (messengerProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final filteredContacts = _getFilteredContacts(messengerProvider.contacts);

                if (filteredContacts.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: filteredContacts.length,
                  itemBuilder: (context, index) {
                    final user = filteredContacts[index];
                    return _buildContactItem(user, messengerProvider);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(User user, MessengerProvider messengerProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Stack(
          children: [
            SmartAvatarWithText(
              user: user,
              radius: 28,
            ),
            
            // نقطة الاتصال
            if (user.isOnline)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
          ],
        ),
        
        title: Text(
          user.name,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user.email),
            if (!user.isOnline && user.lastSeen != null)
              Text(
                'آخر ظهور: ${_formatLastSeen(user.lastSeen!)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
          ],
        ),
        
        trailing: const Icon(
          Icons.chat_bubble_outline,
          color: AppTheme.primaryColor,
        ),
        
        onTap: () => _startChatWithUser(user, messengerProvider),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty 
                ? 'لا توجد جهات اتصال'
                : 'لا توجد نتائج للبحث',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'أضف جهات اتصال لبدء المحادثات'
                : 'جرب البحث بكلمات مختلفة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _showAddContact = true;
                });
              },
              icon: const Icon(Icons.person_add),
              label: const Text('إضافة جهة اتصال'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<User> _getFilteredContacts(List<User> contacts) {
    if (_searchQuery.isEmpty) return contacts;
    
    return contacts.where((user) {
      return user.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             user.email.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
    }
  }

  void _addNewContact() {
    final email = _addContactController.text.trim();
    if (email.isEmpty) return;

    // محاكاة إضافة جهة اتصال جديدة
    final newUser = User(
      id: 'new_user_${DateTime.now().millisecondsSinceEpoch}',
      name: email.split('@').first,
      email: email,
      isOnline: false,
      joinDate: DateTime.now(),
    );

    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.addContact(newUser);

    _addContactController.clear();
    setState(() {
      _showAddContact = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم إضافة ${newUser.name} إلى جهات الاتصال')),
    );
  }

  Future<void> _startChatWithUser(User user, MessengerProvider messengerProvider) async {
    try {
      // إنشاء أو العثور على محادثة موجودة
      final chat = await messengerProvider.createNewChat(user);
      
      if (mounted) {
        // الانتقال إلى شاشة المحادثة
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => MessengerChatScreen(chat: chat),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في بدء المحادثة: $e')),
        );
      }
    }
  }
}
