import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/comments_provider.dart';
import '../providers/auth_provider.dart';
import '../models/comment.dart';
import '../models/user.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';

class CommentInput extends StatefulWidget {
  final String postId;
  final Comment? replyingTo;
  final Comment? editingComment;
  final VoidCallback? onCommentSubmitted;
  final VoidCallback? onCancelReply;
  final VoidCallback? onCancelEdit;

  const CommentInput({
    super.key,
    required this.postId,
    this.replyingTo,
    this.editingComment,
    this.onCommentSubmitted,
    this.onCancelReply,
    this.onCancelEdit,
  });

  @override
  State<CommentInput> createState() => _CommentInputState();
}

class _CommentInputState extends State<CommentInput> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ImagePicker _imagePicker = ImagePicker();
  
  File? _selectedImage;
  File? _selectedVideo;
  bool _isExpanded = false;
  String? _linkUrl;
  Map<String, String?> _linkPreview = {};

  @override
  void initState() {
    super.initState();
    _textController.addListener(_onTextChanged);
    
    // إذا كان في وضع التعديل، املأ النص
    if (widget.editingComment != null) {
      _textController.text = widget.editingComment!.content;
      _isExpanded = true;
      _focusNode.requestFocus();
    }
    
    // إذا كان في وضع الرد، ركز على حقل النص
    if (widget.replyingTo != null) {
      _isExpanded = true;
      _focusNode.requestFocus();
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _textController.text;
    
    // البحث عن روابط في النص
    final urlRegex = RegExp(r'https?://[^\s]+');
    final match = urlRegex.firstMatch(text);
    
    if (match != null) {
      final url = match.group(0)!;
      if (url != _linkUrl) {
        _linkUrl = url;
        _extractLinkPreview(url);
      }
    } else {
      setState(() {
        _linkUrl = null;
        _linkPreview = {};
      });
    }
  }

  Future<void> _extractLinkPreview(String url) async {
    final provider = Provider.of<CommentsProvider>(context, listen: false);
    final preview = await provider.extractLinkInfo(url);
    
    if (mounted && url == _linkUrl) {
      setState(() {
        _linkPreview = preview;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط الرد أو التعديل
          if (widget.replyingTo != null || widget.editingComment != null)
            _buildActionBar(),
          
          // معاينة الرابط
          if (_linkPreview.isNotEmpty) _buildLinkPreview(),
          
          // معاينة الوسائط
          if (_selectedImage != null || _selectedVideo != null)
            _buildMediaPreview(),
          
          // حقل إدخال التعليق
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // صورة المستخدم
              SmartAvatarWithText(
                user: _getCurrentUser(),
                radius: 20,
              ),
              
              const SizedBox(width: 12),
              
              // حقل النص والأزرار
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _isExpanded ? AppTheme.primaryColor : Colors.grey[300]!,
                    ),
                  ),
                  child: Column(
                    children: [
                      // حقل النص
                      TextField(
                        controller: _textController,
                        focusNode: _focusNode,
                        maxLines: _isExpanded ? 4 : 1,
                        decoration: InputDecoration(
                          hintText: widget.replyingTo != null
                              ? 'اكتب رداً...'
                              : widget.editingComment != null
                                  ? 'تعديل التعليق...'
                                  : 'اكتب تعليقاً...',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        onTap: () {
                          setState(() {
                            _isExpanded = true;
                          });
                        },
                        onSubmitted: (_) => _submitComment(),
                      ),
                      
                      // شريط الأدوات
                      if (_isExpanded) _buildToolbar(),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(width: 8),
              
              // زر الإرسال
              Consumer<CommentsProvider>(
                builder: (context, provider, child) {
                  return GestureDetector(
                    onTap: provider.isSubmitting ? null : _submitComment,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _canSubmit() && !provider.isSubmitting
                            ? AppTheme.primaryColor
                            : Colors.grey[400],
                        shape: BoxShape.circle,
                      ),
                      child: provider.isSubmitting
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(
                              Icons.send,
                              color: Colors.white,
                              size: 20,
                            ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionBar() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(
            widget.replyingTo != null ? Icons.reply : Icons.edit,
            size: 16,
            color: Colors.blue[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.replyingTo != null
                  ? 'الرد على ${widget.replyingTo!.author.name}'
                  : 'تعديل التعليق',
              style: TextStyle(
                color: Colors.blue[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              if (widget.replyingTo != null) {
                widget.onCancelReply?.call();
              } else {
                widget.onCancelEdit?.call();
              }
              setState(() {
                _isExpanded = false;
              });
              _focusNode.unfocus();
            },
            child: Icon(
              Icons.close,
              size: 16,
              color: Colors.blue[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLinkPreview() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          if (_linkPreview['image'] != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Image.network(
                _linkPreview['image']!,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 50,
                    height: 50,
                    color: Colors.grey[200],
                    child: const Icon(Icons.link),
                  );
                },
              ),
            ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_linkPreview['title'] != null)
                  Text(
                    _linkPreview['title']!,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (_linkPreview['description'] != null)
                  Text(
                    _linkPreview['description']!,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _linkUrl = null;
                _linkPreview = {};
              });
            },
            child: const Icon(Icons.close, size: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaPreview() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      height: 100,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: _selectedImage != null
                ? Image.file(
                    _selectedImage!,
                    width: double.infinity,
                    height: 100,
                    fit: BoxFit.cover,
                  )
                : Container(
                    width: double.infinity,
                    height: 100,
                    color: Colors.black,
                    child: const Center(
                      child: Icon(
                        Icons.play_circle_fill,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedImage = null;
                  _selectedVideo = null;
                });
              },
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          // زر الصورة
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.image,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
          ),

          // زر الفيديو
          GestureDetector(
            onTap: _pickVideo,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.videocam,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
          ),

          // زر GIF
          GestureDetector(
            onTap: _showGifPicker,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Text(
                'GIF',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),

          // زر الملصقات
          GestureDetector(
            onTap: _showStickerPicker,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.emoji_emotions,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
          ),

          const Spacer(),

          // زر الإلغاء
          if (_isExpanded)
            GestureDetector(
              onTap: _cancelInput,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                child: Text(
                  'إلغاء',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  bool _canSubmit() {
    return _textController.text.trim().isNotEmpty ||
           _selectedImage != null ||
           _selectedVideo != null;
  }

  Future<void> _submitComment() async {
    if (!_canSubmit()) return;

    final provider = Provider.of<CommentsProvider>(context, listen: false);
    final currentUser = _getCurrentUser();

    try {
      if (widget.editingComment != null) {
        // تعديل التعليق
        await provider.editComment(
          postId: widget.postId,
          commentId: widget.editingComment!.id,
          newContent: _textController.text.trim(),
          newMediaFile: _selectedImage ?? _selectedVideo,
        );
      } else {
        // إضافة تعليق جديد
        await provider.addComment(
          postId: widget.postId,
          authorId: currentUser.id,
          author: currentUser,
          content: _textController.text.trim(),
          type: _getCommentType(),
          mediaFile: _selectedImage ?? _selectedVideo,
          linkUrl: _linkUrl,
          parentCommentId: widget.replyingTo?.id,
        );
      }

      // مسح الحقول
      _clearInput();
      widget.onCommentSubmitted?.call();

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في ${widget.editingComment != null ? 'تعديل' : 'إضافة'} التعليق'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  CommentType _getCommentType() {
    if (_selectedImage != null) return CommentType.image;
    if (_selectedVideo != null) return CommentType.video;
    if (_linkUrl != null) return CommentType.link;
    return CommentType.text;
  }

  void _clearInput() {
    _textController.clear();
    setState(() {
      _selectedImage = null;
      _selectedVideo = null;
      _linkUrl = null;
      _linkPreview = {};
      _isExpanded = false;
    });
    _focusNode.unfocus();
  }

  void _cancelInput() {
    _clearInput();
    if (widget.replyingTo != null) {
      widget.onCancelReply?.call();
    }
    if (widget.editingComment != null) {
      widget.onCancelEdit?.call();
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _selectedVideo = null;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في اختيار الصورة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        setState(() {
          _selectedVideo = File(video.path);
          _selectedImage = null;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في اختيار الفيديو'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showGifPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار GIF'),
        content: const Text('ميزة GIF قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showStickerPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار ملصق'),
        content: const Text('ميزة الملصقات قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  User _getCurrentUser() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.currentUser ?? User(
      id: 'current_user',
      name: 'المستخدم الحالي',
      email: '<EMAIL>',
      avatar: null,
      gender: 'male',
      joinDate: DateTime.now(),
    );
  }
}
