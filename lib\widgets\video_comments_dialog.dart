import 'package:flutter/material.dart';
import '../models/video.dart';
import '../screens/comments_screen.dart';

class VideoCommentsDialog extends StatelessWidget {
  final VideoPost video;

  const VideoCommentsDialog({
    super.key,
    required this.video,
  });

  @override
  Widget build(BuildContext context) {
    // استخدام النظام الجديد للتعليقات
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommentsScreen(
          postId: video.id,
          postTitle: 'فيديو ${video.content.isNotEmpty ? video.content : "بدون عنوان"}',
        ),
      ),
    );

    // إرجاع container فارغ لأن التنقل سيحدث
    return const SizedBox.shrink();
  }
}


