import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'dart:io';
import '../models/chat.dart';
import '../models/message.dart';
import '../providers/chat_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import '../widgets/advanced_message_bubble.dart';
import '../widgets/advanced_message_input.dart';

class AdvancedChatScreen extends StatefulWidget {
  final Chat chat;

  const AdvancedChatScreen({super.key, required this.chat});

  @override
  State<AdvancedChatScreen> createState() => _AdvancedChatScreenState();
}

class _AdvancedChatScreenState extends State<AdvancedChatScreen>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late AnimationController _typingAnimationController;
  late Animation<double> _typingAnimation;
  bool _showScrollToBottom = false;

  @override
  void initState() {
    super.initState();
    
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _typingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingAnimationController,
      curve: Curves.easeInOut,
    ));

    // تحميل رسائل المحادثة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final chatProvider = Provider.of<ChatProvider>(context, listen: false);
      chatProvider.loadChatMessages(widget.chat.id);
      chatProvider.markAsRead(widget.chat.id);
    });

    // مراقبة التمرير
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _typingAnimationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final showButton = _scrollController.offset > 200;
    if (showButton != _showScrollToBottom) {
      setState(() {
        _showScrollToBottom = showButton;
      });
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // منطقة الرسائل
          Expanded(
            child: Stack(
              children: [
                _buildMessagesArea(),
                
                // زر العودة للأسفل
                if (_showScrollToBottom)
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: FloatingActionButton.small(
                      onPressed: _scrollToBottom,
                      backgroundColor: AppTheme.primaryColor,
                      child: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
                    ),
                  ),
              ],
            ),
          ),
          
          // شريط إدخال الرسائل
          _buildMessageInput(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 2,
      title: Row(
        children: [
          // صورة المستخدم
          GestureDetector(
            onTap: _showUserProfile,
            child: SmartAvatarWithText(
              user: widget.chat.otherUser,
              radius: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // معلومات المستخدم
          Expanded(
            child: GestureDetector(
              onTap: _showUserProfile,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.chat.otherUser.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Consumer<ChatProvider>(
                    builder: (context, chatProvider, child) {
                      if (chatProvider.userTypingStatus[widget.chat.id] == true) {
                        return AnimatedBuilder(
                          animation: _typingAnimation,
                          builder: (context, child) {
                            _typingAnimationController.repeat(reverse: true);
                            return Text(
                              'يكتب...',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white.withValues(alpha: 0.8),
                                fontStyle: FontStyle.italic,
                              ),
                            );
                          },
                        );
                      }
                      
                      return Text(
                        widget.chat.otherUser.isOnline 
                            ? 'متصل الآن'
                            : widget.chat.otherUser.lastSeen != null
                                ? 'آخر ظهور ${timeago.format(widget.chat.otherUser.lastSeen!, locale: 'ar')}'
                                : 'غير متصل',
                        style: TextStyle(
                          fontSize: 12,
                          color: widget.chat.otherUser.isOnline 
                              ? Colors.greenAccent
                              : Colors.white.withValues(alpha: 0.7),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      actions: [
        // زر المكالمة الصوتية
        IconButton(
          onPressed: _makeVoiceCall,
          icon: const Icon(Icons.call, color: Colors.white),
          tooltip: 'مكالمة صوتية',
        ),
        
        // زر مكالمة الفيديو
        IconButton(
          onPressed: _makeVideoCall,
          icon: const Icon(Icons.videocam, color: Colors.white),
          tooltip: 'مكالمة فيديو',
        ),
        
        // قائمة الخيارات
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'search',
              child: Row(
                children: [
                  Icon(Icons.search),
                  SizedBox(width: 8),
                  Text('البحث في المحادثة'),
                ],
              ),
            ),
            PopupMenuItem(
              value: widget.chat.isMuted ? 'unmute' : 'mute',
              child: Row(
                children: [
                  Icon(widget.chat.isMuted ? Icons.volume_up : Icons.volume_off),
                  const SizedBox(width: 8),
                  Text(widget.chat.isMuted ? 'إلغاء الكتم' : 'كتم الإشعارات'),
                ],
              ),
            ),
            PopupMenuItem(
              value: widget.chat.isPinned ? 'unpin' : 'pin',
              child: Row(
                children: [
                  Icon(widget.chat.isPinned ? Icons.push_pin : Icons.push_pin_outlined),
                  const SizedBox(width: 8),
                  Text(widget.chat.isPinned ? 'إلغاء التثبيت' : 'تثبيت المحادثة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'clear',
              child: Row(
                children: [
                  Icon(Icons.delete_sweep, color: Colors.red),
                  SizedBox(width: 8),
                  Text('مسح المحادثة', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMessagesArea() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        if (chatProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (chatProvider.errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  chatProvider.errorMessage!,
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => chatProvider.loadChatMessages(widget.chat.id),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        final messages = chatProvider.currentChatMessages;

        if (messages.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SmartAvatarWithText(
                  user: widget.chat.otherUser,
                  radius: 40,
                ),
                const SizedBox(height: 16),
                Text(
                  'ابدأ محادثة مع ${widget.chat.otherUser.name}',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'أرسل أول رسالة لبدء المحادثة',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        // التمرير إلى الأسفل عند تحديث الرسائل
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });

        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: messages.length,
          itemBuilder: (context, index) {
            final message = messages[index];
            final isMe = message.senderId == 'current_user';
            
            return AdvancedMessageBubble(
              message: message,
              isMe: isMe,
              otherUser: isMe ? null : widget.chat.otherUser,
              onReply: () => _replyToMessage(message),
              onDelete: () => _deleteMessage(message),
              onForward: () => _forwardMessage(message),
              showAvatar: !isMe,
              showTimestamp: true,
            );
          },
        );
      },
    );
  }

  Widget _buildMessageInput() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        return AdvancedMessageInput(
          onSendMessage: (content, type) {
            if (chatProvider.replyToMessage != null) {
              chatProvider.sendReplyMessage(widget.chat.id, content);
            } else {
              chatProvider.sendMessage(widget.chat.id, content);
            }
          },
          onSendMedia: (file, type) {
            chatProvider.sendMediaMessage(widget.chat.id, file, type);
          },
          onSendLocation: (location) {
            chatProvider.sendMessage(widget.chat.id, 'تم مشاركة الموقع: $location');
          },
          onStartRecording: () {
            chatProvider.startRecording();
          },
          onStopRecording: () {
            chatProvider.stopRecording();
            // محاكاة إرسال رسالة صوتية
            chatProvider.sendAudioMessage(
              widget.chat.id,
              File('mock_audio.m4a'),
              const Duration(seconds: 30),
            );
          },
          isRecording: chatProvider.isRecording,
          replyToMessage: chatProvider.replyToMessage,
          onCancelReply: () {
            chatProvider.cancelReply();
          },
        );
      },
    );
  }

  // دوال الإجراءات
  void _showUserProfile() {
    // TODO: فتح ملف المستخدم الشخصي
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض ملف ${widget.chat.otherUser.name}')),
    );
  }

  void _makeVoiceCall() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('مكالمة صوتية مع ${widget.chat.otherUser.name}')),
    );
  }

  void _makeVideoCall() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('مكالمة فيديو مع ${widget.chat.otherUser.name}')),
    );
  }

  void _handleMenuAction(String action) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    
    switch (action) {
      case 'search':
        _showSearchDialog();
        break;
      case 'mute':
        chatProvider.muteChat(widget.chat.id);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم كتم الإشعارات')),
        );
        break;
      case 'unmute':
        chatProvider.unmuteChat(widget.chat.id);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إلغاء كتم الإشعارات')),
        );
        break;
      case 'pin':
        chatProvider.pinChat(widget.chat.id);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تثبيت المحادثة')),
        );
        break;
      case 'unpin':
        chatProvider.unpinChat(widget.chat.id);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إلغاء تثبيت المحادثة')),
        );
        break;
      case 'clear':
        _showClearChatDialog();
        break;
    }
  }

  void _replyToMessage(Message message) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.setReplyToMessage(message);
  }

  void _deleteMessage(Message message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الرسالة'),
        content: const Text('هل تريد حذف هذه الرسالة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final chatProvider = Provider.of<ChatProvider>(context, listen: false);
              chatProvider.deleteMessage(message.id);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _forwardMessage(Message message) {
    // TODO: تنفيذ إعادة التوجيه
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعادة توجيه الرسالة')),
    );
  }

  void _showSearchDialog() {
    // TODO: تنفيذ البحث في المحادثة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('البحث في المحادثة')),
    );
  }

  void _showClearChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح المحادثة'),
        content: const Text('هل تريد مسح جميع الرسائل في هذه المحادثة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: تنفيذ مسح المحادثة
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح المحادثة')),
              );
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
