import 'package:flutter/material.dart';
import 'dart:math' as math;

class StoryEditorTools extends StatefulWidget {
  final String initialText;
  final Function(String) onTextChanged;
  final Function(double) onFontSizeChanged;
  final Function(Color) onColorChanged;
  final Function(TextAlign) onAlignmentChanged;
  final VoidCallback? onDrawingMode;
  final VoidCallback? onStickersMode;
  final VoidCallback? onTextMode;

  const StoryEditorTools({
    super.key,
    required this.initialText,
    required this.onTextChanged,
    required this.onFontSizeChanged,
    required this.onColorChanged,
    required this.onAlignmentChanged,
    this.onDrawingMode,
    this.onStickersMode,
    this.onTextMode,
  });

  @override
  State<StoryEditorTools> createState() => _StoryEditorToolsState();
}

class _StoryEditorToolsState extends State<StoryEditorTools> {
  late TextEditingController _textController;
  double _fontSize = 24.0;
  Color _selectedColor = Colors.white;
  TextAlign _textAlign = TextAlign.center;
  bool _showTextEditor = false;
  bool _showColorPicker = false;
  bool _showFontSizeSlider = false;

  final List<Color> _colors = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.cyan,
    Colors.lime,
    Colors.indigo,
  ];

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.initialText);
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط الأدوات العلوي
        _buildTopToolbar(),
        
        // محرر النص (إذا كان مفعلاً)
        if (_showTextEditor) _buildTextEditor(),
        
        // منتقي الألوان (إذا كان مفعلاً)
        if (_showColorPicker) _buildColorPicker(),
        
        // شريط تمرير حجم الخط (إذا كان مفعلاً)
        if (_showFontSizeSlider) _buildFontSizeSlider(),
      ],
    );
  }

  Widget _buildTopToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أداة النص
          _buildToolButton(
            icon: Icons.text_fields,
            label: 'نص',
            onTap: () {
              setState(() {
                _showTextEditor = !_showTextEditor;
                _showColorPicker = false;
                _showFontSizeSlider = false;
              });
              widget.onTextMode?.call();
            },
          ),
          
          const SizedBox(width: 12),
          
          // أداة الرسم
          _buildToolButton(
            icon: Icons.brush,
            label: 'رسم',
            onTap: () {
              widget.onDrawingMode?.call();
            },
          ),
          
          const SizedBox(width: 12),
          
          // أداة الملصقات
          _buildToolButton(
            icon: Icons.emoji_emotions,
            label: 'ملصقات',
            onTap: () {
              widget.onStickersMode?.call();
            },
          ),
          
          const SizedBox(width: 12),
          
          // أداة الألوان
          _buildToolButton(
            icon: Icons.palette,
            label: 'ألوان',
            onTap: () {
              setState(() {
                _showColorPicker = !_showColorPicker;
                _showTextEditor = false;
                _showFontSizeSlider = false;
              });
            },
          ),
          
          const SizedBox(width: 12),
          
          // أداة حجم الخط
          _buildToolButton(
            icon: Icons.format_size,
            label: 'حجم',
            onTap: () {
              setState(() {
                _showFontSizeSlider = !_showFontSizeSlider;
                _showTextEditor = false;
                _showColorPicker = false;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextEditor() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // حقل النص
          TextField(
            controller: _textController,
            style: TextStyle(
              color: _selectedColor,
              fontSize: _fontSize,
            ),
            textAlign: _textAlign,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: 'اكتب نصك هنا...',
              hintStyle: TextStyle(color: Colors.grey),
              border: InputBorder.none,
            ),
            onChanged: widget.onTextChanged,
          ),
          
          const SizedBox(height: 12),
          
          // أزرار المحاذاة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAlignmentButton(Icons.format_align_left, TextAlign.left),
              _buildAlignmentButton(Icons.format_align_center, TextAlign.center),
              _buildAlignmentButton(Icons.format_align_right, TextAlign.right),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAlignmentButton(IconData icon, TextAlign alignment) {
    final isSelected = _textAlign == alignment;
    return GestureDetector(
      onTap: () {
        setState(() {
          _textAlign = alignment;
        });
        widget.onAlignmentChanged(alignment);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildColorPicker() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Text(
            'اختر لون النص',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _colors.map((color) => _buildColorOption(color)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildColorOption(Color color) {
    final isSelected = _selectedColor == color;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedColor = color;
        });
        widget.onColorChanged(color);
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? Colors.white : Colors.grey,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: isSelected
            ? const Icon(
                Icons.check,
                color: Colors.black,
                size: 20,
              )
            : null,
      ),
    );
  }

  Widget _buildFontSizeSlider() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Text(
            'حجم الخط',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text(
                'صغير',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
              Expanded(
                child: Slider(
                  value: _fontSize,
                  min: 12.0,
                  max: 48.0,
                  divisions: 18,
                  activeColor: Colors.blue,
                  inactiveColor: Colors.grey,
                  onChanged: (value) {
                    setState(() {
                      _fontSize = value;
                    });
                    widget.onFontSizeChanged(value);
                  },
                ),
              ),
              const Text(
                'كبير',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
          Text(
            'الحجم: ${_fontSize.round()}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}

// Widget للملصقات
class StickersPanel extends StatelessWidget {
  final Function(String) onStickerSelected;

  const StickersPanel({
    super.key,
    required this.onStickerSelected,
  });

  final List<String> _stickers = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
    '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
    '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
    '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍',
    '💯', '💫', '⭐', '🌟', '✨', '⚡', '🔥', '💥',
    '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙',
    '🎉', '🎊', '🎈', '🎁', '🏆', '🥇', '🎯', '🎪',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Text(
            'اختر ملصق',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 8,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: _stickers.length,
              itemBuilder: (context, index) {
                final sticker = _stickers[index];
                return GestureDetector(
                  onTap: () => onStickerSelected(sticker),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        sticker,
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
