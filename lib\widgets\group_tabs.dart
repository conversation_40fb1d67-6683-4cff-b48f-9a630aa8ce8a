import 'package:flutter/material.dart';

class GroupTabs extends StatelessWidget {
  final TabController tabController;

  const GroupTabs({
    super.key,
    required this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return TabBar(
      controller: tabController,
      isScrollable: true,
      indicatorColor: Theme.of(context).primaryColor,
      labelColor: Theme.of(context).primaryColor,
      unselectedLabelColor: Colors.grey,
      tabs: const [
        Tab(text: 'المناقشات'),
        Tab(text: 'حول'),
        Tab(text: 'الأعضاء'),
        Tab(text: 'الصور'),
        Tab(text: 'الأحداث'),
      ],
    );
  }
}
