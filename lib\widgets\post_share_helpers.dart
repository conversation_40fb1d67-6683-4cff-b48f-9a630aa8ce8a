import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../providers/social_provider.dart';
import 'repost_dialog.dart';

class PostShareHelpers {
  static Widget buildShareOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: Colors.white, size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 14,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.grey,
          size: 16,
        ),
      ),
    );
  }

  static void performExternalShare(BuildContext context, Post post) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'مشاركة خارجية',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            
            // شبكة التطبيقات الخارجية
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 4,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: [
                _buildExternalAppOption(
                  icon: Icons.facebook,
                  label: 'Facebook',
                  color: const Color(0xFF1877F2),
                  onTap: () => _shareToFacebook(context, post),
                ),
                _buildExternalAppOption(
                  icon: Icons.camera_alt,
                  label: 'Instagram',
                  color: const Color(0xFFE4405F),
                  onTap: () => _shareToInstagram(context, post),
                ),
                _buildExternalAppOption(
                  icon: Icons.alternate_email,
                  label: 'Twitter',
                  color: const Color(0xFF1DA1F2),
                  onTap: () => _shareToTwitter(context, post),
                ),
                _buildExternalAppOption(
                  icon: Icons.chat,
                  label: 'WhatsApp',
                  color: const Color(0xFF25D366),
                  onTap: () => _shareToWhatsApp(context, post),
                ),
                _buildExternalAppOption(
                  icon: Icons.telegram,
                  label: 'Telegram',
                  color: const Color(0xFF0088CC),
                  onTap: () => _shareToTelegram(context, post),
                ),
                _buildExternalAppOption(
                  icon: Icons.email,
                  label: 'Email',
                  color: Colors.orange,
                  onTap: () => _shareViaEmail(context, post),
                ),
                _buildExternalAppOption(
                  icon: Icons.sms,
                  label: 'SMS',
                  color: Colors.green,
                  onTap: () => _shareViaSMS(context, post),
                ),
                _buildExternalAppOption(
                  icon: Icons.more_horiz,
                  label: 'المزيد',
                  color: Colors.grey,
                  onTap: () => _shareToOtherApps(context, post),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildExternalAppOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Colors.white, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  static void shareAsNewPost(BuildContext context, Post post) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    socialProvider.sharePostAsNewPost(post.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.post_add, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('تم مشاركة المنشور كمنشور جديد! 📝'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  static void sendInMessage(BuildContext context, Post post) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'إرسال في رسالة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            
            // قائمة الأصدقاء
            ListView.builder(
              shrinkWrap: true,
              itemCount: 5,
              itemBuilder: (context, index) {
                final friends = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'عمر خالد'];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue,
                    child: Text(
                      friends[index][0],
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(
                    friends[index],
                    style: const TextStyle(color: Colors.white),
                  ),
                  subtitle: const Text(
                    'متصل الآن',
                    style: TextStyle(color: Colors.green),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _sendPostToFriend(context, post, friends[index]);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  static void shareInGroup(BuildContext context, Post post) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'مشاركة في مجموعة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            
            // قائمة المجموعات
            ListView.builder(
              shrinkWrap: true,
              itemCount: 4,
              itemBuilder: (context, index) {
                final groups = ['مجموعة الأصدقاء', 'مجموعة العمل', 'مجموعة العائلة', 'مجموعة الهوايات'];
                final groupIcons = [Icons.group, Icons.work, Icons.family_restroom, Icons.sports_soccer];
                final groupColors = [Colors.blue, Colors.green, Colors.purple, Colors.orange];
                
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: groupColors[index],
                    child: Icon(
                      groupIcons[index],
                      color: Colors.white,
                    ),
                  ),
                  title: Text(
                    groups[index],
                    style: const TextStyle(color: Colors.white),
                  ),
                  subtitle: Text(
                    '${(index + 1) * 15} عضو',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _sharePostInGroup(context, post, groups[index]);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  static void repostInstantly(BuildContext context, Post post) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    socialProvider.repostPost(post.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.repeat, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('تم إعادة نشر المنشور فوراً! 🔄'),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  static void repostWithComment(BuildContext context, Post post) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => RepostDialog(originalPost: post),
    );
  }

  // دوال المشاركة الخارجية
  static void _shareToFacebook(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Facebook للمشاركة! 📘'),
        backgroundColor: Color(0xFF1877F2),
      ),
    );
  }

  static void _shareToInstagram(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Instagram للمشاركة! 📷'),
        backgroundColor: Color(0xFFE4405F),
      ),
    );
  }

  static void _shareToTwitter(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Twitter للمشاركة! 🐦'),
        backgroundColor: Color(0xFF1DA1F2),
      ),
    );
  }

  static void _shareToWhatsApp(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح WhatsApp للمشاركة! 💬'),
        backgroundColor: Color(0xFF25D366),
      ),
    );
  }

  static void _shareToTelegram(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Telegram للمشاركة! ✈️'),
        backgroundColor: Color(0xFF0088CC),
      ),
    );
  }

  static void _shareViaEmail(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح البريد الإلكتروني للمشاركة! 📧'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  static void _shareViaSMS(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح الرسائل النصية للمشاركة! 📱'),
        backgroundColor: Colors.green,
      ),
    );
  }

  static void _shareToOtherApps(BuildContext context, Post post) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح قائمة التطبيقات الأخرى! 📤'),
        backgroundColor: Colors.grey,
      ),
    );
  }

  static void _sendPostToFriend(BuildContext context, Post post, String friendName) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    socialProvider.sendPostInMessage(post.id, friendName);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.message, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('تم إرسال المنشور إلى $friendName! 💬'),
          ],
        ),
        backgroundColor: Colors.purple,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  static void _sharePostInGroup(BuildContext context, Post post, String groupName) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    socialProvider.sharePostInGroup(post.id, groupName);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.group, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('تم مشاركة المنشور في $groupName! 👥'),
          ],
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}


