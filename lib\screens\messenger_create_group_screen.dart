import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/messenger_provider.dart';
import '../models/user.dart';
import '../models/group_chat.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';

class MessengerCreateGroupScreen extends StatefulWidget {
  const MessengerCreateGroupScreen({super.key});

  @override
  State<MessengerCreateGroupScreen> createState() => _MessengerCreateGroupScreenState();
}

class _MessengerCreateGroupScreenState extends State<MessengerCreateGroupScreen> {
  final TextEditingController _groupNameController = TextEditingController();
  final TextEditingController _groupDescriptionController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  
  final List<User> _selectedUsers = [];
  String _searchQuery = '';
  File? _groupImage;
  bool _isCreating = false;

  @override
  void dispose() {
    _groupNameController.dispose();
    _groupDescriptionController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: const Text('إنشاء مجموعة'),
        actions: [
          TextButton(
            onPressed: _canCreateGroup() ? _createGroup : null,
            child: Text(
              'إنشاء',
              style: TextStyle(
                color: _canCreateGroup() ? Colors.white : Colors.white54,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // معلومات المجموعة
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // صورة المجموعة
                GestureDetector(
                  onTap: _pickGroupImage,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey[200],
                      border: Border.all(color: AppTheme.primaryColor, width: 2),
                    ),
                    child: _groupImage != null
                        ? ClipOval(
                            child: Image.file(
                              _groupImage!,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Icon(
                            Icons.camera_alt,
                            size: 30,
                            color: Colors.grey[600],
                          ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // اسم المجموعة
                TextField(
                  controller: _groupNameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المجموعة',
                    hintText: 'أدخل اسم المجموعة',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => setState(() {}),
                ),
                
                const SizedBox(height: 16),
                
                // وصف المجموعة
                TextField(
                  controller: _groupDescriptionController,
                  decoration: const InputDecoration(
                    labelText: 'وصف المجموعة (اختياري)',
                    hintText: 'أدخل وصف المجموعة',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 8),
          
          // الأعضاء المختارون
          if (_selectedUsers.isNotEmpty) ...[
            Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الأعضاء المختارون (${_selectedUsers.length})',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 60,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _selectedUsers.length,
                      itemBuilder: (context, index) {
                        final user = _selectedUsers[index];
                        return Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: Column(
                            children: [
                              Stack(
                                children: [
                                  SmartAvatarWithText(
                                    user: user,
                                    radius: 20,
                                  ),
                                  Positioned(
                                    top: -5,
                                    right: -5,
                                    child: GestureDetector(
                                      onTap: () => _removeUser(user),
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          size: 14,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              SizedBox(
                                width: 50,
                                child: Text(
                                  user.name.split(' ').first,
                                  style: const TextStyle(fontSize: 10),
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
          
          // شريط البحث
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن جهات الاتصال...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // قائمة جهات الاتصال
          Expanded(
            child: Consumer<MessengerProvider>(
              builder: (context, messengerProvider, child) {
                final filteredContacts = _getFilteredContacts(messengerProvider.contacts);
                
                if (filteredContacts.isEmpty) {
                  return _buildEmptyState();
                }
                
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: filteredContacts.length,
                  itemBuilder: (context, index) {
                    final user = filteredContacts[index];
                    final isSelected = _selectedUsers.contains(user);
                    
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        leading: SmartAvatarWithText(
                          user: user,
                          radius: 25,
                        ),
                        title: Text(
                          user.name,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        subtitle: Text(user.email),
                        trailing: Checkbox(
                          value: isSelected,
                          onChanged: (value) {
                            if (value == true) {
                              _addUser(user);
                            } else {
                              _removeUser(user);
                            }
                          },
                          activeColor: AppTheme.primaryColor,
                        ),
                        onTap: () {
                          if (isSelected) {
                            _removeUser(user);
                          } else {
                            _addUser(user);
                          }
                        },
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty 
                ? 'لا توجد جهات اتصال'
                : 'لا توجد نتائج للبحث',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'أضف جهات اتصال لإنشاء مجموعة'
                : 'جرب البحث بكلمات مختلفة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<User> _getFilteredContacts(List<User> contacts) {
    if (_searchQuery.isEmpty) return contacts;
    
    return contacts.where((user) {
      return user.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             user.email.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  void _addUser(User user) {
    if (!_selectedUsers.contains(user)) {
      setState(() {
        _selectedUsers.add(user);
      });
    }
  }

  void _removeUser(User user) {
    setState(() {
      _selectedUsers.remove(user);
    });
  }

  bool _canCreateGroup() {
    return _groupNameController.text.trim().isNotEmpty && 
           _selectedUsers.isNotEmpty && 
           !_isCreating;
  }

  Future<void> _pickGroupImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    
    if (image != null) {
      setState(() {
        _groupImage = File(image.path);
      });
    }
  }

  Future<void> _createGroup() async {
    if (!_canCreateGroup()) return;

    setState(() {
      _isCreating = true;
    });

    try {
      // إنشاء أعضاء المجموعة
      final members = _selectedUsers.map((user) => GroupChatMember(
        userId: user.id,
        name: user.name,
        role: GroupChatRole.member,
        joinedAt: DateTime.now(),
      )).toList();

      // إضافة المنشئ كمدير
      members.insert(0, GroupChatMember(
        userId: 'current_user',
        name: 'أنت',
        role: GroupChatRole.admin,
        joinedAt: DateTime.now(),
      ));

      // إنشاء المجموعة
      final group = GroupChat(
        id: 'group_${DateTime.now().millisecondsSinceEpoch}',
        name: _groupNameController.text.trim(),
        description: _groupDescriptionController.text.trim().isEmpty 
            ? null 
            : _groupDescriptionController.text.trim(),
        avatar: _groupImage?.path,
        members: members,
        createdBy: 'current_user',
        createdAt: DateTime.now(),
        lastActivity: DateTime.now(),
      );

      // حفظ المجموعة
      final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
      await messengerProvider.createGroup(group);

      // العودة للشاشة السابقة
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم إنشاء مجموعة "${group.name}" بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إنشاء المجموعة: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }
}
