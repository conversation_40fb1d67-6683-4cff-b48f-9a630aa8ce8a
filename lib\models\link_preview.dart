class LinkPreview {
  final String url;
  final String title;
  final String description;
  final String? imageUrl;
  final String? siteName;
  final String? favicon;
  final DateTime createdAt;

  LinkPreview({
    required this.url,
    required this.title,
    required this.description,
    this.imageUrl,
    this.siteName,
    this.favicon,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'siteName': siteName,
      'favicon': favicon,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory LinkPreview.fromJson(Map<String, dynamic> json) {
    return LinkPreview(
      url: json['url'],
      title: json['title'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      siteName: json['siteName'],
      favicon: json['favicon'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  // بيانات تجريبية لمعاينة الروابط
  static LinkPreview generatePreview(String url) {
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      return LinkPreview(
        url: url,
        title: 'فيديو رائع على YouTube',
        description: 'شاهد هذا الفيديو المذهل الذي يحتوي على محتوى ممتع ومفيد',
        imageUrl: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        siteName: 'YouTube',
        favicon: 'https://www.youtube.com/favicon.ico',
        createdAt: DateTime.now(),
      );
    } else if (url.contains('facebook.com')) {
      return LinkPreview(
        url: url,
        title: 'منشور على Facebook',
        description: 'اكتشف المحتوى الجديد والمثير على Facebook',
        imageUrl: 'https://www.facebook.com/images/fb_icon_325x325.png',
        siteName: 'Facebook',
        favicon: 'https://www.facebook.com/favicon.ico',
        createdAt: DateTime.now(),
      );
    } else if (url.contains('instagram.com')) {
      return LinkPreview(
        url: url,
        title: 'صورة على Instagram',
        description: 'شاهد هذه الصورة الجميلة على Instagram',
        imageUrl: 'https://www.instagram.com/static/images/ico/favicon-192.png/68d99ba29cc8.png',
        siteName: 'Instagram',
        favicon: 'https://www.instagram.com/favicon.ico',
        createdAt: DateTime.now(),
      );
    } else if (url.contains('twitter.com') || url.contains('x.com')) {
      return LinkPreview(
        url: url,
        title: 'تغريدة على Twitter',
        description: 'اقرأ هذه التغريدة المثيرة للاهتمام',
        imageUrl: 'https://abs.twimg.com/icons/apple-touch-icon-192x192.png',
        siteName: 'Twitter',
        favicon: 'https://twitter.com/favicon.ico',
        createdAt: DateTime.now(),
      );
    } else if (url.contains('linkedin.com')) {
      return LinkPreview(
        url: url,
        title: 'منشور على LinkedIn',
        description: 'اكتشف المحتوى المهني على LinkedIn',
        imageUrl: 'https://static.licdn.com/sc/h/al2o9zrvru7aqj8e1x2rzsrca',
        siteName: 'LinkedIn',
        favicon: 'https://www.linkedin.com/favicon.ico',
        createdAt: DateTime.now(),
      );
    } else if (url.contains('github.com')) {
      return LinkPreview(
        url: url,
        title: 'مشروع على GitHub',
        description: 'استكشف هذا المشروع البرمجي المثير على GitHub',
        imageUrl: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png',
        siteName: 'GitHub',
        favicon: 'https://github.com/favicon.ico',
        createdAt: DateTime.now(),
      );
    } else {
      return LinkPreview(
        url: url,
        title: 'رابط ويب',
        description: 'انقر لزيارة هذا الموقع الإلكتروني',
        imageUrl: null,
        siteName: _extractDomain(url),
        favicon: null,
        createdAt: DateTime.now(),
      );
    }
  }

  static String _extractDomain(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return 'موقع ويب';
    }
  }
}
