import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../providers/social_provider.dart';
import '../theme/app_theme.dart';

class InviteFriendsDialog extends StatefulWidget {
  final Group group;

  const InviteFriendsDialog({
    super.key,
    required this.group,
  });

  @override
  State<InviteFriendsDialog> createState() => _InviteFriendsDialogState();
}

class _InviteFriendsDialogState extends State<InviteFriendsDialog> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  final Set<String> _selectedFriends = {};
  bool _isInviting = false;

  // قائمة الأصدقاء التجريبية
  final List<Friend> _allFriends = [
    Friend(id: '1', name: 'أحمد محمد', avatar: null, isOnline: true),
    Friend(id: '2', name: 'فاطمة علي', avatar: null, isOnline: false),
    Friend(id: '3', name: 'محمد حسن', avatar: null, isOnline: true),
    Friend(id: '4', name: 'عائشة أحمد', avatar: null, isOnline: false),
    Friend(id: '5', name: 'عمر خالد', avatar: null, isOnline: true),
    Friend(id: '6', name: 'زينب سالم', avatar: null, isOnline: false),
    Friend(id: '7', name: 'يوسف إبراهيم', avatar: null, isOnline: true),
    Friend(id: '8', name: 'مريم حسين', avatar: null, isOnline: false),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredFriends = _getFilteredFriends();

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          children: [
            // رأس الحوار
            _buildHeader(),
            
            // شريط البحث
            _buildSearchBar(),
            
            // قائمة الأصدقاء
            Expanded(
              child: filteredFriends.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: filteredFriends.length,
                      itemBuilder: (context, index) {
                        final friend = filteredFriends[index];
                        return _buildFriendItem(friend);
                      },
                    ),
            ),
            
            // شريط الإجراءات
            _buildActionBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Icon(Icons.group_add, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'دعوة أصدقاء',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'إلى مجموعة ${widget.group.name}',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث عن صديق...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[100],
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildFriendItem(Friend friend) {
    final isSelected = _selectedFriends.contains(friend.id);
    final isAlreadyMember = widget.group.isUserMember(friend.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected ? AppTheme.primaryColor.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(12),
        border: isSelected 
            ? Border.all(color: AppTheme.primaryColor, width: 2)
            : null,
      ),
      child: ListTile(
        leading: Stack(
          children: [
            CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Text(
                friend.name[0].toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (friend.isOnline)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
          ],
        ),
        
        title: Text(
          friend.name,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isAlreadyMember ? Colors.grey : null,
          ),
        ),
        
        subtitle: Row(
          children: [
            if (friend.isOnline)
              const Text(
                'متصل الآن',
                style: TextStyle(color: Colors.green, fontSize: 12),
              )
            else
              const Text(
                'غير متصل',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
            if (isAlreadyMember) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'عضو بالفعل',
                  style: TextStyle(fontSize: 10, color: Colors.grey),
                ),
              ),
            ],
          ],
        ),
        
        trailing: isAlreadyMember
            ? Icon(Icons.check_circle, color: Colors.grey)
            : Checkbox(
                value: isSelected,
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      _selectedFriends.add(friend.id);
                    } else {
                      _selectedFriends.remove(friend.id);
                    }
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),
        
        onTap: isAlreadyMember ? null : () {
          setState(() {
            if (_selectedFriends.contains(friend.id)) {
              _selectedFriends.remove(friend.id);
            } else {
              _selectedFriends.add(friend.id);
            }
          });
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد أصدقاء',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أصدقاء بالمعايير المحددة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          // عدد المحددين
          if (_selectedFriends.isNotEmpty)
            Expanded(
              child: Text(
                'تم اختيار ${_selectedFriends.length} أصدقاء',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          else
            const Expanded(
              child: Text(
                'اختر الأصدقاء للدعوة',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          
          const SizedBox(width: 12),
          
          // زر الإلغاء
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          
          const SizedBox(width: 8),
          
          // زر الدعوة
          ElevatedButton(
            onPressed: _selectedFriends.isEmpty || _isInviting 
                ? null 
                : _inviteSelectedFriends,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: _isInviting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text('دعوة (${_selectedFriends.length})'),
          ),
        ],
      ),
    );
  }

  List<Friend> _getFilteredFriends() {
    if (_searchQuery.isEmpty) {
      return _allFriends;
    }
    
    return _allFriends.where((friend) {
      return friend.name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  void _inviteSelectedFriends() async {
    setState(() {
      _isInviting = true;
    });

    try {
      final socialProvider = Provider.of<SocialProvider>(context, listen: false);
      
      // محاكاة إرسال الدعوات
      await Future.delayed(const Duration(seconds: 2));
      
      for (final friendId in _selectedFriends) {
        await socialProvider.inviteFriendToGroup(widget.group.id, friendId);
      }

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('تم إرسال ${_selectedFriends.length} دعوة بنجاح! 📧'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الدعوات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isInviting = false;
        });
      }
    }
  }
}

class Friend {
  final String id;
  final String name;
  final String? avatar;
  final bool isOnline;

  const Friend({
    required this.id,
    required this.name,
    this.avatar,
    this.isOnline = false,
  });
}
