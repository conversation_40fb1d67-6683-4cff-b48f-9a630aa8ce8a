import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/saved_item.dart';
import '../models/post.dart';
import '../services/saved_items_service.dart';

class SavedItemsProvider with ChangeNotifier {
  final SavedItemsService _savedItemsService = SavedItemsService();
  
  List<SavedItem> _savedItems = [];
  List<SavedItem> _filteredItems = [];
  bool _isLoading = false;
  String? _errorMessage;
  SavedItemType? _selectedFilter;
  String _searchQuery = '';
  
  // Subscription للتحديثات الفورية
  StreamSubscription<List<SavedItem>>? _savedItemsSubscription;
  
  // Getters
  List<SavedItem> get savedItems => _filteredItems;
  List<SavedItem> get allSavedItems => _savedItems;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  SavedItemType? get selectedFilter => _selectedFilter;
  String get searchQuery => _searchQuery;
  
  SavedItemsProvider() {
    _setupStreamListener();
    loadSavedItems();
  }
  
  // إعداد المستمع للتحديثات الفورية
  void _setupStreamListener() {
    _savedItemsSubscription = _savedItemsService.savedItemsStream.listen(
      (savedItems) {
        _savedItems = savedItems;
        _applyFilters();
        notifyListeners();
      },
    );
  }
  
  // تحميل العناصر المحفوظة
  Future<void> loadSavedItems() async {
    try {
      _setLoading(true);
      _savedItems = await _savedItemsService.getSavedItems();
      _applyFilters();
      _clearError();
    } catch (e) {
      _setError('خطأ في تحميل العناصر المحفوظة: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // حفظ منشور
  Future<void> savePost(Post post) async {
    try {
      await _savedItemsService.savePost(post);
      _clearError();
    } catch (e) {
      _setError('خطأ في حفظ المنشور: $e');
    }
  }
  
  // حفظ صورة
  Future<void> saveImage({
    required String imageId,
    required String imageUrl,
    String? title,
    String? authorName,
    String? authorId,
  }) async {
    try {
      await _savedItemsService.saveImage(
        imageId: imageId,
        imageUrl: imageUrl,
        title: title,
        authorName: authorName,
        authorId: authorId,
      );
      _clearError();
    } catch (e) {
      _setError('خطأ في حفظ الصورة: $e');
    }
  }
  
  // حفظ فيديو
  Future<void> saveVideo({
    required String videoId,
    required String videoUrl,
    String? title,
    String? thumbnailUrl,
    String? authorName,
    String? authorId,
    int? duration,
  }) async {
    try {
      await _savedItemsService.saveVideo(
        videoId: videoId,
        videoUrl: videoUrl,
        title: title,
        thumbnailUrl: thumbnailUrl,
        authorName: authorName,
        authorId: authorId,
        duration: duration,
      );
      _clearError();
    } catch (e) {
      _setError('خطأ في حفظ الفيديو: $e');
    }
  }
  
  // إزالة عنصر محفوظ
  Future<void> removeSavedItem(String savedItemId) async {
    try {
      await _savedItemsService.removeSavedItem(savedItemId);
      _clearError();
    } catch (e) {
      _setError('خطأ في إزالة العنصر المحفوظ: $e');
    }
  }
  
  // إزالة عنصر بناءً على نوعه ومعرفه
  Future<void> removeSavedItemByTypeAndId(SavedItemType type, String itemId) async {
    try {
      await _savedItemsService.removeSavedItemByTypeAndId(type, itemId);
      _clearError();
    } catch (e) {
      _setError('خطأ في إزالة العنصر المحفوظ: $e');
    }
  }
  
  // التحقق من كون العنصر محفوظ
  Future<bool> isItemSaved(SavedItemType type, String itemId) async {
    try {
      return await _savedItemsService.isItemSaved(type, itemId);
    } catch (e) {
      _setError('خطأ في التحقق من العنصر المحفوظ: $e');
      return false;
    }
  }
  
  // تطبيق الفلاتر والبحث
  void _applyFilters() {
    List<SavedItem> filtered = List.from(_savedItems);
    
    // تطبيق فلتر النوع
    if (_selectedFilter != null) {
      filtered = filtered.where((item) => item.type == _selectedFilter).toList();
    }
    
    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        return item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (item.description?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
               (item.authorName?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }
    
    _filteredItems = filtered;
  }
  
  // تعيين فلتر النوع
  void setFilter(SavedItemType? filter) {
    _selectedFilter = filter;
    _applyFilters();
    notifyListeners();
  }
  
  // تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }
  
  // مسح جميع العناصر المحفوظة
  Future<void> clearAllSavedItems() async {
    try {
      await _savedItemsService.clearAllSavedItems();
      _clearError();
    } catch (e) {
      _setError('خطأ في مسح العناصر المحفوظة: $e');
    }
  }
  
  // مسح العناصر المحفوظة بنوع معين
  Future<void> clearSavedItemsByType(SavedItemType type) async {
    try {
      await _savedItemsService.clearSavedItemsByType(type);
      _clearError();
    } catch (e) {
      _setError('خطأ في مسح العناصر المحفوظة: $e');
    }
  }
  
  // الحصول على إحصائيات العناصر المحفوظة
  Future<Map<SavedItemType, int>> getSavedItemsStats() async {
    try {
      return await _savedItemsService.getSavedItemsStats();
    } catch (e) {
      _setError('خطأ في تحميل إحصائيات العناصر المحفوظة: $e');
      return {};
    }
  }
  
  // الحصول على العناصر المحفوظة بنوع معين
  List<SavedItem> getSavedItemsByType(SavedItemType type) {
    return _savedItems.where((item) => item.type == type).toList();
  }
  
  // الحصول على عدد العناصر المحفوظة
  int get totalSavedItemsCount => _savedItems.length;
  
  // الحصول على عدد العناصر المحفوظة بنوع معين
  int getSavedItemsCountByType(SavedItemType type) {
    return _savedItems.where((item) => item.type == type).length;
  }
  
  // دوال مساعدة لإدارة الحالة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  @override
  void dispose() {
    _savedItemsSubscription?.cancel();
    _savedItemsService.dispose();
    super.dispose();
  }
}
