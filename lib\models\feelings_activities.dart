// نماذج المشاعر والأنشطة مثل Facebook

class Feeling {
  final String id;
  final String name;
  final String emoji;
  final String color;

  const Feeling({
    required this.id,
    required this.name,
    required this.emoji,
    required this.color,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'emoji': emoji,
      'color': color,
    };
  }

  factory Feeling.fromJson(Map<String, dynamic> json) {
    return Feeling(
      id: json['id'],
      name: json['name'],
      emoji: json['emoji'],
      color: json['color'],
    );
  }
}

class Activity {
  final String id;
  final String name;
  final String icon;
  final String color;
  final List<String> subActivities;

  const Activity({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    this.subActivities = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'color': color,
      'subActivities': subActivities,
    };
  }

  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'],
      name: json['name'],
      icon: json['icon'],
      color: json['color'],
      subActivities: List<String>.from(json['subActivities'] ?? []),
    );
  }
}

class Location {
  final String id;
  final String name;
  final String type; // 'home', 'work', 'school', 'custom'
  final String icon;
  final String? address;

  const Location({
    required this.id,
    required this.name,
    required this.type,
    required this.icon,
    this.address,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'icon': icon,
      'address': address,
    };
  }

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      icon: json['icon'],
      address: json['address'],
    );
  }
}

// قوائم المشاعر مثل Facebook
class FeelingsData {
  static const List<Feeling> feelings = [
    // مشاعر إيجابية
    Feeling(id: 'happy', name: 'سعيد', emoji: '😊', color: '#FFD700'),
    Feeling(id: 'excited', name: 'متحمس', emoji: '🤩', color: '#FF6B35'),
    Feeling(id: 'grateful', name: 'ممتن', emoji: '🙏', color: '#D32F2F'),
    Feeling(id: 'blessed', name: 'مبارك', emoji: '✨', color: '#9C27B0'),
    Feeling(id: 'loved', name: 'محبوب', emoji: '🥰', color: '#E91E63'),
    Feeling(id: 'proud', name: 'فخور', emoji: '😎', color: '#2196F3'),
    Feeling(id: 'relaxed', name: 'مرتاح', emoji: '😌', color: '#00BCD4'),
    Feeling(id: 'motivated', name: 'متحفز', emoji: '💪', color: '#FF5722'),
    Feeling(id: 'hopeful', name: 'متفائل', emoji: '🌟', color: '#FFEB3B'),
    Feeling(id: 'peaceful', name: 'هادئ', emoji: '☮️', color: '#D32F2F'),
    
    // مشاعر محايدة
    Feeling(id: 'thoughtful', name: 'مفكر', emoji: '🤔', color: '#607D8B'),
    Feeling(id: 'curious', name: 'فضولي', emoji: '🧐', color: '#795548'),
    Feeling(id: 'surprised', name: 'متفاجئ', emoji: '😲', color: '#FF9800'),
    Feeling(id: 'confused', name: 'محتار', emoji: '😕', color: '#9E9E9E'),
    
    // مشاعر سلبية
    Feeling(id: 'sad', name: 'حزين', emoji: '😢', color: '#3F51B5'),
    Feeling(id: 'tired', name: 'متعب', emoji: '😴', color: '#673AB7'),
    Feeling(id: 'stressed', name: 'متوتر', emoji: '😰', color: '#F44336'),
    Feeling(id: 'worried', name: 'قلق', emoji: '😟', color: '#FF5722'),
    Feeling(id: 'frustrated', name: 'محبط', emoji: '😤', color: '#E91E63'),
    Feeling(id: 'lonely', name: 'وحيد', emoji: '😔', color: '#607D8B'),
  ];
}

// قوائم الأنشطة مثل Facebook
class ActivitiesData {
  static const List<Activity> activities = [
    // أنشطة اجتماعية
    Activity(
      id: 'celebrating',
      name: 'يحتفل',
      icon: '🎉',
      color: '#FF6B35',
      subActivities: ['عيد ميلاد', 'تخرج', 'ترقية', 'زواج', 'نجاح'],
    ),
    Activity(
      id: 'eating',
      name: 'يتناول',
      icon: '🍽️',
      color: '#D32F2F',
      subActivities: ['الإفطار', 'الغداء', 'العشاء', 'وجبة خفيفة', 'حلويات'],
    ),
    Activity(
      id: 'attending',
      name: 'يحضر',
      icon: '📅',
      color: '#2196F3',
      subActivities: ['مؤتمر', 'حفلة', 'اجتماع', 'ورشة عمل', 'فعالية'],
    ),
    Activity(
      id: 'listening',
      name: 'يستمع',
      icon: '🎵',
      color: '#9C27B0',
      subActivities: ['موسيقى', 'بودكاست', 'محاضرة', 'أغنية', 'راديو'],
    ),
    Activity(
      id: 'thinking',
      name: 'يفكر في',
      icon: '💭',
      color: '#607D8B',
      subActivities: ['المستقبل', 'الحياة', 'العمل', 'الأهداف', 'الذكريات'],
    ),
    
    // أنشطة ترفيهية
    Activity(
      id: 'playing',
      name: 'يلعب',
      icon: '🎮',
      color: '#FF5722',
      subActivities: ['ألعاب فيديو', 'كرة قدم', 'تنس', 'شطرنج', 'ألعاب جماعية'],
    ),
    Activity(
      id: 'watching',
      name: 'يشاهد',
      icon: '📺',
      color: '#E91E63',
      subActivities: ['فيلم', 'مسلسل', 'مباراة', 'وثائقي', 'يوتيوب'],
    ),
    Activity(
      id: 'drinking',
      name: 'يشرب',
      icon: '☕',
      color: '#795548',
      subActivities: ['قهوة', 'شاي', 'عصير', 'ماء', 'مشروب ساخن'],
    ),
    
    // أنشطة تعليمية
    Activity(
      id: 'reading',
      name: 'يقرأ',
      icon: '📚',
      color: '#3F51B5',
      subActivities: ['كتاب', 'مقال', 'جريدة', 'مجلة', 'قصة'],
    ),
    Activity(
      id: 'learning',
      name: 'يتعلم',
      icon: '🎓',
      color: '#00BCD4',
      subActivities: ['لغة جديدة', 'مهارة', 'برمجة', 'طبخ', 'رياضة'],
    ),
    Activity(
      id: 'searching',
      name: 'يبحث عن',
      icon: '🔍',
      color: '#FF9800',
      subActivities: ['وظيفة', 'منزل', 'معلومات', 'أصدقاء', 'حلول'],
    ),
    
    // أنشطة أخرى
    Activity(
      id: 'traveling',
      name: 'يسافر إلى',
      icon: '✈️',
      color: '#FFEB3B',
      subActivities: ['بلد جديد', 'مدينة', 'شاطئ', 'جبال', 'صحراء'],
    ),
    Activity(
      id: 'supporting',
      name: 'يدعم',
      icon: '🤝',
      color: '#D32F2F',
      subActivities: ['فريق رياضي', 'قضية', 'صديق', 'عائلة', 'مجتمع'],
    ),
    Activity(
      id: 'working',
      name: 'يعمل على',
      icon: '💼',
      color: '#673AB7',
      subActivities: ['مشروع', 'تقرير', 'عرض تقديمي', 'تطبيق', 'فكرة'],
    ),
  ];
}

// قوائم المواقع
class LocationsData {
  static const List<Location> defaultLocations = [
    Location(
      id: 'home',
      name: 'المنزل',
      type: 'home',
      icon: '🏠',
    ),
    Location(
      id: 'work',
      name: 'العمل',
      type: 'work',
      icon: '🏢',
    ),
    Location(
      id: 'school',
      name: 'المدرسة',
      type: 'school',
      icon: '🏫',
    ),
    Location(
      id: 'university',
      name: 'الجامعة',
      type: 'school',
      icon: '🎓',
    ),
    Location(
      id: 'gym',
      name: 'النادي الرياضي',
      type: 'custom',
      icon: '🏋️',
    ),
    Location(
      id: 'restaurant',
      name: 'مطعم',
      type: 'custom',
      icon: '🍽️',
    ),
    Location(
      id: 'cafe',
      name: 'مقهى',
      type: 'custom',
      icon: '☕',
    ),
    Location(
      id: 'mall',
      name: 'مركز تسوق',
      type: 'custom',
      icon: '🛍️',
    ),
    Location(
      id: 'hospital',
      name: 'مستشفى',
      type: 'custom',
      icon: '🏥',
    ),
    Location(
      id: 'airport',
      name: 'مطار',
      type: 'custom',
      icon: '✈️',
    ),
  ];
}
