import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../providers/social_provider.dart';
import '../screens/group_admin_screen.dart';
import '../theme/app_theme.dart';
import '../widgets/invite_friends_dialog.dart';
import '../widgets/create_group_post_dialog.dart';

class GroupHeader extends StatelessWidget {
  final Group group;

  const GroupHeader({
    super.key,
    required this.group,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 300,
      child: Stack(
        children: [
          // صورة الغلاف
          _buildCoverImage(),
          
          // تدرج لوني للنص
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
          ),
          
          // معلومات المجموعة
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // صورة المجموعة واسمها
                Row(
                  children: [
                    // صورة المجموعة
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white, width: 3),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(9),
                        child: group.profileImage != null
                            ? Image.network(
                                group.profileImage!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildDefaultGroupIcon();
                                },
                              )
                            : _buildDefaultGroupIcon(),
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // اسم المجموعة ومعلومات أساسية
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // اسم المجموعة
                          Text(
                            group.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // نوع الخصوصية
                          Row(
                            children: [
                              _buildPrivacyIcon(),
                              const SizedBox(width: 4),
                              Text(
                                _getPrivacyText(),
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // عدد الأعضاء
                          Text(
                            '${group.memberCount} عضو',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // أزرار الإجراءات
                _buildActionButtons(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoverImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: group.coverImage != null
          ? Image.network(
              group.coverImage!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildDefaultCover();
              },
            )
          : _buildDefaultCover(),
    );
  }

  Widget _buildDefaultCover() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
            AppTheme.accentColor,
          ],
        ),
      ),
      child: Center(
        child: Icon(
          Icons.groups,
          size: 80,
          color: Colors.white.withValues(alpha: 0.3),
        ),
      ),
    );
  }

  Widget _buildDefaultGroupIcon() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.accentColor,
          ],
        ),
      ),
      child: const Icon(
        Icons.groups,
        color: Colors.white,
        size: 40,
      ),
    );
  }

  Widget _buildPrivacyIcon() {
    IconData icon;
    Color color;

    switch (group.privacy) {
      case GroupPrivacy.public:
        icon = Icons.public;
        color = Colors.green;
        break;
      case GroupPrivacy.private:
        icon = Icons.lock_outline;
        color = Colors.orange;
        break;
      case GroupPrivacy.secret:
        icon = Icons.lock;
        color = Colors.red;
        break;
    }

    return Icon(
      icon,
      color: color,
      size: 16,
    );
  }

  String _getPrivacyText() {
    switch (group.privacy) {
      case GroupPrivacy.public:
        return 'مجموعة عامة';
      case GroupPrivacy.private:
        return 'مجموعة خاصة';
      case GroupPrivacy.secret:
        return 'مجموعة سرية';
    }
  }

  Widget _buildActionButtons(BuildContext context) {
    final isUserMember = group.isUserMember('current_user');
    final isUserAdmin = group.isUserAdmin('current_user');

    return Row(
      children: [
        // زر الانضمام/المغادرة
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _handleMembershipAction(context),
            icon: Icon(
              isUserMember ? Icons.check : Icons.group_add,
              size: 18,
            ),
            label: Text(
              isUserMember ? 'عضو' : 'انضمام',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: isUserMember ? Colors.green : AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // زر دعوة الأصدقاء
        if (isUserMember)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _inviteFriends(context),
              icon: const Icon(Icons.person_add, size: 18),
              label: const Text(
                'دعوة',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: const BorderSide(color: Colors.white),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        
        // زر إنشاء منشور (للأعضاء فقط)
        if (isUserMember) ...[
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: () => _createPost(context),
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('نشر'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],

        // زر الإدارة (للمديرين فقط)
        if (isUserAdmin) ...[
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: () => _openGroupAdmin(context),
            icon: const Icon(Icons.admin_panel_settings, size: 18),
            label: const Text('إدارة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.withValues(alpha: 0.9),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _handleMembershipAction(BuildContext context) async {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);

    if (group.isUserMember('current_user')) {
      // مغادرة المجموعة
      _showLeaveGroupDialog(context, socialProvider);
    } else {
      // الانضمام للمجموعة
      try {
        await socialProvider.joinGroup(group.id);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('تم الانضمام لمجموعة ${group.name} بنجاح! 🎉'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في الانضمام: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showLeaveGroupDialog(BuildContext context, SocialProvider socialProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مغادرة المجموعة'),
        content: Text('هل أنت متأكد من مغادرة مجموعة ${group.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              socialProvider.leaveGroup(group.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تم مغادرة مجموعة ${group.name}')),
              );
            },
            child: const Text('مغادرة'),
          ),
        ],
      ),
    );
  }

  void _inviteFriends(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => InviteFriendsDialog(group: group),
    );
  }

  void _createPost(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CreateGroupPostDialog(group: group),
    );
  }

  void _openGroupAdmin(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupAdminScreen(group: group),
      ),
    );
  }
}
