import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import '../theme/app_theme.dart';
import '../models/message.dart';
import '../widgets/voice_message_widget.dart';
import '../widgets/emoji_sticker_picker.dart';

class AdvancedMessageInput extends StatefulWidget {
  final Function(String content, MessageType type) onSendMessage;
  final Function(File file, MessageType type)? onSendMedia;
  final Function(String location)? onSendLocation;
  final VoidCallback? onStartRecording;
  final VoidCallback? onStopRecording;
  final bool isRecording;
  final Message? replyToMessage;
  final VoidCallback? onCancelReply;

  const AdvancedMessageInput({
    super.key,
    required this.onSendMessage,
    this.onSendMedia,
    this.onSendLocation,
    this.onStartRecording,
    this.onStopRecording,
    this.isRecording = false,
    this.replyToMessage,
    this.onCancelReply,
  });

  @override
  State<AdvancedMessageInput> createState() => _AdvancedMessageInputState();
}

class _AdvancedMessageInputState extends State<AdvancedMessageInput>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ImagePicker _picker = ImagePicker();
  
  bool _showAttachments = false;
  bool _isTyping = false;
  late AnimationController _attachmentController;
  late Animation<double> _attachmentAnimation;

  @override
  void initState() {
    super.initState();
    _attachmentController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _attachmentAnimation = CurvedAnimation(
      parent: _attachmentController,
      curve: Curves.easeInOut,
    );

    _textController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _attachmentController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final isTyping = _textController.text.trim().isNotEmpty;
    if (isTyping != _isTyping) {
      setState(() {
        _isTyping = isTyping;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Column(
        children: [
          // معاينة الرد
          if (widget.replyToMessage != null)
            _buildReplyPreview(),
          
          // شريط المرفقات
          AnimatedBuilder(
            animation: _attachmentAnimation,
            builder: (context, child) {
              return SizeTransition(
                sizeFactor: _attachmentAnimation,
                child: _showAttachments ? _buildAttachmentsBar() : const SizedBox(),
              );
            },
          ),
          
          // شريط الإدخال الرئيسي
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // زر المرفقات
                _buildAttachmentButton(),
                
                const SizedBox(width: 8),
                
                // حقل النص
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _textController,
                            focusNode: _focusNode,
                            decoration: const InputDecoration(
                              hintText: 'اكتب رسالة...',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            maxLines: null,
                            textInputAction: TextInputAction.newline,
                            onSubmitted: (_) => _sendTextMessage(),
                          ),
                        ),
                        
                        // زر الإيموجي
                        IconButton(
                          onPressed: _showEmojiPicker,
                          icon: const Icon(Icons.emoji_emotions_outlined),
                          color: Colors.grey[600],
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // زر الإرسال أو التسجيل
                _buildSendButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReplyPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 3,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الرد على',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  widget.replyToMessage!.content,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onCancelReply,
            icon: const Icon(Icons.close, size: 20),
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentsBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildAttachmentOption(
            icon: Icons.photo_camera,
            label: 'كاميرا',
            color: Colors.green,
            onTap: () => _pickImage(ImageSource.camera),
          ),
          _buildAttachmentOption(
            icon: Icons.photo_library,
            label: 'معرض',
            color: Colors.blue,
            onTap: () => _pickImage(ImageSource.gallery),
          ),
          _buildAttachmentOption(
            icon: Icons.videocam,
            label: 'فيديو',
            color: Colors.red,
            onTap: () => _pickVideo(),
          ),
          _buildAttachmentOption(
            icon: Icons.attach_file,
            label: 'ملف',
            color: Colors.orange,
            onTap: () => _pickFile(),
          ),
          _buildAttachmentOption(
            icon: Icons.location_on,
            label: 'موقع',
            color: Colors.purple,
            onTap: () => _sendLocation(),
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        onTap();
        _toggleAttachments();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentButton() {
    return GestureDetector(
      onTap: _toggleAttachments,
      child: AnimatedRotation(
        turns: _showAttachments ? 0.125 : 0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _showAttachments ? AppTheme.primaryColor : Colors.grey[300],
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.add,
            color: _showAttachments ? Colors.white : Colors.grey[600],
            size: 24,
          ),
        ),
      ),
    );
  }

  Widget _buildSendButton() {
    if (_isTyping) {
      return GestureDetector(
        onTap: _sendTextMessage,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.send,
            color: Colors.white,
            size: 24,
          ),
        ),
      );
    }

    // استخدام ويدجت التسجيل الصوتي المتقدم
    return VoiceRecorderWidget(
      onStart: () {
        if (widget.onStartRecording != null) {
          widget.onStartRecording!();
        }
      },
      onStop: () {
        if (widget.onStopRecording != null) {
          widget.onStopRecording!();
        }
      },
      onCancel: () {
        // إلغاء التسجيل
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إلغاء التسجيل'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      },
      onComplete: (duration) {
        // إرسال الرسالة الصوتية
        final audioFile = File('/mock/audio/voice_${DateTime.now().millisecondsSinceEpoch}.m4a');
        if (widget.onSendMedia != null) {
          widget.onSendMedia!(audioFile, MessageType.audio);
        } else {
          widget.onSendMessage('🎤 رسالة صوتية (${_formatDuration(duration)})', MessageType.audio);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال الرسالة الصوتية'),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
    );
  }

  void _toggleAttachments() {
    setState(() {
      _showAttachments = !_showAttachments;
    });
    
    if (_showAttachments) {
      _attachmentController.forward();
    } else {
      _attachmentController.reverse();
    }
  }

  void _sendTextMessage() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      widget.onSendMessage(text, MessageType.text);
      _textController.clear();
    }
  }

  void _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        if (widget.onSendMedia != null) {
          widget.onSendMedia!(File(image.path), MessageType.image);
        } else {
          // إرسال كرسالة نصية مع وصف الصورة
          widget.onSendMessage('تم إرسال صورة: ${image.name}', MessageType.image);
        }

        // إغلاق قائمة المرفقات
        _toggleAttachments();

        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال الصورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      _showError('خطأ في اختيار الصورة: $e');
    }
  }

  void _pickVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        if (widget.onSendMedia != null) {
          widget.onSendMedia!(File(video.path), MessageType.video);
        } else {
          // إرسال كرسالة نصية مع وصف الفيديو
          widget.onSendMessage('تم إرسال فيديو: ${video.name}', MessageType.video);
        }

        // إغلاق قائمة المرفقات
        _toggleAttachments();

        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال الفيديو بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      _showError('خطأ في اختيار الفيديو: $e');
    }
  }

  void _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;
        final fileSize = result.files.single.size;

        if (widget.onSendMedia != null) {
          widget.onSendMedia!(file, MessageType.file);
        } else {
          // إرسال كرسالة نصية مع وصف الملف
          widget.onSendMessage('تم إرسال ملف: $fileName (${_formatFileSize(fileSize)})', MessageType.file);
        }

        // إغلاق قائمة المرفقات
        _toggleAttachments();

        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال الملف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      _showError('خطأ في اختيار الملف: $e');
    }
  }

  void _sendLocation() async {
    try {
      // محاكاة الحصول على الموقع الحالي
      const String locationName = 'الرياض، المملكة العربية السعودية';
      const double latitude = 24.7136;
      const double longitude = 46.6753;

      if (widget.onSendLocation != null) {
        widget.onSendLocation!(locationName);
      } else {
        // إرسال كرسالة موقع
        widget.onSendMessage('📍 $locationName', MessageType.location);
      }

      // إغلاق قائمة المرفقات
      _toggleAttachments();

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مشاركة الموقع بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _showError('خطأ في مشاركة الموقع: $e');
    }
  }

  void _showEmojiPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EmojiStickerPicker(
        onEmojiSelected: (emoji) {
          _textController.text += emoji;
          Navigator.pop(context);
        },
        onStickerSelected: (sticker) {
          widget.onSendMessage(sticker, MessageType.emoji);
          Navigator.pop(context);
        },
        onClose: () => Navigator.pop(context),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
