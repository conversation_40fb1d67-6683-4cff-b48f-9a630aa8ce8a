class GroupPoll {
  final String id;
  final String groupId;
  final String authorId;
  final String question;
  final List<PollOption> options;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final bool allowMultipleChoices;
  final bool isActive;
  final List<String> voters;

  const GroupPoll({
    required this.id,
    required this.groupId,
    required this.authorId,
    required this.question,
    required this.options,
    required this.createdAt,
    this.expiresAt,
    this.allowMultipleChoices = false,
    this.isActive = true,
    this.voters = const [],
  });

  int get totalVotes => options.fold(0, (sum, option) => sum + option.votes.length);
  
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
  
  bool hasUserVoted(String userId) => voters.contains(userId);

  GroupPoll copyWith({
    String? id,
    String? groupId,
    String? authorId,
    String? question,
    List<PollOption>? options,
    DateTime? createdAt,
    DateTime? expiresAt,
    bool? allowMultipleChoices,
    bool? isActive,
    List<String>? voters,
  }) {
    return GroupPoll(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      authorId: authorId ?? this.authorId,
      question: question ?? this.question,
      options: options ?? this.options,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      allowMultipleChoices: allowMultipleChoices ?? this.allowMultipleChoices,
      isActive: isActive ?? this.isActive,
      voters: voters ?? this.voters,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'authorId': authorId,
      'question': question,
      'options': options.map((o) => o.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'allowMultipleChoices': allowMultipleChoices,
      'isActive': isActive,
      'voters': voters,
    };
  }

  factory GroupPoll.fromJson(Map<String, dynamic> json) {
    return GroupPoll(
      id: json['id'],
      groupId: json['groupId'],
      authorId: json['authorId'],
      question: json['question'],
      options: (json['options'] as List)
          .map((o) => PollOption.fromJson(o))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      expiresAt: json['expiresAt'] != null 
          ? DateTime.parse(json['expiresAt']) 
          : null,
      allowMultipleChoices: json['allowMultipleChoices'] ?? false,
      isActive: json['isActive'] ?? true,
      voters: List<String>.from(json['voters'] ?? []),
    );
  }
}

class PollOption {
  final String id;
  final String text;
  final List<String> votes;

  const PollOption({
    required this.id,
    required this.text,
    this.votes = const [],
  });

  double getPercentage(int totalVotes) {
    if (totalVotes == 0) return 0.0;
    return (votes.length / totalVotes) * 100;
  }

  PollOption copyWith({
    String? id,
    String? text,
    List<String>? votes,
  }) {
    return PollOption(
      id: id ?? this.id,
      text: text ?? this.text,
      votes: votes ?? this.votes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'votes': votes,
    };
  }

  factory PollOption.fromJson(Map<String, dynamic> json) {
    return PollOption(
      id: json['id'],
      text: json['text'],
      votes: List<String>.from(json['votes'] ?? []),
    );
  }
}
