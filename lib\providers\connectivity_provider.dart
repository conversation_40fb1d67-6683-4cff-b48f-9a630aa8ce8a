import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/connectivity_status.dart';
import '../models/pending_action.dart' as pending;
import '../services/connectivity_service.dart';
import '../services/cache_service.dart';

class ConnectivityProvider with ChangeNotifier {
  final ConnectivityService _connectivityService = ConnectivityService();
  final CacheService _cacheService = CacheService();

  // Current state
  NetworkState _networkState = NetworkState.initial;
  final List<pending.PendingAction> _pendingActions = [];
  bool _showConnectionBanner = false;
  String? _lastConnectionMessage;

  // Track initialization and previous state
  bool _isInitialized = false;
  NetworkState? _previousState;

  // Subscription
  StreamSubscription<NetworkState>? _networkStateSubscription;

  // Getters
  NetworkState get networkState => _networkState;
  bool get isOnline => _networkState.isOnline;
  bool get isOffline => !_networkState.isOnline;
  bool get isReconnecting => _networkState.status == ConnectivityStatus.reconnecting;
  bool get showConnectionBanner => _showConnectionBanner;
  String? get lastConnectionMessage => _lastConnectionMessage;
  List<pending.PendingAction> get pendingActions => List.unmodifiable(_pendingActions);
  int get pendingActionsCount => _pendingActions.length;

  // Connection quality
  String get connectionQuality => _networkState.connectionQualityText;
  String get connectionType => _networkState.typeDisplayName;

  ConnectivityProvider() {
    _initialize();
  }

  // Initialize the provider
  Future<void> _initialize() async {
    try {
      // Initialize services
      await _connectivityService.initialize();
      await _cacheService.initialize();

      // Listen to network state changes
      _networkStateSubscription = _connectivityService.networkStateStream.listen(
        _onNetworkStateChanged,
        onError: (error) {
          print('Error in network state stream: $error');
        },
      );

      // Set initial state without showing banner
      _networkState = _connectivityService.currentState;
      _isInitialized = true;
      notifyListeners();

    } catch (e) {
      print('Error initializing ConnectivityProvider: $e');
    }
  }

  // Handle network state changes
  void _onNetworkStateChanged(NetworkState newState) {
    final previousState = _networkState;
    _networkState = newState;

    // Only handle state transitions after initialization
    if (_isInitialized) {
      _handleStateTransition(previousState, newState);
    }

    // Update UI
    notifyListeners();
  }

  // Handle state transitions
  void _handleStateTransition(NetworkState previous, NetworkState current) {
    // Only show banner for actual state changes, not initial state
    if (previous.status == ConnectivityStatus.unknown) {
      return; // Skip initial state transitions
    }

    // Connection lost (was online, now offline)
    if (previous.isOnline && !current.isOnline) {
      _showConnectionBanner = true;
      _lastConnectionMessage = 'لا يوجد اتصال بالإنترنت';
      _scheduleHideBanner(duration: const Duration(seconds: 5));
    }

    // Connection restored (was offline, now online)
    else if (!previous.isOnline && current.isOnline) {
      _showConnectionBanner = true;
      _lastConnectionMessage = 'تم استعادة الاتصال بالإنترنت';
      _scheduleHideBanner(duration: const Duration(seconds: 3));

      // Process pending actions
      _processPendingActions();
    }

    // Reconnecting (only if not already reconnecting)
    else if (current.status == ConnectivityStatus.reconnecting &&
             previous.status != ConnectivityStatus.reconnecting) {
      _showConnectionBanner = true;
      _lastConnectionMessage = 'جارٍ إعادة الاتصال...';
      // Don't auto-hide while reconnecting
    }

    // Connection type changed (only if both states are online)
    else if (previous.type != current.type &&
             current.isOnline &&
             previous.isOnline &&
             previous.type != ConnectionType.none) {
      _showConnectionBanner = true;
      _lastConnectionMessage = 'تم التبديل إلى ${current.typeDisplayName}';
      _scheduleHideBanner(duration: const Duration(seconds: 2));
    }
  }

  // Schedule hiding the connection banner
  void _scheduleHideBanner({Duration duration = const Duration(seconds: 3)}) {
    Timer(duration, () {
      if (_networkState.status != ConnectivityStatus.reconnecting) {
        _showConnectionBanner = false;
        notifyListeners();
      }
    });
  }

  // Manually hide the connection banner
  void hideConnectionBanner() {
    _showConnectionBanner = false;
    notifyListeners();
  }

  // Add pending action
  Future<void> addPendingAction({
    required String type,
    required Map<String, dynamic> data,
    int maxRetries = 3,
  }) async {
    try {
      final action = pending.PendingAction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: type,
        data: data,
        createdAt: DateTime.now(),
      );
      
      _pendingActions.add(action);
      notifyListeners();
      
      // Save to persistent storage
      await _connectivityService.addPendingAction(action);
      
      // Try to execute immediately if online
      if (isOnline) {
        await _processPendingActions();
      }
    } catch (e) {
      print('Error adding pending action: $e');
    }
  }

  // Process pending actions
  Future<void> _processPendingActions() async {
    if (!isOnline || _pendingActions.isEmpty) return;
    
    try {
      final actionsToRemove = <pending.PendingAction>[];
      
      for (final action in _pendingActions) {
        final success = await _executeAction(action);
        if (success) {
          actionsToRemove.add(action);
        }
      }
      
      // Remove successful actions
      for (final action in actionsToRemove) {
        _pendingActions.remove(action);
      }
      
      if (actionsToRemove.isNotEmpty) {
        notifyListeners();
      }
    } catch (e) {
      print('Error processing pending actions: $e');
    }
  }

  // Execute a pending action
  Future<bool> _executeAction(pending.PendingAction action) async {
    try {
      // This would be implemented based on action type
      // For now, simulate execution
      switch (action.type) {
        case 'like_post':
          return await _executeLikeAction(action.data);
        case 'comment_post':
          return await _executeCommentAction(action.data);
        case 'create_post':
          return await _executeCreatePostAction(action.data);
        case 'follow_user':
          return await _executeFollowAction(action.data);
        default:
          print('Unknown action type: ${action.type}');
          return false;
      }
    } catch (e) {
      print('Error executing action ${action.id}: $e');
      return false;
    }
  }

  // Execute like action
  Future<bool> _executeLikeAction(Map<String, dynamic> data) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));
      print('Executed like action: $data');
      return true;
    } catch (e) {
      print('Error executing like action: $e');
      return false;
    }
  }

  // Execute comment action
  Future<bool> _executeCommentAction(Map<String, dynamic> data) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 800));
      print('Executed comment action: $data');
      return true;
    } catch (e) {
      print('Error executing comment action: $e');
      return false;
    }
  }

  // Execute create post action
  Future<bool> _executeCreatePostAction(Map<String, dynamic> data) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 1200));
      print('Executed create post action: $data');
      return true;
    } catch (e) {
      print('Error executing create post action: $e');
      return false;
    }
  }

  // Execute follow action
  Future<bool> _executeFollowAction(Map<String, dynamic> data) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 600));
      print('Executed follow action: $data');
      return true;
    } catch (e) {
      print('Error executing follow action: $e');
      return false;
    }
  }

  // Cache data
  Future<void> cacheData<T>(
    String key,
    T data, {
    Duration? ttl,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    try {
      await _cacheService.set(key, data, ttl: ttl, toJson: toJson);
    } catch (e) {
      print('Error caching data: $e');
    }
  }

  // Get cached data
  Future<T?> getCachedData<T>(
    String key, {
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      return await _cacheService.get<T>(key, fromJson: fromJson);
    } catch (e) {
      print('Error getting cached data: $e');
      return null;
    }
  }

  // Get data with fallback to cache
  Future<T> getDataWithCache<T>(
    String key,
    Future<T> Function() networkCall, {
    Duration? ttl,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    try {
      if (isOnline) {
        // Try network first when online
        return await _cacheService.getWithFallback(
          key,
          networkCall,
          ttl: ttl,
          fromJson: fromJson,
          toJson: toJson,
        );
      } else {
        // Use cache when offline
        final cachedData = await getCachedData<T>(key, fromJson: fromJson);
        if (cachedData != null) {
          return cachedData;
        }
        throw Exception('لا يوجد اتصال بالإنترنت وليس هناك بيانات محفوظة');
      }
    } catch (e) {
      print('Error getting data with cache: $e');
      rethrow;
    }
  }

  // Clear cache
  Future<void> clearCache() async {
    try {
      await _cacheService.clear();
    } catch (e) {
      print('Error clearing cache: $e');
    }
  }

  // Get cache info
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      return await _cacheService.getCacheInfo();
    } catch (e) {
      print('Error getting cache info: $e');
      return {};
    }
  }

  // Retry failed actions
  Future<void> retryFailedActions() async {
    if (isOnline) {
      await _processPendingActions();
    }
  }

  // Clear pending actions
  void clearPendingActions() {
    _pendingActions.clear();
    notifyListeners();
  }

  // Force connectivity check
  Future<void> forceConnectivityCheck() async {
    try {
      // This would trigger a manual connectivity check
      // For now, just notify listeners
      notifyListeners();
    } catch (e) {
      print('Error forcing connectivity check: $e');
    }
  }

  @override
  void dispose() {
    _networkStateSubscription?.cancel();
    _connectivityService.dispose();
    super.dispose();
  }
}
