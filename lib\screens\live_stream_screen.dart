import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../providers/live_stream_provider.dart';
import '../models/live_stream.dart';
import '../models/reaction_types.dart';
import '../widgets/live_stream_chat.dart';
import '../widgets/live_stream_reactions.dart';
import '../widgets/live_stream_controls.dart';

class LiveStreamScreen extends StatefulWidget {
  final LiveStream stream;
  final bool isStreamer;

  const LiveStreamScreen({
    super.key,
    required this.stream,
    this.isStreamer = false,
  });

  @override
  State<LiveStreamScreen> createState() => _LiveStreamScreenState();
}

class _LiveStreamScreenState extends State<LiveStreamScreen> {
  Timer? _durationTimer;
  Duration _currentDuration = Duration.zero;
  bool _showControls = true;
  bool _showChat = true;
  Timer? _hideControlsTimer;

  @override
  void initState() {
    super.initState();
    _startDurationTimer();
    _setupAutoHideControls();
    
    // الانضمام للبث إذا لم يكن المستخدم هو الباث
    if (!widget.isStreamer) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _joinStream();
      });
    }
  }

  @override
  void dispose() {
    _durationTimer?.cancel();
    _hideControlsTimer?.cancel();
    
    // مغادرة البث إذا لم يكن المستخدم هو الباث
    if (!widget.isStreamer) {
      _leaveStream();
    }
    
    super.dispose();
  }

  void _startDurationTimer() {
    _durationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _currentDuration = DateTime.now().difference(widget.stream.startTime);
        });
      }
    });
  }

  void _setupAutoHideControls() {
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _resetAutoHideControls() {
    _hideControlsTimer?.cancel();
    setState(() {
      _showControls = true;
    });
    _setupAutoHideControls();
  }

  Future<void> _joinStream() async {
    final provider = Provider.of<LiveStreamProvider>(context, listen: false);
    await provider.joinStream(
      widget.stream.id,
      'current_user',
      'أنت',
    );
  }

  Future<void> _leaveStream() async {
    final provider = Provider.of<LiveStreamProvider>(context, listen: false);
    await provider.leaveStream(widget.stream.id, 'current_user');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Consumer<LiveStreamProvider>(
        builder: (context, provider, child) {
          final currentStream = provider.currentStream ?? widget.stream;
          
          return Stack(
            children: [
              // فيديو البث
              _buildStreamVideo(currentStream),
              
              // معلومات البث العلوية
              if (_showControls) _buildTopInfo(currentStream),
              
              // التحكم في البث (للباث فقط)
              if (widget.isStreamer && _showControls)
                _buildStreamerControls(provider),
              
              // التفاعلات المتحركة
              Positioned(
                right: 16,
                bottom: _showChat ? 300 : 100,
                child: LiveStreamReactions(
                  reactions: provider.currentReactions,
                ),
              ),
              
              // الدردشة المباشرة
              if (_showChat)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: LiveStreamChat(
                    streamId: currentStream.id,
                    comments: provider.currentComments,
                    onToggleChat: () {
                      setState(() {
                        _showChat = !_showChat;
                      });
                    },
                  ),
                ),
              
              // أزرار التفاعل (للمشاهدين)
              if (!widget.isStreamer)
                _buildViewerActions(currentStream, provider),
              
              // زر إغلاق/إنهاء البث
              Positioned(
                top: MediaQuery.of(context).padding.top + 16,
                left: 16,
                child: GestureDetector(
                  onTap: _handleBackButton,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      widget.isStreamer ? Icons.close : Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStreamVideo(LiveStream stream) {
    return GestureDetector(
      onTap: _resetAutoHideControls,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.withValues(alpha: 0.4),
              Colors.purple.withValues(alpha: 0.4),
              Colors.pink.withValues(alpha: 0.4),
              Colors.orange.withValues(alpha: 0.4),
            ],
          ),
        ),
        child: Stack(
          children: [
            // محاكاة فيديو البث
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.videocam,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    widget.isStreamer ? 'أنت تبث الآن' : 'مشاهدة البث المباشر',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    stream.title,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            // مؤشر البث المباشر
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    const Text(
                      'مباشر',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopInfo(LiveStream stream) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      right: 16,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // معلومات الباث
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.blue,
                  child: Text(
                    stream.streamer.name[0],
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  stream.streamer.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 8),
          
          // عدد المشاهدين ومدة البث
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.visibility,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Consumer<LiveStreamProvider>(
                  builder: (context, provider, child) {
                    final currentStream = provider.currentStream ?? stream;
                    return Text(
                      '${currentStream.viewerCount}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                const Icon(
                  Icons.access_time,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDuration(_currentDuration),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStreamerControls(LiveStreamProvider provider) {
    return Positioned(
      bottom: 100,
      left: 16,
      child: LiveStreamControls(
        isCameraEnabled: provider.isCameraEnabled,
        isMicEnabled: provider.isMicEnabled,
        isFrontCamera: provider.isFrontCamera,
        onToggleCamera: provider.toggleCamera,
        onToggleMicrophone: provider.toggleMicrophone,
        onSwitchCamera: provider.switchCamera,
        onEndStream: _endStream,
      ),
    );
  }

  Widget _buildViewerActions(LiveStream stream, LiveStreamProvider provider) {
    return Positioned(
      bottom: _showChat ? 320 : 120,
      right: 16,
      child: Column(
        children: [
          // زر الإعجاب
          _buildActionButton(
            icon: Icons.favorite,
            onTap: () => _addReaction(ReactionType.love, provider),
          ),
          const SizedBox(height: 12),
          
          // زر التفاعلات
          _buildActionButton(
            icon: Icons.emoji_emotions,
            onTap: () => _showReactionPicker(provider),
          ),
          const SizedBox(height: 12),
          
          // زر المشاركة
          _buildActionButton(
            icon: Icons.share,
            onTap: _shareStream,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }

  Future<void> _addReaction(ReactionType reactionType, LiveStreamProvider provider) async {
    await provider.addReaction(
      streamId: widget.stream.id,
      userId: 'current_user',
      userName: 'أنت',
      reactionType: reactionType,
    );
  }

  void _showReactionPicker(LiveStreamProvider provider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 120,
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: ReactionType.values.map((reaction) {
            return GestureDetector(
              onTap: () {
                Navigator.pop(context);
                _addReaction(reaction, provider);
              },
              child: Container(
                width: 48,
                height: 48,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    _getReactionEmoji(reaction),
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  String _getReactionEmoji(ReactionType reaction) {
    switch (reaction) {
      case ReactionType.like:
        return '👍';
      case ReactionType.love:
        return '❤️';
      case ReactionType.haha:
        return '😂';
      case ReactionType.wow:
        return '😮';
      case ReactionType.sad:
        return '😢';
      case ReactionType.angry:
        return '😡';
    }
  }

  void _shareStream() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ رابط البث! 🔗'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _endStream() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنهاء البث'),
        content: const Text('هل أنت متأكد من إنهاء البث المباشر؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إنهاء', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final provider = Provider.of<LiveStreamProvider>(context, listen: false);
      await provider.endLiveStream();
      
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  void _handleBackButton() {
    if (widget.isStreamer) {
      _endStream();
    } else {
      Navigator.pop(context);
    }
  }
}
