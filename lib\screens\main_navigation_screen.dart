import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../theme/app_theme.dart';
import 'social_screen.dart';
import 'groups_screen.dart';
import 'videos_screen.dart';
import 'people_screen.dart';
import 'create_post_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _screens = [
    const SocialScreen(),
    const GroupsScreen(),
    const VideosScreen(),
    const PeopleScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemCount: _screens.length,
        itemBuilder: (context, index) {
          return _screens[index];
        },
      ),
      // شريط التنقل السفلي مع زر الإضافة
      bottomNavigationBar: _buildBottomNavigationBar(),
      // زر الإضافة العائم
      floatingActionButton: _buildCreatePostButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  // شريط التنقل السفلي
  Widget _buildBottomNavigationBar() {
    return BottomAppBar(
      height: 70,
      color: Colors.white,
      elevation: 8,
      notchMargin: 8,
      shape: const CircularNotchedRectangle(),
      child: Row(
        children: [
          // الرئيسية
          Expanded(
            child: _buildBottomNavIcon(
              icon: _currentIndex == 0 ? Icons.home : Icons.home_outlined,
              label: 'الرئيسية',
              isActive: _currentIndex == 0,
              onTap: () => _navigateToPage(0),
            ),
          ),
          // المجموعات
          Expanded(
            child: _buildBottomNavIcon(
              icon: _currentIndex == 1 ? Icons.groups : Icons.groups_outlined,
              label: 'المجموعات',
              isActive: _currentIndex == 1,
              onTap: () => _navigateToPage(1),
            ),
          ),
          // مساحة فارغة للزر العائم (بالضبط في الوسط بين المجموعات والفيديوهات)
          const SizedBox(width: 56),
          // الفيديوهات
          Expanded(
            child: _buildBottomNavIcon(
              icon: _currentIndex == 2 ? Icons.video_library : Icons.video_library_outlined,
              label: 'الفيديوهات',
              isActive: _currentIndex == 2,
              onTap: () => _navigateToPage(2),
            ),
          ),
          // الأشخاص
          Expanded(
            child: _buildBottomNavIcon(
              icon: _currentIndex == 3 ? Icons.people : Icons.people_outline,
              label: 'الأشخاص',
              isActive: _currentIndex == 3,
              onTap: () => _navigateToPage(3),
            ),
          ),
        ],
      ),
    );
  }

  // أيقونة التنقل السفلي
  Widget _buildBottomNavIcon({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 24,
            color: isActive ? AppTheme.primaryColor : Colors.grey[600],
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: isActive ? AppTheme.primaryColor : Colors.grey[600],
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  // زر الإضافة العائم
  Widget _buildCreatePostButton() {
    return FloatingActionButton(
      onPressed: _showCreatePostOptions,
      backgroundColor: AppTheme.primaryColor,
      elevation: 8,
      child: const Icon(
        Icons.add,
        color: Colors.white,
        size: 28,
      ),
    );
  }

  void _navigateToPage(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  // عرض خيارات النشر
  void _showCreatePostOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // العنوان
            const Text(
              'إنشاء منشور جديد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // خيارات النشر
            _buildPostOption(
              icon: Icons.text_fields,
              title: 'منشور نصي',
              subtitle: 'شارك أفكارك مع الأصدقاء',
              color: Colors.blue,
              onTap: () => _createTextPost(),
            ),
            const SizedBox(height: 12),

            _buildPostOption(
              icon: Icons.image,
              title: 'منشور بصورة',
              subtitle: 'شارك صورة مع تعليق',
              color: Colors.green,
              onTap: () => _createImagePost(),
            ),
            const SizedBox(height: 12),

            _buildPostOption(
              icon: Icons.videocam,
              title: 'منشور بفيديو',
              subtitle: 'شارك فيديو مع أصدقائك',
              color: Colors.orange,
              onTap: () => _createVideoPost(),
            ),
            const SizedBox(height: 12),

            _buildPostOption(
              icon: Icons.live_tv,
              title: 'بث مباشر',
              subtitle: 'ابدأ بث مباشر الآن',
              color: Colors.red,
              onTap: () => _startLiveStream(),
            ),
            const SizedBox(height: 12),

            _buildPostOption(
              icon: Icons.mood,
              title: 'إضافة شعور',
              subtitle: 'شارك شعورك مع الأصدقاء',
              color: Colors.purple,
              onTap: () => _addFeeling(),
            ),
            const SizedBox(height: 12),

            _buildPostOption(
              icon: Icons.location_on,
              title: 'إضافة موقع',
              subtitle: 'شارك موقعك الحالي',
              color: Colors.teal,
              onTap: () => _addLocation(),
            ),

            const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  // بناء خيار النشر
  Widget _buildPostOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // دوال النشر
  void _createTextPost() {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreatePostScreen(postType: 'text'),
      ),
    );
  }

  Future<void> _createImagePost() async {
    Navigator.pop(context);
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CreatePostScreen(
            postType: 'image',
            selectedMedia: File(image.path),
          ),
        ),
      );
    }
  }

  Future<void> _createVideoPost() async {
    Navigator.pop(context);
    final ImagePicker picker = ImagePicker();
    final XFile? video = await picker.pickVideo(source: ImageSource.gallery);

    if (video != null && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CreatePostScreen(
            postType: 'video',
            selectedMedia: File(video.path),
          ),
        ),
      );
    }
  }

  void _startLiveStream() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء البث المباشر قريباً!')),
    );
  }

  void _addFeeling() {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreatePostScreen(postType: 'text'),
      ),
    );
  }

  void _addLocation() {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreatePostScreen(postType: 'text'),
      ),
    );
  }
}
