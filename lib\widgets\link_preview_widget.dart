import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/link_preview.dart';

class LinkPreviewWidget extends StatelessWidget {
  final LinkPreview linkPreview;
  final bool showInPost;

  const LinkPreviewWidget({
    super.key,
    required this.linkPreview,
    this.showInPost = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[50],
      ),
      child: InkWell(
        onTap: () => _openLink(context),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المعاينة
            if (linkPreview.imageUrl != null)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Image.network(
                    linkPreview.imageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 48,
                          ),
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Colors.grey[200],
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    },
                  ),
                ),
              ),

            // محتوى المعاينة
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم الموقع مع الأيقونة
                  if (linkPreview.siteName != null)
                    Row(
                      children: [
                        if (linkPreview.favicon != null)
                          Container(
                            width: 16,
                            height: 16,
                            margin: const EdgeInsets.only(left: 8),
                            child: Image.network(
                              linkPreview.favicon!,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.language,
                                  size: 16,
                                  color: Colors.grey,
                                );
                              },
                            ),
                          )
                        else
                          const Icon(
                            Icons.language,
                            size: 16,
                            color: Colors.grey,
                          ),
                        const SizedBox(width: 4),
                        Text(
                          linkPreview.siteName!.toUpperCase(),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),

                  if (linkPreview.siteName != null)
                    const SizedBox(height: 8),

                  // عنوان المعاينة
                  Text(
                    linkPreview.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // وصف المعاينة
                  Text(
                    linkPreview.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      height: 1.3,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // الرابط
                  Text(
                    linkPreview.url,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Color(0xFF1877F2), // أزرق Facebook
                      decoration: TextDecoration.underline,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openLink(BuildContext context) async {
    try {
      final uri = Uri.parse(linkPreview.url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('لا يمكن فتح الرابط: ${linkPreview.url}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح الرابط: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

// Widget لمعاينة الرابط أثناء الكتابة
class LinkPreviewBuilder extends StatefulWidget {
  final String text;
  final Function(LinkPreview?) onPreviewGenerated;

  const LinkPreviewBuilder({
    super.key,
    required this.text,
    required this.onPreviewGenerated,
  });

  @override
  State<LinkPreviewBuilder> createState() => _LinkPreviewBuilderState();
}

class _LinkPreviewBuilderState extends State<LinkPreviewBuilder> {
  LinkPreview? _currentPreview;
  String? _lastUrl;

  @override
  void initState() {
    super.initState();
    _checkForLinks();
  }

  @override
  void didUpdateWidget(LinkPreviewBuilder oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      _checkForLinks();
    }
  }

  void _checkForLinks() {
    final urlRegex = RegExp(
      r'https?://[^\s]+',
      caseSensitive: false,
    );

    final match = urlRegex.firstMatch(widget.text);
    if (match != null) {
      final url = match.group(0)!;
      if (url != _lastUrl) {
        _lastUrl = url;
        _generatePreview(url);
      }
    } else {
      if (_currentPreview != null) {
        setState(() {
          _currentPreview = null;
          _lastUrl = null;
        });
        widget.onPreviewGenerated(null);
      }
    }
  }

  void _generatePreview(String url) async {
    // محاكاة تحميل المعاينة
    await Future.delayed(const Duration(milliseconds: 500));
    
    final preview = LinkPreview.generatePreview(url);
    setState(() {
      _currentPreview = preview;
    });
    widget.onPreviewGenerated(preview);
  }

  @override
  Widget build(BuildContext context) {
    if (_currentPreview == null) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        const SizedBox(height: 8),
        LinkPreviewWidget(
          linkPreview: _currentPreview!,
          showInPost: false,
        ),
      ],
    );
  }
}
