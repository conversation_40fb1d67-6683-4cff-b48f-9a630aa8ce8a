# تعليمات التثبيت - تطبيق Arzawo 📲

## تثبيت التطبيق على Android 🤖

### الطريقة الأولى: تثبيت APK الجاهز (الأسرع) ⚡

#### 1. تحميل ملف APK
- انتقل إلى مجلد: `build/app/outputs/flutter-apk/`
- ستجد ملف: `app-release.apk` (حجم: 20.8 MB)
- انسخ الملف إلى هاتفك الذكي

#### 2. تفعيل تثبيت التطبيقات من مصادر غير معروفة
**Android 8.0 وأحدث:**
1. اذهب إلى **الإعدادات** > **الأمان والخصوصية**
2. اختر **تثبيت التطبيقات من مصادر غير معروفة**
3. فعّل الخيار للمتصفح أو مدير الملفات

**Android 7.0 وأقدم:**
1. اذه<PERSON> إلى **الإعدادات** > **الأمان**
2. فعّل **مصادر غير معروفة**

#### 3. تثبيت التطبيق
1. افتح **مدير الملفات** في هاتفك
2. انتقل إلى مكان ملف APK
3. اضغط على `app-release.apk`
4. اضغط **تثبيت**
5. انتظر حتى انتهاء التثبيت
6. اضغط **فتح** لتشغيل التطبيق

### الطريقة الثانية: بناء التطبيق من المصدر 🛠️

#### المتطلبات الأساسية
- **Flutter SDK** (الإصدار 3.7.2 أو أحدث)
- **Android Studio** أو **VS Code**
- **Git** لاستنساخ المشروع
- **جهاز Android** أو **محاكي Android**

#### خطوات التثبيت

##### 1. تثبيت Flutter
```bash
# تحميل Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable

# إضافة Flutter إلى PATH
export PATH="$PATH:`pwd`/flutter/bin"

# التحقق من التثبيت
flutter doctor
```

##### 2. إعداد Android Studio
1. حمّل وثبّت **Android Studio**
2. افتح Android Studio
3. اذهب إلى **SDK Manager**
4. ثبّت **Android SDK** و **Android SDK Build-Tools**
5. ثبّت **Android Emulator** (اختياري)

##### 3. استنساخ المشروع
```bash
# استنساخ المشروع (استبدل URL بالرابط الصحيح)
git clone <repository-url>
cd arzawo

# أو إذا كان لديك المجلد محلياً
cd path/to/arzawo
```

##### 4. تثبيت التبعيات
```bash
# تثبيت حزم Flutter
flutter pub get

# التحقق من عدم وجود مشاكل
flutter doctor
```

##### 5. تشغيل التطبيق
```bash
# تشغيل على جهاز متصل أو محاكي
flutter run

# أو بناء APK للتثبيت
flutter build apk --release
```

## تشغيل التطبيق لأول مرة 🎯

### 1. فتح التطبيق
- ابحث عن أيقونة **Arzawo** في قائمة التطبيقات
- اضغط على الأيقونة لفتح التطبيق

### 2. تسجيل الدخول
- ستظهر شاشة تسجيل الدخول
- أدخل بيانات الاختبار:
  - **البريد الإلكتروني**: `<EMAIL>`
  - **كلمة المرور**: `password123`
- اضغط **تسجيل الدخول**

### 3. استكشاف التطبيق
- ستظهر قائمة المحادثات مع 5 مستخدمين تجريبيين
- اضغط على أي محادثة لبدء الدردشة
- جرب إرسال رسائل مختلفة

## استكشاف الأخطاء 🔧

### مشاكل التثبيت

#### خطأ: "التطبيق غير مثبت"
**الأسباب المحتملة:**
- عدم تفعيل "مصادر غير معروفة"
- ملف APK تالف
- مساحة تخزين غير كافية

**الحلول:**
1. تأكد من تفعيل تثبيت التطبيقات من مصادر غير معروفة
2. أعد تحميل ملف APK
3. احذف تطبيقات غير مستخدمة لتوفير مساحة

#### خطأ: "فشل في التحليل"
**الحل:**
- تأكد من أن ملف APK غير تالف
- أعد تحميل الملف من المصدر
- جرب تثبيت الملف من مكان مختلف

### مشاكل التشغيل

#### التطبيق لا يفتح
**الحلول:**
1. أعد تشغيل الهاتف
2. امسح ذاكرة التخزين المؤقت للتطبيق
3. أعد تثبيت التطبيق

#### التطبيق بطيء
**الحلول:**
1. أغلق التطبيقات الأخرى
2. أعد تشغيل الهاتف
3. تأكد من وجود ذاكرة RAM كافية

### مشاكل Flutter (للمطورين)

#### خطأ: "Flutter command not found"
```bash
# تأكد من إضافة Flutter إلى PATH
export PATH="$PATH:/path/to/flutter/bin"

# أو أضف إلى ~/.bashrc أو ~/.zshrc
echo 'export PATH="$PATH:/path/to/flutter/bin"' >> ~/.bashrc
```

#### خطأ: "Android SDK not found"
```bash
# تعيين متغير ANDROID_HOME
export ANDROID_HOME=/path/to/android/sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

#### خطأ في التبعيات
```bash
# تنظيف المشروع
flutter clean

# إعادة تثبيت التبعيات
flutter pub get

# إعادة بناء المشروع
flutter build apk --release
```

## متطلبات النظام 📋

### الحد الأدنى للمتطلبات
- **نظام التشغيل**: Android 5.0 (API level 21) أو أحدث
- **ذاكرة RAM**: 2 GB أو أكثر
- **مساحة التخزين**: 50 MB مساحة فارغة
- **المعالج**: ARM أو x86

### المتطلبات الموصى بها
- **نظام التشغيل**: Android 8.0 أو أحدث
- **ذاكرة RAM**: 4 GB أو أكثر
- **مساحة التخزين**: 100 MB مساحة فارغة
- **اتصال الإنترنت**: للتحديثات المستقبلية

## الأمان والخصوصية 🔒

### أذونات التطبيق
التطبيق يطلب الأذونات التالية:
- **التخزين**: لحفظ بيانات المحادثات محلياً
- **الإنترنت**: للتحديثات المستقبلية (حالياً غير مطلوب)

### حماية البيانات
- جميع البيانات محفوظة محلياً على الجهاز
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- البيانات مشفرة باستخدام SharedPreferences

## الدعم الفني 📞

### للحصول على المساعدة:
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: www.arzawo.com
- **التوثيق**: راجع ملفات README.md و USER_GUIDE.md

### الإبلاغ عن المشاكل:
عند الإبلاغ عن مشكلة في التثبيت، يرجى تضمين:
- نوع الجهاز وإصدار Android
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة
- لقطات شاشة للخطأ

---

**مبروك! أصبح تطبيق Arzawo جاهزاً للاستخدام على جهازك.** 🎉
