import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../providers/social_provider.dart';
import '../theme/app_theme.dart';

class GroupMembersTab extends StatefulWidget {
  final Group group;

  const GroupMembersTab({
    super.key,
    required this.group,
  });

  @override
  State<GroupMembersTab> createState() => _GroupMembersTabState();
}

class _GroupMembersTabState extends State<GroupMembersTab> {
  String _selectedFilter = 'all'; // all, admins, moderators, members
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredMembers = _getFilteredMembers();

    return Column(
      children: [
        // شريط البحث والفلاتر
        _buildSearchAndFilters(),
        
        // إحصائيات الأعضاء
        _buildMemberStats(),
        
        // قائمة الأعضاء
        Expanded(
          child: filteredMembers.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: filteredMembers.length,
                  itemBuilder: (context, index) {
                    final member = filteredMembers[index];
                    return _buildMemberCard(member);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن عضو...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 12),
          
          // فلاتر الأعضاء
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'الكل', widget.group.memberCount),
                _buildFilterChip('admins', 'المديرون', widget.group.admins.length),
                _buildFilterChip('moderators', 'المشرفون', _getModeratorsCount()),
                _buildFilterChip('members', 'الأعضاء', _getRegularMembersCount()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label, int count) {
    final isSelected = _selectedFilter == value;
    
    return Container(
      margin: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text('$label ($count)'),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = value;
          });
        },
        selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
        checkmarkColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildMemberStats() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.people,
              title: 'إجمالي الأعضاء',
              value: '${widget.group.memberCount}',
              color: AppTheme.primaryColor,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.admin_panel_settings,
              title: 'المديرون',
              value: '${widget.group.admins.length}',
              color: Colors.orange,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.verified_user,
              title: 'المشرفون',
              value: '${_getModeratorsCount()}',
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMemberCard(GroupMember member) {
    final isCurrentUser = member.userId == 'current_user';
    final isUserAdmin = widget.group.isUserAdmin('current_user');
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getRoleColor(member.role),
          child: Text(
            _getUserName(member.userId)[0].toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        title: Row(
          children: [
            Expanded(
              child: Text(
                _getUserName(member.userId),
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (isCurrentUser)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'أنت',
                  style: TextStyle(
                    fontSize: 10,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getRoleIcon(member.role),
                  size: 14,
                  color: _getRoleColor(member.role),
                ),
                const SizedBox(width: 4),
                Text(
                  _getRoleText(member.role),
                  style: TextStyle(
                    color: _getRoleColor(member.role),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            Text(
              'انضم ${_formatJoinDate(member.joinedAt)}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 11,
              ),
            ),
          ],
        ),
        
        trailing: isUserAdmin && !isCurrentUser
            ? PopupMenuButton<String>(
                onSelected: (value) => _handleMemberAction(value, member),
                itemBuilder: (context) => [
                  if (member.role != GroupMemberRole.admin)
                    const PopupMenuItem(
                      value: 'promote',
                      child: ListTile(
                        leading: Icon(Icons.arrow_upward),
                        title: Text('ترقية'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  if (member.role != GroupMemberRole.member)
                    const PopupMenuItem(
                      value: 'demote',
                      child: ListTile(
                        leading: Icon(Icons.arrow_downward),
                        title: Text('تخفيض الرتبة'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'message',
                    child: ListTile(
                      leading: Icon(Icons.message),
                      title: Text('إرسال رسالة'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'remove',
                    child: ListTile(
                      leading: Icon(Icons.remove_circle, color: Colors.red),
                      title: Text('إزالة من المجموعة', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              )
            : null,
        
        onTap: () => _showMemberProfile(member),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد أعضاء',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أعضاء بالمعايير المحددة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<GroupMember> _getFilteredMembers() {
    List<GroupMember> members = widget.group.activeMembers;
    
    // تطبيق فلتر الدور
    switch (_selectedFilter) {
      case 'admins':
        members = members.where((m) => m.role == GroupMemberRole.admin).toList();
        break;
      case 'moderators':
        members = members.where((m) => m.role == GroupMemberRole.moderator).toList();
        break;
      case 'members':
        members = members.where((m) => m.role == GroupMemberRole.member).toList();
        break;
    }
    
    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      members = members.where((member) {
        final userName = _getUserName(member.userId).toLowerCase();
        return userName.contains(_searchQuery.toLowerCase());
      }).toList();
    }
    
    // ترتيب الأعضاء (المديرون أولاً، ثم المشرفون، ثم الأعضاء)
    members.sort((a, b) {
      final roleOrder = {
        GroupMemberRole.admin: 0,
        GroupMemberRole.moderator: 1,
        GroupMemberRole.member: 2,
      };
      
      final roleComparison = roleOrder[a.role]!.compareTo(roleOrder[b.role]!);
      if (roleComparison != 0) return roleComparison;
      
      // إذا كان نفس الدور، رتب حسب تاريخ الانضمام
      return a.joinedAt.compareTo(b.joinedAt);
    });
    
    return members;
  }

  int _getModeratorsCount() {
    return widget.group.members
        .where((m) => m.role == GroupMemberRole.moderator && m.isActive)
        .length;
  }

  int _getRegularMembersCount() {
    return widget.group.members
        .where((m) => m.role == GroupMemberRole.member && m.isActive)
        .length;
  }

  Color _getRoleColor(GroupMemberRole role) {
    switch (role) {
      case GroupMemberRole.owner:
        return Colors.purple;
      case GroupMemberRole.admin:
        return Colors.red;
      case GroupMemberRole.moderator:
        return Colors.orange;
      case GroupMemberRole.member:
        return Colors.blue;
    }
  }

  IconData _getRoleIcon(GroupMemberRole role) {
    switch (role) {
      case GroupMemberRole.owner:
        return Icons.star;
      case GroupMemberRole.admin:
        return Icons.admin_panel_settings;
      case GroupMemberRole.moderator:
        return Icons.verified_user;
      case GroupMemberRole.member:
        return Icons.person;
    }
  }

  String _getRoleText(GroupMemberRole role) {
    switch (role) {
      case GroupMemberRole.owner:
        return 'مالك المجموعة';
      case GroupMemberRole.admin:
        return 'مدير';
      case GroupMemberRole.moderator:
        return 'مشرف';
      case GroupMemberRole.member:
        return 'عضو';
    }
  }

  String _getUserName(String userId) {
    switch (userId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  String _formatJoinDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays < 1) {
      return 'اليوم';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      return 'منذ ${(difference.inDays / 7).floor()} أسابيع';
    } else {
      return 'منذ ${(difference.inDays / 30).floor()} شهور';
    }
  }

  void _handleMemberAction(String action, GroupMember member) {
    switch (action) {
      case 'promote':
        _promoteMember(member);
        break;
      case 'demote':
        _demoteMember(member);
        break;
      case 'message':
        _sendMessage(member);
        break;
      case 'remove':
        _removeMember(member);
        break;
    }
  }

  void _promoteMember(GroupMember member) {
    // TODO: تنفيذ ترقية العضو
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم ترقية ${_getUserName(member.userId)}')),
    );
  }

  void _demoteMember(GroupMember member) {
    // TODO: تنفيذ تخفيض رتبة العضو
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم تخفيض رتبة ${_getUserName(member.userId)}')),
    );
  }

  void _sendMessage(GroupMember member) {
    // TODO: فتح شاشة الدردشة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إرسال رسالة إلى ${_getUserName(member.userId)}')),
    );
  }

  void _removeMember(GroupMember member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة العضو'),
        content: Text('هل أنت متأكد من إزالة ${_getUserName(member.userId)} من المجموعة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: تنفيذ إزالة العضو
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تم إزالة ${_getUserName(member.userId)}')),
              );
            },
            child: const Text('إزالة', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showMemberProfile(GroupMember member) {
    // TODO: فتح ملف العضو الشخصي
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('ملف ${_getUserName(member.userId)} الشخصي')),
    );
  }
}
