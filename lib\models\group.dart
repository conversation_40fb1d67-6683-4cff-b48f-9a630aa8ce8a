import 'post.dart';
import 'link_preview.dart';
import 'group_models.dart';

// إعادة تصدير الكلاسات من group_models.dart للتوافق مع الكود الموجود
export 'group_models.dart';

// Wrapper class للتوافق مع الكود الموجود
class GroupMember {
  final String userId;
  final GroupMemberRole role;
  final DateTime joinedAt;
  final bool isActive;

  const GroupMember({
    required this.userId,
    required this.role,
    required this.joinedAt,
    this.isActive = true,
  });

  GroupMember copyWith({
    String? userId,
    GroupMemberRole? role,
    DateTime? joinedAt,
    bool? isActive,
  }) {
    return GroupMember(
      userId: userId ?? this.userId,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'role': role.name,
      'joinedAt': joinedAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  factory GroupMember.fromJson(Map<String, dynamic> json) {
    return GroupMember(
      userId: json['userId'],
      role: GroupMemberRole.values.firstWhere((e) => e.name == json['role']),
      joinedAt: DateTime.parse(json['joinedAt']),
      isActive: json['isActive'] ?? true,
    );
  }
}

class Group {
  final String id;
  final String name;
  final String description;
  final String? coverImage;
  final String? profileImage;
  final GroupPrivacy privacy;
  final String createdBy;
  final DateTime createdAt;
  final List<GroupMember> members;
  final List<String> tags;
  final String? location;
  final bool isActive;
  final int postCount;
  final DateTime lastActivity;
  final bool requiresPostApproval;

  const Group({
    required this.id,
    required this.name,
    required this.description,
    this.coverImage,
    this.profileImage,
    this.privacy = GroupPrivacy.public,
    required this.createdBy,
    required this.createdAt,
    this.members = const [],
    this.tags = const [],
    this.location,
    this.isActive = true,
    this.postCount = 0,
    required this.lastActivity,
    this.requiresPostApproval = false,
  });

  int get memberCount => members.where((m) => m.isActive).length;
  
  List<GroupMember> get activeMembers => 
      members.where((m) => m.isActive).toList();
  
  List<GroupMember> get admins => 
      members.where((m) => m.role == GroupMemberRole.admin && m.isActive).toList();
  
  bool isUserMember(String userId) => 
      members.any((m) => m.userId == userId && m.isActive);
  
  bool isUserAdmin(String userId) => 
      members.any((m) => m.userId == userId && 
                        m.role == GroupMemberRole.admin && 
                        m.isActive);

  Group copyWith({
    String? id,
    String? name,
    String? description,
    String? coverImage,
    String? profileImage,
    GroupPrivacy? privacy,
    String? createdBy,
    DateTime? createdAt,
    List<GroupMember>? members,
    List<String>? tags,
    String? location,
    bool? isActive,
    int? postCount,
    DateTime? lastActivity,
    bool? requiresPostApproval,
  }) {
    return Group(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      coverImage: coverImage ?? this.coverImage,
      profileImage: profileImage ?? this.profileImage,
      privacy: privacy ?? this.privacy,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      members: members ?? this.members,
      tags: tags ?? this.tags,
      location: location ?? this.location,
      isActive: isActive ?? this.isActive,
      postCount: postCount ?? this.postCount,
      lastActivity: lastActivity ?? this.lastActivity,
      requiresPostApproval: requiresPostApproval ?? this.requiresPostApproval,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'coverImage': coverImage,
      'profileImage': profileImage,
      'privacy': privacy.name,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'members': members.map((m) => m.toJson()).toList(),
      'tags': tags,
      'location': location,
      'isActive': isActive,
      'postCount': postCount,
      'lastActivity': lastActivity.toIso8601String(),
      'requiresPostApproval': requiresPostApproval,
    };
  }

  factory Group.fromJson(Map<String, dynamic> json) {
    return Group(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      coverImage: json['coverImage'],
      profileImage: json['profileImage'],
      privacy: GroupPrivacy.values.firstWhere(
        (e) => e.name == json['privacy'],
        orElse: () => GroupPrivacy.public,
      ),
      createdBy: json['createdBy'],
      createdAt: DateTime.parse(json['createdAt']),
      members: (json['members'] as List?)
          ?.map((m) => GroupMember.fromJson(m))
          .toList() ?? [],
      tags: List<String>.from(json['tags'] ?? []),
      location: json['location'],
      isActive: json['isActive'] ?? true,
      postCount: json['postCount'] ?? 0,
      lastActivity: DateTime.parse(json['lastActivity']),
      requiresPostApproval: json['requiresPostApproval'] ?? false,
    );
  }
}

class GroupPost extends Post {
  final bool isPinned;
  final bool isApproved;

  const GroupPost({
    required super.id,
    required super.authorId,
    required super.content,
    super.type,
    super.media,
    super.background,
    super.taggedUsers,
    super.feeling,
    super.activity,
    super.location,
    super.feelingDisplay,
    super.activityDisplay,
    super.locationDisplay,
    required super.timestamp,
    super.reactions,
    super.comments,
    super.shareCount,
    super.shares,
    super.reposts,
    super.isPublic,
    super.linkPreview,
    required super.groupId,
    this.isPinned = false,
    this.isApproved = true,
  });

  @override
  GroupPost copyWith({
    String? id,
    String? authorId,
    String? content,
    PostType? type,
    List<PostMedia>? media,
    PostBackground? background,
    List<String>? taggedUsers,
    String? feeling,
    String? activity,
    String? location,
    String? feelingDisplay,
    String? activityDisplay,
    String? locationDisplay,
    DateTime? timestamp,
    List<PostReaction>? reactions,
    List<PostComment>? comments,
    int? shareCount,
    List<PostShare>? shares,
    List<PostRepost>? reposts,
    bool? isPublic,
    LinkPreview? linkPreview,
    String? groupId,
    bool? isPinned,
    bool? isApproved,
  }) {
    return GroupPost(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      content: content ?? this.content,
      type: type ?? this.type,
      media: media ?? this.media,
      background: background ?? this.background,
      taggedUsers: taggedUsers ?? this.taggedUsers,
      feeling: feeling ?? this.feeling,
      activity: activity ?? this.activity,
      location: location ?? this.location,
      feelingDisplay: feelingDisplay ?? this.feelingDisplay,
      activityDisplay: activityDisplay ?? this.activityDisplay,
      locationDisplay: locationDisplay ?? this.locationDisplay,
      timestamp: timestamp ?? this.timestamp,
      reactions: reactions ?? this.reactions,
      comments: comments ?? this.comments,
      shareCount: shareCount ?? this.shareCount,
      shares: shares ?? this.shares,
      reposts: reposts ?? this.reposts,
      isPublic: isPublic ?? this.isPublic,
      linkPreview: linkPreview ?? this.linkPreview,
      groupId: groupId ?? this.groupId,
      isPinned: isPinned ?? this.isPinned,
      isApproved: isApproved ?? this.isApproved,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'groupId': groupId,
      'isPinned': isPinned,
      'isApproved': isApproved,
    });
    return json;
  }

  factory GroupPost.fromJson(Map<String, dynamic> json) {
    final post = Post.fromJson(json);
    return GroupPost(
      id: post.id,
      authorId: post.authorId,
      content: post.content,
      type: post.type,
      media: post.media,
      background: post.background,
      taggedUsers: post.taggedUsers,
      feeling: post.feeling,
      activity: post.activity,
      location: post.location,
      timestamp: post.timestamp,
      reactions: post.reactions,
      comments: post.comments,
      shareCount: post.shareCount,
      isPublic: post.isPublic,
      groupId: json['groupId'],
      isPinned: json['isPinned'] ?? false,
      isApproved: json['isApproved'] ?? true,
    );
  }
}
