import 'user.dart';
import 'message.dart';

enum ChatType {
  individual, // محادثة فردية
  group,      // محادثة جماعية
}

class Chat {
  final String id;
  final User otherUser;
  final Message? lastMessage;
  final int unreadCount;
  final DateTime lastActivity;
  final ChatType type;
  final bool isPinned;
  final bool isMuted;
  final bool isTyping;
  final DateTime? lastSeen;
  final List<String>? groupMembers; // للمحادثات الجماعية
  final String? groupName;
  final String? groupAvatar;

  const Chat({
    required this.id,
    required this.otherUser,
    this.lastMessage,
    this.unreadCount = 0,
    required this.lastActivity,
    this.type = ChatType.individual,
    this.isPinned = false,
    this.isMuted = false,
    this.isTyping = false,
    this.lastSeen,
    this.groupMembers,
    this.groupName,
    this.groupAvatar,
  });

  Chat copyWith({
    String? id,
    User? otherUser,
    Message? lastMessage,
    int? unreadCount,
    DateTime? lastActivity,
    ChatType? type,
    bool? isPinned,
    bool? isMuted,
    bool? isTyping,
    DateTime? lastSeen,
    List<String>? groupMembers,
    String? groupName,
    String? groupAvatar,
  }) {
    return Chat(
      id: id ?? this.id,
      otherUser: otherUser ?? this.otherUser,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
      lastActivity: lastActivity ?? this.lastActivity,
      type: type ?? this.type,
      isPinned: isPinned ?? this.isPinned,
      isMuted: isMuted ?? this.isMuted,
      isTyping: isTyping ?? this.isTyping,
      lastSeen: lastSeen ?? this.lastSeen,
      groupMembers: groupMembers ?? this.groupMembers,
      groupName: groupName ?? this.groupName,
      groupAvatar: groupAvatar ?? this.groupAvatar,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'otherUser': otherUser.toJson(),
      'lastMessage': lastMessage?.toJson(),
      'unreadCount': unreadCount,
      'lastActivity': lastActivity.toIso8601String(),
      'type': type.name,
      'isPinned': isPinned,
      'isMuted': isMuted,
      'isTyping': isTyping,
      'lastSeen': lastSeen?.toIso8601String(),
      'groupMembers': groupMembers,
      'groupName': groupName,
      'groupAvatar': groupAvatar,
    };
  }

  factory Chat.fromJson(Map<String, dynamic> json) {
    return Chat(
      id: json['id'],
      otherUser: User.fromJson(json['otherUser']),
      lastMessage: json['lastMessage'] != null
          ? Message.fromJson(json['lastMessage'])
          : null,
      unreadCount: json['unreadCount'] ?? 0,
      lastActivity: DateTime.parse(json['lastActivity']),
      type: ChatType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ChatType.individual,
      ),
      isPinned: json['isPinned'] ?? false,
      isMuted: json['isMuted'] ?? false,
      isTyping: json['isTyping'] ?? false,
      lastSeen: json['lastSeen'] != null
          ? DateTime.parse(json['lastSeen'])
          : null,
      groupMembers: json['groupMembers']?.cast<String>(),
      groupName: json['groupName'],
      groupAvatar: json['groupAvatar'],
    );
  }
}
