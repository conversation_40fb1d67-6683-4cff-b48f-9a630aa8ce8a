import 'package:flutter/material.dart';
import '../models/post.dart';
import '../models/reaction_types.dart';
import 'professional_reactions.dart';

class StackedReactionsWidget extends StatefulWidget {
  final List<PostReaction> reactions;
  final VoidCallback? onTap;

  const StackedReactionsWidget({
    super.key,
    required this.reactions,
    this.onTap,
  });

  @override
  State<StackedReactionsWidget> createState() => _StackedReactionsWidgetState();
}

class _StackedReactionsWidgetState extends State<StackedReactionsWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticInOut,
    ));

    // بدء الانيميشن عند إنشاء الويدجت
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.reactions.isEmpty) return const SizedBox.shrink();

    // تجميع التفاعلات حسب النوع
    final reactionCounts = <String, int>{};
    for (final reaction in widget.reactions) {
      reactionCounts[reaction.type] = (reactionCounts[reaction.type] ?? 0) + 1;
    }

    // ترتيب التفاعلات حسب الشيوع
    final sortedReactions = reactionCounts.entries.toList();
    sortedReactions.sort((a, b) => b.value.compareTo(a.value));

    // أخذ أكثر 3 تفاعلات شيوعاً
    final topReactions = sortedReactions.take(3).toList();
    final totalCount = widget.reactions.length;

    return GestureDetector(
      onTap: () {
        _animateOnTap();
        widget.onTap?.call();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // التفاعلات المتراكبة
              SizedBox(
                width: _calculateStackWidth(topReactions.length),
                height: 24,
                child: Stack(
                  children: [
                    for (int i = 0; i < topReactions.length; i++)
                      _buildStackedReaction(
                        topReactions[i],
                        i,
                        topReactions.length,
                      ),
                  ],
                ),
              ),

              const SizedBox(width: 6),

              // عدد التفاعلات مع انيميشن
              Transform.scale(
                scale: _bounceAnimation.value,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '$totalCount',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStackedReaction(
    MapEntry<String, int> reactionEntry,
    int index,
    int totalReactions,
  ) {
    final reactionType = ReactionType.values.firstWhere(
      (type) => type.name == reactionEntry.key,
      orElse: () => ReactionType.like,
    );

    // حساب الموضع والعمق
    final leftOffset = index * 12.0; // تداخل 12 بكسل
    final scale = 1.0 - (index * 0.05); // تصغير تدريجي

    return Positioned(
      left: leftOffset,
      child: Transform.scale(
        scale: _scaleAnimation.value * scale,
        child: ProfessionalReactionIcon(
          type: reactionType,
          size: 24,
          isSelected: false,
          showShadow: true,
        ),
      ),
    );
  }

  double _calculateStackWidth(int reactionCount) {
    if (reactionCount == 0) return 0;
    if (reactionCount == 1) return 24;
    return 24 + ((reactionCount - 1) * 12);
  }

  void _animateOnTap() {
    _animationController.reset();
    _animationController.forward();
  }
}

// Widget للتفاعلات المنبثقة عند النقر
class ReactionsPopup extends StatefulWidget {
  final List<PostReaction> reactions;
  final Offset position;
  final VoidCallback onClose;

  const ReactionsPopup({
    super.key,
    required this.reactions,
    required this.position,
    required this.onClose,
  });

  @override
  State<ReactionsPopup> createState() => _ReactionsPopupState();
}

class _ReactionsPopupState extends State<ReactionsPopup>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تجميع التفاعلات
    final reactionCounts = <String, List<PostReaction>>{};
    for (final reaction in widget.reactions) {
      if (reactionCounts[reaction.type] == null) {
        reactionCounts[reaction.type] = [];
      }
      reactionCounts[reaction.type]!.add(reaction);
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              constraints: const BoxConstraints(
                maxWidth: 300,
                maxHeight: 400,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رأس النافذة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Text(
                          'التفاعلات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: widget.onClose,
                          icon: const Icon(Icons.close),
                          iconSize: 20,
                        ),
                      ],
                    ),
                  ),

                  // قائمة التفاعلات
                  Flexible(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: reactionCounts.length,
                      itemBuilder: (context, index) {
                        final entry = reactionCounts.entries.elementAt(index);
                        final reactionType = ReactionType.values.firstWhere(
                          (type) => type.name == entry.key,
                          orElse: () => ReactionType.like,
                        );
                        final reactionData = ReactionData.getReaction(reactionType);

                        return ListTile(
                          leading: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: reactionData.color,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                reactionData.emoji,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                          title: Text(
                            reactionData.name,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: reactionData.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${entry.value.length}',
                              style: TextStyle(
                                color: reactionData.color,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
