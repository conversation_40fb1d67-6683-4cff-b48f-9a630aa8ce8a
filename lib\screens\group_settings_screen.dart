import 'package:flutter/material.dart';
import '../models/group.dart';
// import '../widgets/group_rules_manager.dart';
// import '../widgets/group_questions_manager.dart';
// import '../widgets/group_privacy_settings.dart';
// import '../widgets/group_post_settings.dart';

class GroupSettingsScreen extends StatefulWidget {
  final Group group;

  const GroupSettingsScreen({
    super.key,
    required this.group,
  });

  @override
  State<GroupSettingsScreen> createState() => _GroupSettingsScreenState();
}

class _GroupSettingsScreenState extends State<GroupSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  
  bool _commentsEnabled = true;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // تهيئة القيم الحالية
    _nameController.text = widget.group.name;
    _descriptionController.text = widget.group.description;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'إعدادات المجموعة',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: Colors.blue,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          tabs: const [
            Tab(text: 'عام'),
            Tab(text: 'الخصوصية'),
            Tab(text: 'المنشورات'),
            Tab(text: 'القوانين'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGeneralSettings(),
          _buildPrivacySettings(),
          _buildPostSettings(),
          _buildRulesSettings(),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة الغلاف
          _buildSectionTitle('صورة الغلاف'),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
              image: widget.group.coverImage != null
                  ? DecorationImage(
                      image: NetworkImage(widget.group.coverImage!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: widget.group.coverImage == null
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_photo_alternate,
                          size: 48,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'إضافة صورة غلاف',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  )
                : Stack(
                    children: [
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.edit,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: _changeCoverImage,
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
          
          const SizedBox(height: 24),
          
          // صورة المجموعة
          _buildSectionTitle('صورة المجموعة'),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  shape: BoxShape.circle,
                  image: widget.group.profileImage != null
                      ? DecorationImage(
                          image: NetworkImage(widget.group.profileImage!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: widget.group.profileImage == null
                    ? const Icon(
                        Icons.group,
                        size: 40,
                        color: Colors.grey,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _changeProfileImage,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('تغيير الصورة'),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // اسم المجموعة
          _buildSectionTitle('اسم المجموعة'),
          const SizedBox(height: 8),
          TextField(
            controller: _nameController,
            decoration: InputDecoration(
              hintText: 'أدخل اسم المجموعة',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            maxLength: 100,
          ),
          
          const SizedBox(height: 16),
          
          // وصف المجموعة
          _buildSectionTitle('وصف المجموعة'),
          const SizedBox(height: 8),
          TextField(
            controller: _descriptionController,
            decoration: InputDecoration(
              hintText: 'أدخل وصف المجموعة',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            maxLines: 4,
            maxLength: 500,
          ),
          
          const SizedBox(height: 24),
          
          // إحصائيات المجموعة
          _buildSectionTitle('إحصائيات المجموعة'),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildStatRow('الأعضاء', '${widget.group.memberCount}'),
                  _buildStatRow('المنشورات', '${widget.group.postCount}'),
                  _buildStatRow('المديرين', '2'),
                  _buildStatRow('المشرفين', '3'),
                  _buildStatRow('طلبات الانضمام', '5'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.privacy_tip,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'إعدادات الخصوصية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ميزة إعدادات الخصوصية قريباً!',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إعدادات المنشورات'),
          const SizedBox(height: 16),

          // إعدادات التعليقات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إعدادات التعليقات',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  SwitchListTile(
                    title: const Text('السماح بالتعليقات'),
                    subtitle: const Text('يمكن للأعضاء التعليق على المنشورات'),
                    value: _commentsEnabled,
                    onChanged: (value) {
                      setState(() {
                        _commentsEnabled = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // إعدادات أخرى
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(
                    Icons.settings,
                    size: 48,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'إعدادات إضافية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'المزيد من إعدادات المنشورات قريباً!',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRulesSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('قوانين المجموعة'),
          const SizedBox(height: 8),
          const Text(
            'يمكنك إضافة قوانين للمجموعة لتوضيح السلوك المقبول',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(
                    Icons.rule,
                    size: 48,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'لا توجد قوانين بعد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('ميزة إدارة القوانين قريباً!')),
                      );
                    },
                    child: const Text('إضافة قانون'),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          _buildSectionTitle('أسئلة الانضمام'),
          const SizedBox(height: 8),
          const Text(
            'يمكنك إضافة أسئلة يجب على الأعضاء الجدد الإجابة عليها',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(
                    Icons.quiz,
                    size: 48,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'لا توجد أسئلة بعد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('ميزة إدارة الأسئلة قريباً!')),
                      );
                    },
                    child: const Text('إضافة سؤال'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  void _changeCoverImage() {
    // تنفيذ تغيير صورة الغلاف
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة تغيير صورة الغلاف قريباً!')),
    );
  }

  void _changeProfileImage() {
    // تنفيذ تغيير صورة المجموعة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة تغيير صورة المجموعة قريباً!')),
    );
  }

  Future<void> _saveSettings() async {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال اسم المجموعة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // محاكاة حفظ الإعدادات
    await Future.delayed(const Duration(seconds: 1));

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الإعدادات بنجاح! ✅'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.pop(context);
    }
  }


}
