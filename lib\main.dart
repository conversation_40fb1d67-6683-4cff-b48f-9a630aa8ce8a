import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'providers/auth_provider.dart';
import 'providers/messenger_provider.dart';
import 'providers/social_provider.dart';
import 'providers/notification_provider.dart';
import 'providers/comments_provider.dart';
import 'providers/live_stream_provider.dart';
import 'providers/saved_items_provider.dart';
import 'providers/app_settings_provider.dart';
import 'providers/connectivity_provider.dart';
import 'screens/facebook_login_screen.dart';
import 'services/auth_service.dart';
import 'screens/main_navigation_screen.dart';
import 'theme/app_theme.dart';

void main() {
  // إعداد timeago للغة العربية
  timeago.setLocaleMessages('ar', timeago.ArMessages());

  runApp(const ArzawoApp());
}

class ArzawoApp extends StatelessWidget {
  const A<PERSON>awoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => MessengerProvider()),
        ChangeNotifierProvider(create: (_) => SocialProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => CommentsProvider()),
        ChangeNotifierProvider(create: (_) => LiveStreamProvider()),
        ChangeNotifierProvider(create: (_) => SavedItemsProvider()),
        ChangeNotifierProvider(create: (_) => AppSettingsProvider()),
        ChangeNotifierProvider(create: (_) => ConnectivityProvider()),
      ],
      child: Consumer<AppSettingsProvider>(
        builder: (context, settingsProvider, child) {
          return MaterialApp(
            title: 'Arzawo',
            debugShowCheckedModeBanner: false,
            theme: _buildLightTheme(settingsProvider),
            darkTheme: _buildDarkTheme(settingsProvider),
            themeMode: _getThemeMode(settingsProvider.themeMode),

            // إعداد التدويل
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar'), // العربية
            ],
            locale: const Locale('ar'),

            // إعداد اتجاه النص
            builder: (context, child) {
              return Directionality(
                textDirection: TextDirection.rtl,
                child: child!,
              );
            },

            home: const AuthWrapper(),
          );
        },
      ),
    );
  }

  ThemeData _buildLightTheme(AppSettingsProvider settingsProvider) {
    final fontSizeMultiplier = settingsProvider.settings.fontSizeMultiplier;

    return ThemeData(
      brightness: Brightness.light,
      primarySwatch: Colors.blue,
      scaffoldBackgroundColor: Colors.white,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(fontSize: 16 * fontSizeMultiplier),
        bodyMedium: TextStyle(fontSize: 14 * fontSizeMultiplier),
        bodySmall: TextStyle(fontSize: 12 * fontSizeMultiplier),
        headlineLarge: TextStyle(
          fontSize: 32 * fontSizeMultiplier,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          fontSize: 28 * fontSizeMultiplier,
          fontWeight: FontWeight.bold,
        ),
        headlineSmall: TextStyle(
          fontSize: 24 * fontSizeMultiplier,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  ThemeData _buildDarkTheme(AppSettingsProvider settingsProvider) {
    final fontSizeMultiplier = settingsProvider.settings.fontSizeMultiplier;

    return ThemeData(
      brightness: Brightness.dark,
      primarySwatch: Colors.blue,
      scaffoldBackgroundColor: Colors.grey[900],
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.grey[900],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(
          fontSize: 16 * fontSizeMultiplier,
          color: Colors.white,
        ),
        bodyMedium: TextStyle(
          fontSize: 14 * fontSizeMultiplier,
          color: Colors.white,
        ),
        bodySmall: TextStyle(
          fontSize: 12 * fontSizeMultiplier,
          color: Colors.white70,
        ),
        headlineLarge: TextStyle(
          fontSize: 32 * fontSizeMultiplier,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        headlineMedium: TextStyle(
          fontSize: 28 * fontSizeMultiplier,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        headlineSmall: TextStyle(
          fontSize: 24 * fontSizeMultiplier,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  ThemeMode _getThemeMode(dynamic appThemeMode) {
    // تحويل من app_settings.ThemeMode إلى Flutter ThemeMode
    switch (appThemeMode.toString()) {
      case 'ThemeMode.light':
        return ThemeMode.light;
      case 'ThemeMode.dark':
        return ThemeMode.dark;
      case 'ThemeMode.system':
        return ThemeMode.system;
      default:
        return ThemeMode.system;
    }
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final AuthService _authService = AuthService();
  bool _isLoading = true;
  bool _isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    final isLoggedIn = await _authService.isLoggedIn();
    setState(() {
      _isAuthenticated = isLoggedIn;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // إظهار شاشة التحميل أثناء التحقق من حالة المصادقة
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 80,
                color: AppTheme.primaryColor,
              ),
              SizedBox(height: 24),
              Text(
                'Arzawo',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
              SizedBox(height: 24),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
              ),
            ],
          ),
        ),
      );
    }

    // التنقل بناءً على حالة المصادقة
    if (_isAuthenticated) {
      return const MainNavigationScreen();
    } else {
      return const FacebookLoginScreen();
    }
  }
}