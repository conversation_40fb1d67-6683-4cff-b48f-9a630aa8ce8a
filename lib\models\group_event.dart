enum EventType {
  meeting,
  social,
  educational,
  entertainment,
  sports,
  business,
  other,
}

enum EventStatus {
  upcoming,
  ongoing,
  completed,
  cancelled,
}

class GroupEvent {
  final String id;
  final String groupId;
  final String organizerId;
  final String title;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final String? location;
  final String? onlineLink;
  final EventType type;
  final EventStatus status;
  final String? coverImage;
  final List<String> attendees;
  final List<String> interested;
  final List<String> notGoing;
  final int maxAttendees;
  final bool requiresApproval;
  final DateTime createdAt;

  const GroupEvent({
    required this.id,
    required this.groupId,
    required this.organizerId,
    required this.title,
    required this.description,
    required this.startDate,
    required this.endDate,
    this.location,
    this.onlineLink,
    this.type = EventType.other,
    this.status = EventStatus.upcoming,
    this.coverImage,
    this.attendees = const [],
    this.interested = const [],
    this.notGoing = const [],
    this.maxAttendees = 0,
    this.requiresApproval = false,
    required this.createdAt,
  });

  bool get isOnline => onlineLink != null;
  bool get hasLocationLimit => maxAttendees > 0;
  bool get isFull => hasLocationLimit && attendees.length >= maxAttendees;
  
  int get totalResponses => attendees.length + interested.length + notGoing.length;
  
  bool hasUserResponded(String userId) {
    return attendees.contains(userId) || 
           interested.contains(userId) || 
           notGoing.contains(userId);
  }
  
  String? getUserResponse(String userId) {
    if (attendees.contains(userId)) return 'going';
    if (interested.contains(userId)) return 'interested';
    if (notGoing.contains(userId)) return 'not_going';
    return null;
  }

  GroupEvent copyWith({
    String? id,
    String? groupId,
    String? organizerId,
    String? title,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    String? location,
    String? onlineLink,
    EventType? type,
    EventStatus? status,
    String? coverImage,
    List<String>? attendees,
    List<String>? interested,
    List<String>? notGoing,
    int? maxAttendees,
    bool? requiresApproval,
    DateTime? createdAt,
  }) {
    return GroupEvent(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      organizerId: organizerId ?? this.organizerId,
      title: title ?? this.title,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      location: location ?? this.location,
      onlineLink: onlineLink ?? this.onlineLink,
      type: type ?? this.type,
      status: status ?? this.status,
      coverImage: coverImage ?? this.coverImage,
      attendees: attendees ?? this.attendees,
      interested: interested ?? this.interested,
      notGoing: notGoing ?? this.notGoing,
      maxAttendees: maxAttendees ?? this.maxAttendees,
      requiresApproval: requiresApproval ?? this.requiresApproval,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'organizerId': organizerId,
      'title': title,
      'description': description,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'location': location,
      'onlineLink': onlineLink,
      'type': type.name,
      'status': status.name,
      'coverImage': coverImage,
      'attendees': attendees,
      'interested': interested,
      'notGoing': notGoing,
      'maxAttendees': maxAttendees,
      'requiresApproval': requiresApproval,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory GroupEvent.fromJson(Map<String, dynamic> json) {
    return GroupEvent(
      id: json['id'],
      groupId: json['groupId'],
      organizerId: json['organizerId'],
      title: json['title'],
      description: json['description'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      location: json['location'],
      onlineLink: json['onlineLink'],
      type: EventType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => EventType.other,
      ),
      status: EventStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => EventStatus.upcoming,
      ),
      coverImage: json['coverImage'],
      attendees: List<String>.from(json['attendees'] ?? []),
      interested: List<String>.from(json['interested'] ?? []),
      notGoing: List<String>.from(json['notGoing'] ?? []),
      maxAttendees: json['maxAttendees'] ?? 0,
      requiresApproval: json['requiresApproval'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}
