import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/connectivity_provider.dart';
import '../models/connectivity_status.dart';

class ConnectivityBanner extends StatefulWidget {
  final Widget child;
  final bool showAtTop;

  const ConnectivityBanner({
    super.key,
    required this.child,
    this.showAtTop = true,
  });

  @override
  State<ConnectivityBanner> createState() => _ConnectivityBannerState();
}

class _ConnectivityBannerState extends State<ConnectivityBanner>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: widget.showAtTop ? const Offset(0, -1) : const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _slideController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityProvider>(
      builder: (context, connectivityProvider, child) {
        final shouldShow = connectivityProvider.showConnectionBanner;
        
        // Animate banner visibility
        if (shouldShow) {
          _slideController.forward();
        } else {
          _slideController.reverse();
        }

        return Stack(
          children: [
            // Main content
            widget.child,
            
            // Connection banner
            if (shouldShow)
              Positioned(
                top: widget.showAtTop ? 0 : null,
                bottom: widget.showAtTop ? null : 0,
                left: 0,
                right: 0,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildBanner(connectivityProvider),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildBanner(ConnectivityProvider provider) {
    final networkState = provider.networkState;
    final message = provider.lastConnectionMessage ?? '';
    
    Color backgroundColor;
    Color textColor;
    IconData icon;
    bool showPulse = false;

    switch (networkState.status) {
      case ConnectivityStatus.connected:
        backgroundColor = Colors.green;
        textColor = Colors.white;
        icon = _getConnectionIcon(networkState.type);
        break;
      case ConnectivityStatus.disconnected:
        backgroundColor = Colors.red;
        textColor = Colors.white;
        icon = Icons.wifi_off;
        break;
      case ConnectivityStatus.reconnecting:
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        icon = Icons.wifi_find;
        showPulse = true;
        break;
      case ConnectivityStatus.unknown:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.help_outline;
        break;
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        top: widget.showAtTop ? MediaQuery.of(context).padding.top + 8 : 8,
        bottom: widget.showAtTop ? 8 : MediaQuery.of(context).padding.bottom + 8,
        left: 16,
        right: 16,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: widget.showAtTop ? const Offset(0, 2) : const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: widget.showAtTop,
        bottom: !widget.showAtTop,
        child: Row(
          children: [
            // Connection icon
            showPulse
                ? ScaleTransition(
                    scale: _pulseAnimation,
                    child: Icon(icon, color: textColor, size: 20),
                  )
                : Icon(icon, color: textColor, size: 20),
            
            const SizedBox(width: 12),
            
            // Message
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: textColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Additional info
            if (networkState.status == ConnectivityStatus.connected) ...[
              const SizedBox(width: 8),
              Text(
                networkState.typeDisplayName,
                style: TextStyle(
                  color: textColor.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
            ],
            
            // Pending actions indicator
            if (provider.pendingActionsCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: textColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${provider.pendingActionsCount}',
                  style: TextStyle(
                    color: textColor,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
            
            // Close button
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () => provider.hideConnectionBanner(),
              child: Icon(
                Icons.close,
                color: textColor.withValues(alpha: 0.8),
                size: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getConnectionIcon(ConnectionType type) {
    switch (type) {
      case ConnectionType.wifi:
        return Icons.wifi;
      case ConnectionType.mobile:
        return Icons.signal_cellular_4_bar;
      case ConnectionType.ethernet:
        return Icons.cable;
      case ConnectionType.bluetooth:
        return Icons.bluetooth;
      case ConnectionType.vpn:
        return Icons.vpn_key;
      case ConnectionType.other:
        return Icons.device_hub;
      case ConnectionType.none:
        return Icons.wifi_off;
    }
  }
}

class ConnectivityIndicator extends StatelessWidget {
  final double size;
  final bool showLabel;

  const ConnectivityIndicator({
    super.key,
    this.size = 24,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityProvider>(
      builder: (context, provider, child) {
        final networkState = provider.networkState;
        
        Color color;
        IconData icon;
        
        switch (networkState.status) {
          case ConnectivityStatus.connected:
            color = Colors.green;
            icon = _getConnectionIcon(networkState.type);
            break;
          case ConnectivityStatus.disconnected:
            color = Colors.red;
            icon = Icons.wifi_off;
            break;
          case ConnectivityStatus.reconnecting:
            color = Colors.orange;
            icon = Icons.wifi_find;
            break;
          case ConnectivityStatus.unknown:
            color = Colors.grey;
            icon = Icons.help_outline;
            break;
        }

        if (showLabel) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: color, size: size),
              const SizedBox(width: 4),
              Text(
                networkState.statusDisplayName,
                style: TextStyle(
                  color: color,
                  fontSize: size * 0.6,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          );
        }

        return Icon(icon, color: color, size: size);
      },
    );
  }

  IconData _getConnectionIcon(ConnectionType type) {
    switch (type) {
      case ConnectionType.wifi:
        return Icons.wifi;
      case ConnectionType.mobile:
        return Icons.signal_cellular_4_bar;
      case ConnectionType.ethernet:
        return Icons.cable;
      case ConnectionType.bluetooth:
        return Icons.bluetooth;
      case ConnectionType.vpn:
        return Icons.vpn_key;
      case ConnectionType.other:
        return Icons.device_hub;
      case ConnectionType.none:
        return Icons.wifi_off;
    }
  }
}

class OfflineIndicator extends StatelessWidget {
  final Widget child;
  final Widget? offlineWidget;

  const OfflineIndicator({
    super.key,
    required this.child,
    this.offlineWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityProvider>(
      builder: (context, provider, child) {
        if (provider.isOffline && offlineWidget != null) {
          return offlineWidget!;
        }
        return this.child;
      },
    );
  }
}

class ConnectivityAwareWidget extends StatelessWidget {
  final Widget onlineChild;
  final Widget offlineChild;
  final Widget? reconnectingChild;

  const ConnectivityAwareWidget({
    super.key,
    required this.onlineChild,
    required this.offlineChild,
    this.reconnectingChild,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityProvider>(
      builder: (context, provider, child) {
        switch (provider.networkState.status) {
          case ConnectivityStatus.connected:
            return onlineChild;
          case ConnectivityStatus.reconnecting:
            return reconnectingChild ?? offlineChild;
          case ConnectivityStatus.disconnected:
          case ConnectivityStatus.unknown:
            return offlineChild;
        }
      },
    );
  }
}
