#!/usr/bin/env python3
"""
إنشاء أيقونة مؤقتة للتطبيق
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    # إنشاء صورة 1024x1024 بخلفية زرقاء
    size = 1024
    img = Image.new('RGB', (size, size), color='#1877F2')  # لون فيسبوك الأزرق
    draw = ImageDraw.Draw(img)
    
    # رسم دائرة بيضاء في المنتصف
    circle_size = size // 2
    circle_pos = (size // 4, size // 4, size * 3 // 4, size * 3 // 4)
    draw.ellipse(circle_pos, fill='white')
    
    # رسم حرف A في المنتصف
    try:
        # محاولة استخدام خط كبير
        font_size = size // 4
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # استخدام الخط الافتراضي إذا لم يتوفر arial
        font = ImageFont.load_default()
    
    # كتابة حرف A
    text = "A"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2 - 20  # تعديل قليل للوسط
    
    draw.text((text_x, text_y), text, fill='#1877F2', font=font)
    
    # حفظ الصورة
    output_path = 'assets/icon/app_icon.png'
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path, 'PNG')
    print(f"تم إنشاء الأيقونة في: {output_path}")

if __name__ == "__main__":
    create_app_icon()
