# دليل المطور - تطبيق Arzawo 👨‍💻

## نظرة عامة

تطبيق Arzawo هو تطبيق دردشة مطور بـ Flutter مع دعم كامل للغة العربية. يستخدم التطبيق بيانات تجريبية محلية ولا يتطلب خادم خلفي.

## البنية المعمارية

### نمط المعمارية
- **MVVM (Model-View-ViewModel)** مع Provider لإدارة الحالة
- **Repository Pattern** للوصول إلى البيانات
- **Service Layer** للمنطق التجاري

### إدارة الحالة
```dart
// استخدام Provider لإدارة الحالة
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => AuthProvider()),
    ChangeNotifierProvider(create: (_) => ChatProvider()),
  ],
  child: My<PERSON><PERSON>(),
)
```

## المكونات الرئيسية

### 1. النماذج (Models)
- **User**: معلومات المستخدم
- **Message**: بيانات الرسالة
- **Chat**: معلومات المحادثة

### 2. مزودي الحالة (Providers)
- **AuthProvider**: إدارة المصادقة وحالة المستخدم
- **ChatProvider**: إدارة المحادثات والرسائل

### 3. الخدمات (Services)
- **MockDataService**: خدمة البيانات التجريبية

### 4. الشاشات (Screens)
- **LoginScreen**: شاشة تسجيل الدخول
- **ChatsScreen**: شاشة قائمة المحادثات
- **ChatScreen**: شاشة الدردشة

## التدويل (Internationalization)

### إعداد التدويل
```yaml
# pubspec.yaml
flutter:
  generate: true

dependencies:
  flutter_localizations:
    sdk: flutter
  intl: any
```

### ملفات الترجمة
```json
// lib/l10n/app_ar.arb
{
  "@@locale": "ar",
  "appTitle": "Arzawo",
  "login": "تسجيل الدخول"
}
```

### استخدام الترجمة
```dart
// في الكود
Text(AppLocalizations.of(context)!.login)
```

## إدارة البيانات

### التخزين المحلي
```dart
// استخدام SharedPreferences
final prefs = await SharedPreferences.getInstance();
await prefs.setString('user_data', jsonEncode(user.toJson()));
```

### البيانات التجريبية
```dart
// في MockDataService
List<User> get mockUsers => [
  User(id: '1', name: 'أحمد محمد', email: '<EMAIL>'),
  // المزيد من المستخدمين...
];
```

## الثيم والتصميم

### ألوان التطبيق
```dart
class AppTheme {
  static const Color primaryColor = Color(0xFF2E7D32);
  static const Color primaryLightColor = Color(0xFF4CAF50);
  static const Color sentMessageColor = Color(0xFF4CAF50);
  static const Color receivedMessageColor = Colors.white;
}
```

### اتجاه النص
```dart
// في main.dart
builder: (context, child) {
  return Directionality(
    textDirection: TextDirection.rtl,
    child: child!,
  );
}
```

## إضافة ميزات جديدة

### 1. إضافة شاشة جديدة
```dart
// 1. إنشاء ملف الشاشة
class NewScreen extends StatefulWidget {
  @override
  _NewScreenState createState() => _NewScreenState();
}

// 2. إضافة التنقل
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => NewScreen()),
);
```

### 2. إضافة نموذج جديد
```dart
class NewModel {
  final String id;
  final String name;
  
  NewModel({required this.id, required this.name});
  
  // إضافة toJson و fromJson
  Map<String, dynamic> toJson() => {'id': id, 'name': name};
  factory NewModel.fromJson(Map<String, dynamic> json) => 
    NewModel(id: json['id'], name: json['name']);
}
```

### 3. إضافة مزود حالة جديد
```dart
class NewProvider with ChangeNotifier {
  List<NewModel> _items = [];
  List<NewModel> get items => _items;
  
  void addItem(NewModel item) {
    _items.add(item);
    notifyListeners();
  }
}
```

## الاختبارات

### اختبارات الوحدة
```dart
test('User model serialization works', () {
  final user = User(id: '1', name: 'أحمد', email: '<EMAIL>');
  final json = user.toJson();
  final userFromJson = User.fromJson(json);
  
  expect(userFromJson.name, equals(user.name));
});
```

### اختبارات الواجهة
```dart
testWidgets('Login screen shows correctly', (WidgetTester tester) async {
  await tester.pumpWidget(MyApp());
  
  expect(find.text('تسجيل الدخول'), findsOneWidget);
  expect(find.text('البريد الإلكتروني'), findsOneWidget);
});
```

## نصائح للتطوير

### 1. إدارة الحالة
- استخدم `Consumer` للاستماع لتغييرات الحالة
- استخدم `Provider.of(context, listen: false)` للوصول بدون استماع
- تجنب استدعاء `notifyListeners()` في البناء

### 2. الأداء
- استخدم `const` للويدجت الثابتة
- تجنب إعادة بناء الويدجت غير الضرورية
- استخدم `ListView.builder` للقوائم الطويلة

### 3. إدارة الأخطاء
```dart
try {
  await someAsyncOperation();
} catch (e) {
  _errorMessage = 'حدث خطأ: ${e.toString()}';
  notifyListeners();
}
```

### 4. التحقق من البيانات
```dart
String? validateEmail(String? value) {
  if (value == null || value.isEmpty) {
    return 'أدخل البريد الإلكتروني';
  }
  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
    return 'البريد الإلكتروني غير صحيح';
  }
  return null;
}
```

## البناء والنشر

### بناء APK للاختبار
```bash
flutter build apk --debug
```

### بناء APK للإنتاج
```bash
flutter build apk --release
```

### بناء App Bundle
```bash
flutter build appbundle --release
```

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في التبعيات**: تشغيل `flutter pub get`
2. **مشاكل في البناء**: تشغيل `flutter clean` ثم `flutter pub get`
3. **مشاكل في المحاكي**: إعادة تشغيل المحاكي

### أدوات التطوير
```bash
# فحص المشروع
flutter analyze

# تشغيل الاختبارات
flutter test

# فحص الأداء
flutter run --profile
```

## الموارد المفيدة

- [Flutter Documentation](https://docs.flutter.dev/)
- [Provider Package](https://pub.dev/packages/provider)
- [Material Design](https://material.io/design)
- [Arabic Typography](https://material.io/design/typography/language-support.html#arabic)

---

**ملاحظة**: هذا الدليل يغطي الأساسيات. للمزيد من التفاصيل، راجع التعليقات في الكود المصدري.
