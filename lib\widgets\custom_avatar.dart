import 'package:flutter/material.dart';
import '../services/avatar_service.dart';

class CustomAvatar extends StatelessWidget {
  final String name;
  final String? imageUrl;
  final double radius;
  final Color? backgroundColor;
  final Color? textColor;
  final bool showBorder;
  final Color borderColor;
  final double borderWidth;
  final bool showOnlineIndicator;
  final bool isOnline;
  final VoidCallback? onTap;

  const CustomAvatar({
    super.key,
    required this.name,
    this.imageUrl,
    this.radius = 20,
    this.backgroundColor,
    this.textColor,
    this.showBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 2,
    this.showOnlineIndicator = false,
    this.isOnline = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final avatarService = AvatarService();
    
    Widget avatarWidget = avatarService.buildAvatarWithFallback(
      name: name,
      imageUrl: imageUrl,
      radius: radius,
      backgroundColor: backgroundColor,
      textColor: textColor,
      showBorder: showBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
    );

    // إضافة مؤشر الاتصال إذا كان مطلوباً
    if (showOnlineIndicator) {
      avatarWidget = Stack(
        children: [
          avatarWidget,
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: radius * 0.4,
              height: radius * 0.4,
              decoration: BoxDecoration(
                color: isOnline ? Colors.green : Colors.grey,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
              ),
            ),
          ),
        ],
      );
    }

    // إضافة إمكانية النقر
    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: avatarWidget,
      );
    }

    return avatarWidget;
  }
}

class CustomAvatarList extends StatelessWidget {
  final List<String> names;
  final List<String?> imageUrls;
  final double radius;
  final int maxVisible;
  final VoidCallback? onMoreTap;

  const CustomAvatarList({
    super.key,
    required this.names,
    required this.imageUrls,
    this.radius = 16,
    this.maxVisible = 3,
    this.onMoreTap,
  });

  @override
  Widget build(BuildContext context) {
    final visibleCount = names.length > maxVisible ? maxVisible : names.length;
    final remainingCount = names.length - maxVisible;

    return Row(
      children: [
        // عرض الصور المرئية
        ...List.generate(visibleCount, (index) {
          return Padding(
            padding: EdgeInsets.only(right: index > 0 ? 4 : 0),
            child: CustomAvatar(
              name: names[index],
              imageUrl: imageUrls.length > index ? imageUrls[index] : null,
              radius: radius,
              showBorder: true,
              borderColor: Colors.white,
              borderWidth: 2,
            ),
          );
        }),
        
        // عرض عدد الباقي إذا كان هناك المزيد
        if (remainingCount > 0)
          Padding(
            padding: const EdgeInsets.only(right: 4),
            child: GestureDetector(
              onTap: onMoreTap,
              child: CircleAvatar(
                radius: radius,
                backgroundColor: Colors.grey[300],
                child: Text(
                  '+$remainingCount',
                  style: TextStyle(
                    fontSize: radius * 0.6,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class CustomAvatarGrid extends StatelessWidget {
  final List<String> names;
  final List<String?> imageUrls;
  final double radius;
  final int crossAxisCount;
  final double spacing;
  final Function(int)? onAvatarTap;

  const CustomAvatarGrid({
    super.key,
    required this.names,
    required this.imageUrls,
    this.radius = 30,
    this.crossAxisCount = 4,
    this.spacing = 8,
    this.onAvatarTap,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: 1,
      ),
      itemCount: names.length,
      itemBuilder: (context, index) {
        return Column(
          children: [
            CustomAvatar(
              name: names[index],
              imageUrl: imageUrls.length > index ? imageUrls[index] : null,
              radius: radius,
              onTap: onAvatarTap != null ? () => onAvatarTap!(index) : null,
            ),
            const SizedBox(height: 4),
            Expanded(
              child: Text(
                names[index],
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      },
    );
  }
}

class CustomAvatarWithName extends StatelessWidget {
  final String name;
  final String? imageUrl;
  final double radius;
  final bool showName;
  final TextStyle? nameStyle;
  final MainAxisAlignment alignment;
  final VoidCallback? onTap;

  const CustomAvatarWithName({
    super.key,
    required this.name,
    this.imageUrl,
    this.radius = 25,
    this.showName = true,
    this.nameStyle,
    this.alignment = MainAxisAlignment.center,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: alignment,
        children: [
          CustomAvatar(
            name: name,
            imageUrl: imageUrl,
            radius: radius,
          ),
          if (showName) ...[
            const SizedBox(height: 8),
            Text(
              name,
              style: nameStyle ?? const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}

class CustomAvatarStory extends StatelessWidget {
  final String name;
  final String? imageUrl;
  final bool hasStory;
  final bool isViewed;
  final double radius;
  final VoidCallback? onTap;

  const CustomAvatarStory({
    super.key,
    required this.name,
    this.imageUrl,
    this.hasStory = false,
    this.isViewed = false,
    this.radius = 30,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: hasStory
              ? LinearGradient(
                  colors: isViewed
                      ? [Colors.grey, Colors.grey]
                      : [Colors.purple, Colors.pink, Colors.orange],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
        ),
        child: Container(
          padding: const EdgeInsets.all(2),
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: CustomAvatar(
            name: name,
            imageUrl: imageUrl,
            radius: radius,
          ),
        ),
      ),
    );
  }
}

class CustomAvatarChat extends StatelessWidget {
  final String name;
  final String? imageUrl;
  final bool isOnline;
  final bool showOnlineIndicator;
  final int? unreadCount;
  final double radius;
  final VoidCallback? onTap;

  const CustomAvatarChat({
    super.key,
    required this.name,
    this.imageUrl,
    this.isOnline = false,
    this.showOnlineIndicator = true,
    this.unreadCount,
    this.radius = 25,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          CustomAvatar(
            name: name,
            imageUrl: imageUrl,
            radius: radius,
            showOnlineIndicator: showOnlineIndicator,
            isOnline: isOnline,
          ),
          
          // مؤشر الرسائل غير المقروءة
          if (unreadCount != null && unreadCount! > 0)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                child: Text(
                  unreadCount! > 99 ? '99+' : unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
