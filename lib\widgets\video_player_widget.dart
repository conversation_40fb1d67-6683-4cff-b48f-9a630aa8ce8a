import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../providers/social_provider.dart';
import '../models/video.dart';
import '../theme/app_theme.dart';
import 'video_comments_dialog.dart';
import 'real_video_player.dart';

class VideoPlayerWidget extends StatefulWidget {
  final VideoPost video;
  final bool isActive;
  final bool isReels;

  const VideoPlayerWidget({
    super.key,
    required this.video,
    required this.isActive,
    required this.isReels,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {

  @override
  Widget build(BuildContext context) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // مشغل الفيديو الحقيقي الجديد
        RealVideoPlayer(
          videoUrl: widget.video.videoUrl,
          isLocalFile: false, // فيديوهات من الشبكة
          autoPlay: widget.isActive, // تشغيل تلقائي للفيديو النشط
          authorId: widget.video.authorId,
          onDelete: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم حذف الفيديو')),
            );
          },
        ),

        // معلومات الفيديو والمؤلف
        Positioned(
          bottom: 100,
          right: 16,
          left: 80,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المؤلف
              Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: AppTheme.primaryLightColor,
                    child: Text(
                      _getAuthorName(widget.video.authorId)[0].toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getAuthorName(widget.video.authorId),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'متابعة',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // وصف الفيديو
              Text(
                widget.video.content,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 8),
              
              // الهاشتاغات
              if (widget.video.hashtags.isNotEmpty)
                Wrap(
                  spacing: 4,
                  children: widget.video.hashtags.map((hashtag) {
                    return Text(
                      '#$hashtag',
                      style: const TextStyle(
                        color: Colors.blue,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  }).toList(),
                ),
              
              const SizedBox(height: 8),
              
              // معلومات الفيديو
              Row(
                children: [
                  Icon(
                    widget.isReels ? Icons.video_collection : Icons.video_library,
                    color: Colors.white,
                    size: 12,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    widget.video.videoTypeText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.access_time,
                    color: Colors.white,
                    size: 12,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    widget.video.videoMetadata.durationText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.visibility,
                    color: Colors.white,
                    size: 12,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.video.viewCount}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
              
              // الموسيقى (إذا وجدت)
              if (widget.video.musicTitle != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.music_note,
                      color: Colors.white,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        '${widget.video.musicTitle} - ${widget.video.musicArtist ?? 'فنان غير معروف'}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),

        // أزرار التفاعل
        Positioned(
          bottom: 100,
          left: 16,
          child: Column(
            children: [
              // زر الإعجاب
              Consumer<SocialProvider>(
                builder: (context, socialProvider, child) {
                  final hasLiked = socialProvider.hasUserLikedVideo(widget.video.id);
                  final count = socialProvider.getVideoLikesCount(widget.video.id);
                  return _ActionButton(
                    icon: Icon(
                      hasLiked ? Icons.favorite : Icons.favorite_border,
                      color: hasLiked ? Colors.red : Colors.white,
                      size: 28,
                    ),
                    label: _formatCount(count),
                    onTap: () => _likeVideo(),
                  );
                },
              ),
              
              const SizedBox(height: 16),
              
              // زر التعليق
              Consumer<SocialProvider>(
                builder: (context, socialProvider, child) {
                  final count = socialProvider.getVideoCommentsCount(widget.video.id);
                  return _ActionButton(
                    icon: const Icon(
                      Icons.comment,
                      color: Colors.white,
                      size: 28,
                    ),
                    label: _formatCount(count),
                    onTap: () => _showComments(),
                  );
                },
              ),
              
              const SizedBox(height: 16),
              
              // زر المشاركة
              _ActionButton(
                icon: const Icon(
                  Icons.share,
                  color: Colors.white,
                  size: 28,
                ),
                label: _formatCount(widget.video.shareCount),
                onTap: () => _shareVideo(),
              ),
              
              const SizedBox(height: 16),
              
              // زر المزيد
              _ActionButton(
                icon: const Icon(
                  Icons.more_vert,
                  color: Colors.white,
                  size: 28,
                ),
                label: '',
                onTap: () => _showMoreOptions(),
              ),
            ],
          ),
        ),

        // معلومات الوقت
        Positioned(
          top: 16,
          right: 16,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              timeago.format(widget.video.timestamp, locale: 'ar'),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getAuthorName(String authorId) {
    switch (authorId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}م';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}ك';
    } else {
      return count.toString();
    }
  }

  void _likeVideo() {
    Provider.of<SocialProvider>(context, listen: false)
        .likeVideo(widget.video.id);
  }

  void _showComments() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => VideoCommentsDialog(video: widget.video),
    );
  }

  void _shareVideo() {
    Provider.of<SocialProvider>(context, listen: false)
        .shareVideo(widget.video.id);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم مشاركة الفيديو!')),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.bookmark_outline),
              title: const Text('حفظ الفيديو'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم حفظ الفيديو!')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.report_outlined),
              title: const Text('الإبلاغ عن الفيديو'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم الإبلاغ عن الفيديو!')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('حظر المستخدم'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم حظر المستخدم!')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final Widget icon;
  final String label;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: Center(child: icon),
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
