import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:async';
import 'dart:io';
import '../models/story.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';

class StoryViewerWidget extends StatefulWidget {
  final UserStories userStories;

  const StoryViewerWidget({super.key, required this.userStories});

  @override
  State<StoryViewerWidget> createState() => _StoryViewerWidgetState();
}

class _StoryViewerWidgetState extends State<StoryViewerWidget>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _progressController;
  Timer? _timer;
  int _currentIndex = 0;
  Set<String> _likedStories = {};

  final Duration _storyDuration = const Duration(seconds: 5);

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _progressController = AnimationController(
      duration: _storyDuration,
      vsync: this,
    );
    
    _startStoryTimer();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _progressController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startStoryTimer() {
    _progressController.reset();
    _progressController.forward();
    
    _timer?.cancel();
    _timer = Timer(_storyDuration, () {
      _nextStory();
    });
  }

  void _nextStory() {
    if (_currentIndex < widget.userStories.activeStories.length - 1) {
      setState(() {
        _currentIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startStoryTimer();
    } else {
      Navigator.pop(context);
    }
  }

  void _previousStory() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startStoryTimer();
    }
  }

  void _pauseStory() {
    _timer?.cancel();
    _progressController.stop();
  }

  void _resumeStory() {
    final remaining = _storyDuration * (1 - _progressController.value);
    _timer?.cancel();
    _timer = Timer(remaining, () {
      _nextStory();
    });
    _progressController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final stories = widget.userStories.activeStories;
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTapDown: (details) {
          _pauseStory();
        },
        onTapUp: (details) {
          _resumeStory();
          
          // تحديد الجانب المضغوط
          final screenWidth = MediaQuery.of(context).size.width;
          if (details.globalPosition.dx < screenWidth / 2) {
            _previousStory();
          } else {
            _nextStory();
          }
        },
        onTapCancel: () {
          _resumeStory();
        },
        child: Stack(
          children: [
            // محتوى القصة
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
                _startStoryTimer();
              },
              itemCount: stories.length,
              itemBuilder: (context, index) {
                final story = stories[index];
                return _StoryContent(story: story);
              },
            ),
            
            // شريط التقدم
            Positioned(
              top: MediaQuery.of(context).padding.top + 8,
              right: 8,
              left: 8,
              child: Row(
                children: List.generate(
                  stories.length,
                  (index) => Expanded(
                    child: Container(
                      height: 3,
                      margin: const EdgeInsets.symmetric(horizontal: 1),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                      child: AnimatedBuilder(
                        animation: _progressController,
                        builder: (context, child) {
                          double progress = 0.0;
                          if (index < _currentIndex) {
                            progress = 1.0;
                          } else if (index == _currentIndex) {
                            progress = _progressController.value;
                          }
                          
                          return LinearProgressIndicator(
                            value: progress,
                            backgroundColor: Colors.transparent,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            // معلومات المستخدم
            Positioned(
              top: MediaQuery.of(context).padding.top + 20,
              right: 16,
              left: 16,
              child: Row(
                children: [
                  SmartAvatarWithText(
                    user: widget.userStories.user,
                    radius: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.userStories.user.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _getTimeAgo(stories[_currentIndex].timestamp),
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            
            // مؤشرات التنقل
            Positioned(
              bottom: 100,
              right: 16,
              left: 16,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (_currentIndex > 0)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                        size: 16,
                      ),
                    )
                  else
                    const SizedBox(width: 32),
                  
                  Text(
                    '${_currentIndex + 1} / ${stories.length}',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                  
                  if (_currentIndex < stories.length - 1)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 16,
                      ),
                    )
                  else
                    const SizedBox(width: 32),
                ],
              ),
            ),

            // أزرار التفاعل
            Positioned(
              bottom: 50,
              right: 16,
              child: Column(
                children: [
                  // زر الإعجاب مع تأثيرات
                  GestureDetector(
                    onTap: () => _toggleLike(stories[_currentIndex]),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _isLiked(stories[_currentIndex])
                            ? Colors.red.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                        border: _isLiked(stories[_currentIndex])
                            ? Border.all(color: Colors.red, width: 2)
                            : null,
                      ),
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder: (child, animation) {
                          return ScaleTransition(
                            scale: animation,
                            child: child,
                          );
                        },
                        child: Icon(
                          _isLiked(stories[_currentIndex]) ? Icons.favorite : Icons.favorite_border,
                          key: ValueKey(_isLiked(stories[_currentIndex])),
                          color: _isLiked(stories[_currentIndex]) ? Colors.red : Colors.white,
                          size: 28,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // زر الرد
                  GestureDetector(
                    onTap: () => _showReplyDialog(stories[_currentIndex]),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.reply,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // زر المشاركة
                  GestureDetector(
                    onTap: () => _shareStory(stories[_currentIndex]),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.share,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isLiked(Story story) {
    return _likedStories.contains(story.id);
  }

  void _toggleLike(Story story) {
    setState(() {
      if (_likedStories.contains(story.id)) {
        _likedStories.remove(story.id);
      } else {
        _likedStories.add(story.id);
      }
    });

    // تأثير اهتزاز للزر
    if (_isLiked(story)) {
      // إظهار تأثير القلوب المتطايرة
      _showHeartAnimation();
    }

    // إظهار رسالة تأكيد مع إيموجي
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _isLiked(story) ? Icons.favorite : Icons.heart_broken,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(_isLiked(story) ? 'أعجبتك هذه القصة! ❤️' : 'تم إلغاء الإعجاب 💔'),
          ],
        ),
        duration: const Duration(seconds: 1),
        backgroundColor: _isLiked(story) ? Colors.red : Colors.grey[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showHeartAnimation() {
    // تأثير بصري للإعجاب (يمكن تطويره لاحقاً)
    // هنا يمكن إضافة تأثيرات متحركة للقلوب
  }

  void _showReplyDialog(Story story) {
    _pauseStory();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'رد على القصة',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                hintText: 'اكتب ردك...',
                hintStyle: TextStyle(color: Colors.grey),
                border: OutlineInputBorder(),
                filled: true,
                fillColor: Colors.white,
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // أيقونات تفاعل سريع
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _sendQuickReaction('❤️');
                  },
                  child: const Text('❤️', style: TextStyle(fontSize: 32)),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _sendQuickReaction('😍');
                  },
                  child: const Text('😍', style: TextStyle(fontSize: 32)),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _sendQuickReaction('😂');
                  },
                  child: const Text('😂', style: TextStyle(fontSize: 32)),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _sendQuickReaction('👏');
                  },
                  child: const Text('👏', style: TextStyle(fontSize: 32)),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _sendQuickReaction('🔥');
                  },
                  child: const Text('🔥', style: TextStyle(fontSize: 32)),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _resumeStory();
            },
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _sendReply(story);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            child: const Text(
              'إرسال',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    ).then((_) => _resumeStory());
  }

  void _sendQuickReaction(String emoji) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إرسال $emoji'),
        duration: const Duration(seconds: 1),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _sendReply(Story story) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال الرد! 💬'),
        duration: Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareStory(Story story) {
    _pauseStory();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[600],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            // العنوان
            const Text(
              'مشاركة القصة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // خيارات المشاركة
            _buildShareOption(
              icon: Icons.share,
              title: 'مشاركة خارجية',
              subtitle: 'مشاركة عبر التطبيقات الأخرى',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _performExternalShare(story);
              },
            ),

            _buildShareOption(
              icon: Icons.post_add,
              title: 'مشاركة كمنشور',
              subtitle: 'نشر في الصفحة الرئيسية',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                _shareAsPost(story);
              },
            ),

            _buildShareOption(
              icon: Icons.message,
              title: 'إرسال في رسالة',
              subtitle: 'إرسال لصديق في المحادثات',
              color: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _sendInMessage(story);
              },
            ),

            _buildShareOption(
              icon: Icons.group,
              title: 'مشاركة في مجموعة',
              subtitle: 'نشر في إحدى المجموعات',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                _shareInGroup(story);
              },
            ),

            _buildShareOption(
              icon: Icons.copy,
              title: 'نسخ الرابط',
              subtitle: 'نسخ رابط القصة للحافظة',
              color: Colors.teal,
              onTap: () {
                Navigator.pop(context);
                _copyStoryLink(story);
              },
            ),

            const SizedBox(height: 16),

            // زر الإلغاء
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.grey[800],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ).then((_) => _resumeStory());
  }

  Widget _buildShareOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[700]!,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[500],
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // دوال المشاركة المختلفة
  void _performExternalShare(Story story) async {
    try {
      String shareText = '';
      List<XFile> files = [];

      // تحضير المحتوى حسب نوع القصة
      switch (story.type) {
        case StoryType.text:
          shareText = 'شاهد هذه القصة: "${story.content}"\n\nمن تطبيق Arzawo';
          break;
        case StoryType.image:
          shareText = 'شاهد هذه الصورة من تطبيق Arzawo';
          if (story.mediaUrl != null && story.mediaUrl!.isNotEmpty) {
            if (story.mediaUrl!.startsWith('http')) {
              // رابط شبكة - سنشارك الرابط فقط
              shareText += '\n${story.mediaUrl}';
            } else {
              // ملف محلي
              files.add(XFile(story.mediaUrl!));
            }
          }
          break;
        case StoryType.video:
          shareText = 'شاهد هذا الفيديو من تطبيق Arzawo';
          if (story.mediaUrl != null && story.mediaUrl!.isNotEmpty) {
            if (story.mediaUrl!.startsWith('http')) {
              shareText += '\n${story.mediaUrl}';
            } else {
              files.add(XFile(story.mediaUrl!));
            }
          }
          break;
      }

      // مشاركة المحتوى
      if (files.isNotEmpty) {
        await Share.shareXFiles(
          files,
          text: shareText,
          subject: 'قصة من تطبيق Arzawo',
        );
      } else {
        await Share.share(
          shareText,
          subject: 'قصة من تطبيق Arzawo',
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح خيارات المشاركة الخارجية! 📤'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المشاركة: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareAsPost(Story story) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'مشاركة كمنشور',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'هل تريد مشاركة هذه القصة كمنشور في الصفحة الرئيسية؟',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            // معاينة المحتوى
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        story.type == StoryType.text ? Icons.text_fields :
                        story.type == StoryType.image ? Icons.image : Icons.video_library,
                        color: Colors.blue,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        story.type == StoryType.text ? 'قصة نصية' :
                        story.type == StoryType.image ? 'قصة صورة' : 'قصة فيديو',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (story.type == StoryType.text)
                    Text(
                      story.content.length > 50
                          ? '${story.content.substring(0, 50)}...'
                          : story.content,
                      style: const TextStyle(color: Colors.grey),
                    )
                  else
                    const Text(
                      'سيتم مشاركة الوسائط مع المنشور',
                      style: TextStyle(color: Colors.grey),
                    ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _createPostFromStory(story);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
            ),
            child: const Text('مشاركة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _createPostFromStory(Story story) {
    // هنا سيتم إنشاء منشور جديد من القصة
    // يمكن تطوير هذا لاحقاً للتكامل مع نظام المنشورات

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مشاركة القصة كمنشور في الصفحة الرئيسية! 📝'),
        duration: Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _sendInMessage(Story story) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'إرسال في رسالة',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'اختر صديق لإرسال القصة إليه:',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            // قائمة الأصدقاء (مؤقتة)
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.blue,
                child: Icon(Icons.person, color: Colors.white),
              ),
              title: const Text('أحمد محمد', style: TextStyle(color: Colors.white)),
              subtitle: const Text('متصل الآن', style: TextStyle(color: Colors.green)),
              onTap: () {
                Navigator.pop(context);
                _sendStoryToFriend('أحمد محمد', story);
              },
            ),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.purple,
                child: Icon(Icons.person, color: Colors.white),
              ),
              title: const Text('فاطمة علي', style: TextStyle(color: Colors.white)),
              subtitle: const Text('متصلة منذ 5 دقائق', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                _sendStoryToFriend('فاطمة علي', story);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _shareInGroup(Story story) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'مشاركة في مجموعة',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'اختر مجموعة لمشاركة القصة فيها:',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            // قائمة المجموعات (مؤقتة)
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.orange,
                child: Icon(Icons.group, color: Colors.white),
              ),
              title: const Text('مجموعة الأصدقاء', style: TextStyle(color: Colors.white)),
              subtitle: const Text('25 عضو', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                _shareStoryInGroup('مجموعة الأصدقاء', story);
              },
            ),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.teal,
                child: Icon(Icons.group, color: Colors.white),
              ),
              title: const Text('مجموعة العمل', style: TextStyle(color: Colors.white)),
              subtitle: const Text('12 عضو', style: TextStyle(color: Colors.grey)),
              onTap: () {
                Navigator.pop(context);
                _shareStoryInGroup('مجموعة العمل', story);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _copyStoryLink(Story story) async {
    try {
      // إنشاء رابط للقصة
      String storyLink = 'https://arzawo.app/story/${story.id}';

      // نسخ الرابط إلى الحافظة
      await Clipboard.setData(ClipboardData(text: storyLink));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('تم نسخ رابط القصة! 📋'),
                      Text(
                        storyLink,
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.teal,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في نسخ الرابط: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _sendStoryToFriend(String friendName, Story story) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'إرسال إلى $friendName',
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معاينة القصة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    story.type == StoryType.text ? Icons.text_fields :
                    story.type == StoryType.image ? Icons.image : Icons.video_library,
                    color: Colors.blue,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          story.type == StoryType.text ? 'قصة نصية' :
                          story.type == StoryType.image ? 'قصة صورة' : 'قصة فيديو',
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        if (story.type == StoryType.text)
                          Text(
                            story.content.length > 30
                                ? '${story.content.substring(0, 30)}...'
                                : story.content,
                            style: const TextStyle(color: Colors.grey, fontSize: 12),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                hintText: 'أضف رسالة (اختياري)...',
                hintStyle: TextStyle(color: Colors.grey),
                border: OutlineInputBorder(),
                filled: true,
                fillColor: Colors.white,
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _confirmSendMessage(friendName, story);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
            ),
            child: const Text('إرسال', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _confirmSendMessage(String friendName, Story story) {
    // هنا سيتم إرسال القصة فعلياً للصديق
    // يمكن تطوير هذا لاحقاً للتكامل مع نظام الرسائل

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.send, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text('تم إرسال القصة إلى $friendName! 💬'),
            ),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.purple,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _shareStoryInGroup(String groupName, Story story) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'مشاركة في $groupName',
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات المجموعة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const CircleAvatar(
                    backgroundColor: Colors.orange,
                    child: Icon(Icons.group, color: Colors.white),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          groupName,
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        Text(
                          groupName == 'مجموعة الأصدقاء' ? '25 عضو' : '12 عضو',
                          style: const TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // معاينة القصة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    story.type == StoryType.text ? Icons.text_fields :
                    story.type == StoryType.image ? Icons.image : Icons.video_library,
                    color: Colors.blue,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          story.type == StoryType.text ? 'قصة نصية' :
                          story.type == StoryType.image ? 'قصة صورة' : 'قصة فيديو',
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        if (story.type == StoryType.text)
                          Text(
                            story.content.length > 30
                                ? '${story.content.substring(0, 30)}...'
                                : story.content,
                            style: const TextStyle(color: Colors.grey, fontSize: 12),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                hintText: 'أضف تعليق للمجموعة (اختياري)...',
                hintStyle: TextStyle(color: Colors.grey),
                border: OutlineInputBorder(),
                filled: true,
                fillColor: Colors.white,
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _confirmShareInGroup(groupName, story);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            child: const Text('مشاركة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _confirmShareInGroup(String groupName, Story story) {
    // هنا سيتم مشاركة القصة فعلياً في المجموعة
    // يمكن تطوير هذا لاحقاً للتكامل مع نظام المجموعات

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.group, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text('تم مشاركة القصة في $groupName! 👥'),
            ),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }
}

class _StoryContent extends StatelessWidget {
  final Story story;

  const _StoryContent({required this.story});

  @override
  Widget build(BuildContext context) {
    switch (story.type) {
      case StoryType.text:
        return Container(
          decoration: BoxDecoration(
            gradient: _getBackgroundGradient(story.background),
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Text(
                story.content,
                style: const TextStyle(
                  color: Colors.black,  // تغيير لون النص إلى أسود
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(2, 2),
                      blurRadius: 4,
                      color: Colors.white,
                    ),
                    Shadow(
                      offset: Offset(-1, -1),
                      blurRadius: 2,
                      color: Colors.white,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      
      case StoryType.image:
        return Stack(
          fit: StackFit.expand,
          children: [
            if (story.mediaUrl != null)
              _buildImageWidget(story.mediaUrl!),
            if (story.content.isNotEmpty)
              Positioned(
                bottom: 100,
                right: 16,
                left: 16,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    story.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black54,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      
      case StoryType.video:
        return _VideoStoryContent(story: story);
    }
  }

  Widget _buildImageWidget(String imageUrl) {
    // تحديد نوع الملف (محلي أم شبكة)
    if (imageUrl.startsWith('http')) {
      // صورة من الشبكة
      return Image.network(
        imageUrl,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: Colors.black,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'جاري تحميل الصورة...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[800],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    color: Colors.white,
                    size: 64,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل الصورة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } else {
      // صورة محلية
      return Image.file(
        File(imageUrl),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[800],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    color: Colors.white,
                    size: 64,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل الصورة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  LinearGradient _getBackgroundGradient(StoryBackground background) {
    switch (background) {
      case StoryBackground.none:
        return const LinearGradient(colors: [Colors.black, Colors.black]);
      case StoryBackground.gradient1:
        return const LinearGradient(
          colors: [Colors.blue, Colors.purple],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient2:
        return const LinearGradient(
          colors: [Colors.pink, Colors.orange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient3:
        return const LinearGradient(
          colors: [Colors.green, Colors.teal],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient4:
        return const LinearGradient(
          colors: [Colors.orange, Colors.red],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient5:
        return const LinearGradient(
          colors: [Colors.purple, Colors.indigo],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case StoryBackground.gradient6:
        return const LinearGradient(
          colors: [Colors.teal, Colors.blue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }
}

class _VideoStoryContent extends StatefulWidget {
  final Story story;

  const _VideoStoryContent({required this.story});

  @override
  State<_VideoStoryContent> createState() => _VideoStoryContentState();
}

class _VideoStoryContentState extends State<_VideoStoryContent> {
  VideoPlayerController? _videoController;
  bool _isInitialized = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    if (widget.story.mediaUrl == null) {
      setState(() {
        _hasError = true;
      });
      return;
    }

    try {
      // تحديد نوع الملف (محلي أم شبكة)
      if (widget.story.mediaUrl!.startsWith('http')) {
        // فيديو من الشبكة
        _videoController = VideoPlayerController.networkUrl(
          Uri.parse(widget.story.mediaUrl!),
        );
      } else {
        // فيديو محلي
        _videoController = VideoPlayerController.file(
          File(widget.story.mediaUrl!),
        );
      }

      await _videoController!.initialize();
      await _videoController!.setLooping(true);
      await _videoController!.play();

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
        });
      }
      debugPrint('خطأ في تشغيل الفيديو: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.white,
                size: 64,
              ),
              SizedBox(height: 16),
              Text(
                'خطأ في تحميل الفيديو',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isInitialized) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              SizedBox(height: 16),
              Text(
                'جاري تحميل الفيديو...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        // الفيديو
        Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black,
          child: Center(
            child: AspectRatio(
              aspectRatio: _videoController!.value.aspectRatio,
              child: VideoPlayer(_videoController!),
            ),
          ),
        ),

        // النص إذا كان موجود
        if (widget.story.content.isNotEmpty)
          Positioned(
            bottom: 100,
            right: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                widget.story.content,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black54,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

        // أيقونة تشغيل/إيقاف
        if (_isInitialized)
          Center(
            child: GestureDetector(
              onTap: () {
                if (_videoController!.value.isPlaying) {
                  _videoController!.pause();
                } else {
                  _videoController!.play();
                }
                setState(() {});
              },
              child: AnimatedOpacity(
                opacity: _videoController!.value.isPlaying ? 0.0 : 1.0,
                duration: const Duration(milliseconds: 300),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _videoController!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 48,
                  ),
                ),
              ),
            ),
          ),

        // مؤشر جودة الفيديو
        if (_isInitialized)
          Positioned(
            top: 100,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'HD',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
