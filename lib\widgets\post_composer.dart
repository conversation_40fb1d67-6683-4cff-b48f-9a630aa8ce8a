import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/social_provider.dart';
import '../models/post.dart';
import '../theme/app_theme.dart';
import 'create_post_dialog.dart';

class PostComposer extends StatelessWidget {
  const PostComposer({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // شريط "بما تفكر؟"
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppTheme.primaryColor,
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _showCreatePostDialog(context),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Text(
                        'بما تفكر؟',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // أزرار الخيارات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _PostOption(
                  icon: Icons.photo_library,
                  label: 'صورة',
                  color: Colors.green,
                  onTap: () => _showCreatePostDialog(
                    context,
                    initialType: PostType.image,
                  ),
                ),
                _PostOption(
                  icon: Icons.videocam,
                  label: 'فيديو',
                  color: Colors.red,
                  onTap: () => _showCreatePostDialog(
                    context,
                    initialType: PostType.video,
                  ),
                ),
                _PostOption(
                  icon: Icons.emoji_emotions,
                  label: 'شعور',
                  color: Colors.orange,
                  onTap: () => _showCreatePostDialog(
                    context,
                    showFeelings: true,
                  ),
                ),
                _PostOption(
                  icon: Icons.location_on,
                  label: 'موقع',
                  color: Colors.blue,
                  onTap: () => _showCreatePostDialog(
                    context,
                    showLocation: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCreatePostDialog(
    BuildContext context, {
    PostType? initialType,
    bool showFeelings = false,
    bool showLocation = false,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreatePostDialog(
        initialType: initialType,
        showFeelings: showFeelings,
        showLocation: showLocation,
      ),
    );
  }
}

class _PostOption extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _PostOption({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
