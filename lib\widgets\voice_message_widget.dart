import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import '../theme/app_theme.dart';

class VoiceMessageWidget extends StatefulWidget {
  final Duration duration;
  final bool isMe;
  final VoidCallback? onPlay;
  final VoidCallback? onPause;
  final String? audioUrl;

  const VoiceMessageWidget({
    super.key,
    required this.duration,
    required this.isMe,
    this.onPlay,
    this.onPause,
    this.audioUrl,
  });

  @override
  State<VoiceMessageWidget> createState() => _VoiceMessageWidgetState();
}

class _VoiceMessageWidgetState extends State<VoiceMessageWidget>
    with TickerProviderStateMixin {
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _currentPosition = Duration.zero;
  Timer? _progressTimer;
  late AnimationController _waveAnimationController;
  late AnimationController _playButtonController;
  late Animation<double> _playButtonAnimation;
  
  // موجات صوتية عشوائية للتأثير البصري
  List<double> _waveHeights = [];
  final int _waveCount = 30;

  @override
  void initState() {
    super.initState();
    
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _playButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _playButtonController,
      curve: Curves.easeInOut,
    ));
    
    _generateWaveHeights();
  }

  @override
  void dispose() {
    _progressTimer?.cancel();
    _waveAnimationController.dispose();
    _playButtonController.dispose();
    super.dispose();
  }

  void _generateWaveHeights() {
    final random = Random();
    _waveHeights = List.generate(_waveCount, (index) {
      return random.nextDouble() * 0.8 + 0.2; // بين 0.2 و 1.0
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر التشغيل/الإيقاف
          AnimatedBuilder(
            animation: _playButtonAnimation,
            child: _buildPlayButton(),
            builder: (context, child) {
              return Transform.scale(
                scale: _playButtonAnimation.value,
                child: child,
              );
            },
          ),
          
          const SizedBox(width: 8),
          
          // الموجات الصوتية
          Expanded(
            child: _buildWaveform(),
          ),
          
          const SizedBox(width: 8),
          
          // مدة الصوت
          Text(
            _formatDuration(_isPlaying ? _currentPosition : widget.duration),
            style: TextStyle(
              color: widget.isMe ? Colors.white70 : Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayButton() {
    return GestureDetector(
      onTapDown: (_) => _playButtonController.forward(),
      onTapUp: (_) => _playButtonController.reverse(),
      onTapCancel: () => _playButtonController.reverse(),
      onTap: _togglePlayback,
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: widget.isMe 
              ? Colors.white.withValues(alpha: 0.2)
              : AppTheme.primaryColor.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: _isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    widget.isMe ? Colors.white : AppTheme.primaryColor,
                  ),
                ),
              )
            : Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: widget.isMe ? Colors.white : AppTheme.primaryColor,
                size: 20,
              ),
      ),
    );
  }

  Widget _buildWaveform() {
    return AnimatedBuilder(
      animation: _waveAnimationController,
      builder: (context, child) {
        return Container(
          height: 30,
          child: Row(
            children: List.generate(_waveCount, (index) {
              final progress = _currentPosition.inMilliseconds / widget.duration.inMilliseconds;
              final isActive = (index / _waveCount) <= progress;
              
              // تأثير الموجة المتحركة أثناء التشغيل
              double animatedHeight = _waveHeights[index];
              if (_isPlaying) {
                final waveProgress = (_waveAnimationController.value + (index * 0.1)) % 1.0;
                animatedHeight *= (sin(waveProgress * 2 * pi) * 0.3 + 0.7);
              }
              
              return Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 1),
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      width: 2,
                      height: 30 * animatedHeight,
                      decoration: BoxDecoration(
                        color: isActive
                            ? (widget.isMe ? Colors.white : AppTheme.primaryColor)
                            : (widget.isMe 
                                ? Colors.white.withValues(alpha: 0.4)
                                : Colors.grey.withValues(alpha: 0.4)),
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }

  void _togglePlayback() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    // محاكاة تحميل الملف الصوتي
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _isLoading = false;
      _isPlaying = !_isPlaying;
    });

    if (_isPlaying) {
      _startPlayback();
      if (widget.onPlay != null) {
        widget.onPlay!();
      }
    } else {
      _stopPlayback();
      if (widget.onPause != null) {
        widget.onPause!();
      }
    }
  }

  void _startPlayback() {
    _waveAnimationController.repeat();
    
    _progressTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isPlaying) {
        timer.cancel();
        return;
      }

      setState(() {
        _currentPosition = Duration(
          milliseconds: _currentPosition.inMilliseconds + 100,
        );
      });

      // إنهاء التشغيل عند الوصول للنهاية
      if (_currentPosition >= widget.duration) {
        _stopPlayback();
      }
    });
  }

  void _stopPlayback() {
    setState(() {
      _isPlaying = false;
    });
    
    _progressTimer?.cancel();
    _waveAnimationController.stop();
    
    // إعادة تعيين الموضع إذا انتهى التشغيل
    if (_currentPosition >= widget.duration) {
      setState(() {
        _currentPosition = Duration.zero;
      });
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

// ويدجت تسجيل الصوت
class VoiceRecorderWidget extends StatefulWidget {
  final VoidCallback? onStart;
  final VoidCallback? onStop;
  final VoidCallback? onCancel;
  final Function(Duration)? onComplete;

  const VoiceRecorderWidget({
    super.key,
    this.onStart,
    this.onStop,
    this.onCancel,
    this.onComplete,
  });

  @override
  State<VoiceRecorderWidget> createState() => _VoiceRecorderWidgetState();
}

class _VoiceRecorderWidgetState extends State<VoiceRecorderWidget>
    with TickerProviderStateMixin {
  bool _isRecording = false;
  Duration _recordingDuration = Duration.zero;
  Timer? _recordingTimer;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  
  List<double> _recordingWaves = [];
  final int _maxWaves = 50;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _recordingTimer?.cancel();
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isRecording) {
      return GestureDetector(
        onTap: _startRecording,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.mic,
            color: Colors.white,
            size: 24,
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          // زر الإلغاء
          GestureDetector(
            onTap: _cancelRecording,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // مؤشر التسجيل النابض
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(width: 12),
          
          // موجات التسجيل
          Expanded(
            child: Container(
              height: 30,
              child: Row(
                children: _recordingWaves.map((height) {
                  return Container(
                    width: 3,
                    height: 30 * height,
                    margin: const EdgeInsets.symmetric(horizontal: 1),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // مدة التسجيل
          Text(
            _formatDuration(_recordingDuration),
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // زر الإيقاف والإرسال
          GestureDetector(
            onTap: _stopRecording,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.send,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _startRecording() {
    setState(() {
      _isRecording = true;
      _recordingDuration = Duration.zero;
      _recordingWaves.clear();
    });

    _pulseController.repeat(reverse: true);
    
    if (widget.onStart != null) {
      widget.onStart!();
    }

    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _recordingDuration = Duration(
          milliseconds: _recordingDuration.inMilliseconds + 100,
        );
        
        // إضافة موجة عشوائية
        final random = Random();
        _recordingWaves.add(random.nextDouble() * 0.8 + 0.2);
        
        // الحفاظ على عدد محدود من الموجات
        if (_recordingWaves.length > _maxWaves) {
          _recordingWaves.removeAt(0);
        }
      });

      // إيقاف التسجيل تلقائياً بعد دقيقتين
      if (_recordingDuration.inMinutes >= 2) {
        _stopRecording();
      }
    });
  }

  void _stopRecording() {
    setState(() {
      _isRecording = false;
    });

    _recordingTimer?.cancel();
    _pulseController.stop();

    if (widget.onStop != null) {
      widget.onStop!();
    }

    if (widget.onComplete != null) {
      widget.onComplete!(_recordingDuration);
    }
  }

  void _cancelRecording() {
    setState(() {
      _isRecording = false;
      _recordingDuration = Duration.zero;
      _recordingWaves.clear();
    });

    _recordingTimer?.cancel();
    _pulseController.stop();

    if (widget.onCancel != null) {
      widget.onCancel!();
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
