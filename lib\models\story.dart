import 'user.dart';

enum StoryType {
  text,
  image,
  video,
}

enum StoryBackground {
  none,
  gradient1,
  gradient2,
  gradient3,
  gradient4,
  gradient5,
  gradient6,
}

class StoryViewer {
  final String userId;
  final DateTime viewedAt;

  const StoryViewer({
    required this.userId,
    required this.viewedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'viewedAt': viewedAt.toIso8601String(),
    };
  }

  factory StoryViewer.fromJson(Map<String, dynamic> json) {
    return StoryViewer(
      userId: json['userId'],
      viewedAt: DateTime.parse(json['viewedAt']),
    );
  }
}

class Story {
  final String id;
  final String authorId;
  final String content;
  final StoryType type;
  final String? mediaUrl;
  final String? thumbnailUrl;
  final StoryBackground background;
  final Duration? duration;
  final DateTime timestamp;
  final DateTime expiresAt;
  final List<StoryViewer> viewers;
  final bool isPublic;

  const Story({
    required this.id,
    required this.authorId,
    required this.content,
    this.type = StoryType.text,
    this.mediaUrl,
    this.thumbnailUrl,
    this.background = StoryBackground.none,
    this.duration,
    required this.timestamp,
    required this.expiresAt,
    this.viewers = const [],
    this.isPublic = true,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isActive => !isExpired;
  int get viewCount => viewers.length;

  Story copyWith({
    String? id,
    String? authorId,
    String? content,
    StoryType? type,
    String? mediaUrl,
    String? thumbnailUrl,
    StoryBackground? background,
    Duration? duration,
    DateTime? timestamp,
    DateTime? expiresAt,
    List<StoryViewer>? viewers,
    bool? isPublic,
  }) {
    return Story(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      content: content ?? this.content,
      type: type ?? this.type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      background: background ?? this.background,
      duration: duration ?? this.duration,
      timestamp: timestamp ?? this.timestamp,
      expiresAt: expiresAt ?? this.expiresAt,
      viewers: viewers ?? this.viewers,
      isPublic: isPublic ?? this.isPublic,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'authorId': authorId,
      'content': content,
      'type': type.name,
      'mediaUrl': mediaUrl,
      'thumbnailUrl': thumbnailUrl,
      'background': background.name,
      'duration': duration?.inSeconds,
      'timestamp': timestamp.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'viewers': viewers.map((v) => v.toJson()).toList(),
      'isPublic': isPublic,
    };
  }

  factory Story.fromJson(Map<String, dynamic> json) {
    return Story(
      id: json['id'],
      authorId: json['authorId'],
      content: json['content'],
      type: StoryType.values.firstWhere((e) => e.name == json['type']),
      mediaUrl: json['mediaUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      background: StoryBackground.values.firstWhere(
        (e) => e.name == json['background'],
        orElse: () => StoryBackground.none,
      ),
      duration: json['duration'] != null 
          ? Duration(seconds: json['duration']) 
          : null,
      timestamp: DateTime.parse(json['timestamp']),
      expiresAt: DateTime.parse(json['expiresAt']),
      viewers: (json['viewers'] as List?)
          ?.map((v) => StoryViewer.fromJson(v))
          .toList() ?? [],
      isPublic: json['isPublic'] ?? true,
    );
  }
}

class UserStories {
  final User user;
  final List<Story> stories;

  const UserStories({
    required this.user,
    required this.stories,
  });

  List<Story> get activeStories => 
      stories.where((story) => story.isActive).toList();
  
  bool get hasActiveStories => activeStories.isNotEmpty;
  
  Story? get latestStory => activeStories.isNotEmpty 
      ? activeStories.reduce((a, b) => 
          a.timestamp.isAfter(b.timestamp) ? a : b)
      : null;

  int get totalViewCount => activeStories
      .map((story) => story.viewCount)
      .fold(0, (sum, count) => sum + count);

  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'stories': stories.map((s) => s.toJson()).toList(),
    };
  }

  factory UserStories.fromJson(Map<String, dynamic> json) {
    return UserStories(
      user: User.fromJson(json['user']),
      stories: (json['stories'] as List)
          .map((s) => Story.fromJson(s))
          .toList(),
    );
  }
}
