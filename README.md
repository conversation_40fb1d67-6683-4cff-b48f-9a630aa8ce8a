# تطبيق Arzawo للدردشة 💬

تطبيق دردشة بسيط مطور بـ Flutter يدعم اللغة العربية بالكامل مع واجهة مستخدم جميلة وسهلة الاستخدام.

## المميزات ✨

- 🌍 **دعم كامل للغة العربية**: واجهة مستخدم باللغة العربية مع اتجاه النص من اليمين إلى اليسار
- 🔐 **تسجيل دخول آمن**: نظام تسجيل دخول بالبريد الإلكتروني وكلمة المرور
- 💬 **دردشة فورية**: إرسال واستقبال الرسائل مع واجهة دردشة حديثة
- 👥 **قائمة المحادثات**: عرض جميع المحادثات مع آخر الرسائل
- 📱 **تصميم متجاوب**: يعمل بسلاسة على جميع أحجام الشاشات
- 🎨 **واجهة جميلة**: تصميم Material Design مع ألوان جذابة
- 💾 **بيانات تجريبية**: يستخدم بيانات محلية للاختبار (بدون Firebase)

## لقطات الشاشة 📱

### شاشة تسجيل الدخول
- واجهة تسجيل دخول بسيطة وأنيقة
- التحقق من صحة البيانات المدخلة
- رسائل خطأ واضحة باللغة العربية

### شاشة المحادثات
- قائمة بجميع المحادثات
- عرض آخر رسالة وتوقيتها
- مؤشر الحالة (متصل/غير متصل)
- عداد الرسائل غير المقروءة

### شاشة الدردشة
- واجهة دردشة حديثة مع فقاعات الرسائل
- تمييز الرسائل المرسلة والمستقبلة
- عرض توقيت الرسائل
- حقل إدخال نص مع زر إرسال

## التقنيات المستخدمة 🛠️

- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **Provider**: إدارة الحالة
- **SharedPreferences**: التخزين المحلي
- **Material Design**: نظام التصميم
- **Intl**: التدويل ودعم اللغات
- **TimeAgo**: عرض الأوقات النسبية

## هيكل المشروع 📁

```
lib/
├── main.dart                 # نقطة البداية الرئيسية
├── models/                   # نماذج البيانات
│   ├── user.dart            # نموذج المستخدم
│   ├── message.dart         # نموذج الرسالة
│   └── chat.dart            # نموذج المحادثة
├── providers/               # مزودي الحالة
│   ├── auth_provider.dart   # مزود المصادقة
│   └── chat_provider.dart   # مزود المحادثات
├── screens/                 # الشاشات
│   ├── login_screen.dart    # شاشة تسجيل الدخول
│   ├── chats_screen.dart    # شاشة المحادثات
│   └── chat_screen.dart     # شاشة الدردشة
├── services/                # الخدمات
│   └── mock_data_service.dart # خدمة البيانات التجريبية
├── theme/                   # الثيم والألوان
│   └── app_theme.dart       # ثيم التطبيق
└── l10n/                    # ملفات الترجمة
    └── app_ar.arb           # الترجمة العربية
```

## التثبيت والتشغيل 🚀

### المتطلبات
- Flutter SDK (3.7.2 أو أحدث)
- Android Studio أو VS Code
- جهاز Android أو محاكي

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd arzawo
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

4. **بناء APK للإنتاج**
```bash
flutter build apk --release
```

## بيانات الاختبار 🧪

للاختبار، يمكنك استخدام أي بريد إلكتروني صحيح مع كلمة مرور من 6 أحرف على الأقل:

- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `password123`

### المستخدمون التجريبيون
- أحمد محمد (متصل)
- فاطمة علي (غير متصل)
- محمد حسن (متصل)
- عائشة أحمد (غير متصل)
- عمر خالد (متصل)

## الاختبارات 🧪

يتضمن المشروع اختبارات شاملة:

```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبارات محددة
flutter test test/widget_test.dart
```

### أنواع الاختبارات
- **اختبارات الواجهة**: التحقق من عمل الشاشات
- **اختبارات النماذج**: التحقق من تسلسل البيانات
- **اختبارات التكامل**: التحقق من تفاعل المكونات

## ملف APK الجاهز 📦

تم بناء ملف APK جاهز للتثبيت:
- **المسار**: `build/app/outputs/flutter-apk/app-release.apk`
- **الحجم**: 20.8 MB
- **الإصدار**: 1.0.0+1

## المساهمة 🤝

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى الفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل 📧

- **المطور**: فريق Arzawo
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: [www.arzawo.com](https://www.arzawo.com)

---

**ملاحظة**: هذا التطبيق مطور لأغراض التعلم والاختبار. يستخدم بيانات تجريبية محلية ولا يتطلب اتصال بالإنترنت.
