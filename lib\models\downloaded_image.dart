class DownloadedImage {
  final String id;
  final String imagePath;
  final String description;
  final DateTime downloadedAt;
  final String userId;

  DownloadedImage({
    required this.id,
    required this.imagePath,
    required this.description,
    required this.downloadedAt,
    required this.userId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'imagePath': imagePath,
      'description': description,
      'downloadedAt': downloadedAt.toIso8601String(),
      'userId': userId,
    };
  }

  factory DownloadedImage.fromJson(Map<String, dynamic> json) {
    return DownloadedImage(
      id: json['id'],
      imagePath: json['imagePath'],
      description: json['description'],
      downloadedAt: DateTime.parse(json['downloadedAt']),
      userId: json['userId'],
    );
  }
}
