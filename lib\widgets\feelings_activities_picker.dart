import 'package:flutter/material.dart';
import '../models/feelings_activities.dart';

class FeelingsActivitiesPicker extends StatefulWidget {
  final Function(String type, String id, String name, String display)? onSelected;

  const FeelingsActivitiesPicker({
    super.key,
    this.onSelected,
  });

  @override
  State<FeelingsActivitiesPicker> createState() => _FeelingsActivitiesPickerState();
}

class _FeelingsActivitiesPickerState extends State<FeelingsActivitiesPicker>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
                const Expanded(
                  child: Text(
                    'كيف تشعر؟ ماذا تفعل؟',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48), // للتوازن
              ],
            ),
          ),

          // Tabs
          Container(
            color: Colors.grey[50],
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.blue,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.blue,
              tabs: const [
                Tab(
                  icon: Icon(Icons.sentiment_satisfied),
                  text: 'المشاعر',
                ),
                Tab(
                  icon: Icon(Icons.local_activity),
                  text: 'الأنشطة',
                ),
                Tab(
                  icon: Icon(Icons.location_on),
                  text: 'الموقع',
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFeelingsTab(),
                _buildActivitiesTab(),
                _buildLocationsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeelingsTab() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 3,
      ),
      itemCount: FeelingsData.feelings.length,
      itemBuilder: (context, index) {
        final feeling = FeelingsData.feelings[index];
        return _buildFeelingCard(feeling);
      },
    );
  }

  Widget _buildFeelingCard(Feeling feeling) {
    return GestureDetector(
      onTap: () {
        widget.onSelected?.call(
          'feeling',
          feeling.id,
          feeling.name,
          '${feeling.emoji} ${feeling.name}',
        );
        Navigator.pop(context);
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(int.parse(feeling.color.replaceFirst('#', '0xFF'))),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Center(
                child: Text(
                  feeling.emoji,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            Expanded(
              child: Text(
                feeling.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivitiesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: ActivitiesData.activities.length,
      itemBuilder: (context, index) {
        final activity = ActivitiesData.activities[index];
        return _buildActivityCard(activity);
      },
    );
  }

  Widget _buildActivityCard(Activity activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Color(int.parse(activity.color.replaceFirst('#', '0xFF'))),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              activity.icon,
              style: const TextStyle(fontSize: 24),
            ),
          ),
        ),
        title: Text(
          activity.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        children: activity.subActivities.map((subActivity) {
          return ListTile(
            contentPadding: const EdgeInsets.only(left: 72, right: 16),
            title: Text(subActivity),
            onTap: () {
              widget.onSelected?.call(
                'activity',
                activity.id,
                subActivity,
                '${activity.icon} ${activity.name} $subActivity',
              );
              Navigator.pop(context);
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildLocationsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: LocationsData.defaultLocations.length,
      itemBuilder: (context, index) {
        final location = LocationsData.defaultLocations[index];
        return _buildLocationCard(location);
      },
    );
  }

  Widget _buildLocationCard(Location location) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.blue[100],
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              location.icon,
              style: const TextStyle(fontSize: 24),
            ),
          ),
        ),
        title: Text(
          location.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: location.address != null
            ? Text(
                location.address!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              )
            : null,
        onTap: () {
          widget.onSelected?.call(
            'location',
            location.id,
            location.name,
            '📍 في ${location.name}',
          );
          Navigator.pop(context);
        },
      ),
    );
  }
}
