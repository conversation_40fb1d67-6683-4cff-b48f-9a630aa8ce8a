import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/social_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/post_card.dart';
import '../models/user.dart';
import '../models/post.dart';
import '../services/mock_data_service.dart';
import '../services/image_picker_service.dart';
import '../widgets/smart_avatar.dart';
import 'edit_profile_screen.dart';
import 'activity_log_screen.dart';
import 'archive_screen.dart';
import 'settings_screen.dart';

class FacebookProfileScreen extends StatefulWidget {
  final String userId;
  final String userName;

  const FacebookProfileScreen({
    super.key,
    required this.userId,
    required this.userName,
  });

  @override
  State<FacebookProfileScreen> createState() => _FacebookProfileScreenState();
}

class _FacebookProfileScreenState extends State<FacebookProfileScreen>
    with SingleTickerProviderStateMixin {
  User? _userInfo;
  bool _isLoading = true;
  bool _isFollowing = false;
  late TabController _tabController;
  List<Post> _userPosts = [];
  List<Post> _userPhotos = [];
  List<Post> _userVideos = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadUserInfo();
    _loadUserPosts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserInfo() async {
    try {
      final mockDataService = MockDataService();
      
      if (widget.userId == 'current_user') {
        final currentUser = await mockDataService.getCurrentUser();
        if (currentUser != null) {
          setState(() {
            _userInfo = currentUser;
            _isLoading = false;
          });
        }
      } else {
        // تحميل بيانات المستخدمين الآخرين
        final users = mockDataService.mockUsers;
        final user = users.firstWhere(
          (u) => u.id == widget.userId,
          orElse: () => users.first,
        );
        setState(() {
          _userInfo = user;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadUserPosts() async {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    final allPosts = socialProvider.posts;
    
    // تصفية منشورات المستخدم
    final userPosts = allPosts.where((post) => post.authorId == widget.userId).toList();
    
    // تصفية الصور - جميع المنشورات التي تحتوي على صور
    final photos = <Post>[];
    for (final post in userPosts) {
      for (final media in post.media) {
        if (media.type == PostType.image) {
          photos.add(post);
          break; // إضافة المنشور مرة واحدة فقط
        }
      }
    }

    // تصفية الفيديوهات - جميع المنشورات التي تحتوي على فيديوهات
    final videos = <Post>[];
    for (final post in userPosts) {
      for (final media in post.media) {
        if (media.type == PostType.video) {
          videos.add(post);
          break; // إضافة المنشور مرة واحدة فقط
        }
      }
    }
    
    setState(() {
      _userPosts = userPosts;
      _userPhotos = photos;
      _userVideos = videos;
    });
  }

  void _toggleFollow() {
    setState(() {
      _isFollowing = !_isFollowing;
    });
  }

  void _sendMessage() {
    // تنفيذ إرسال رسالة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح الدردشة...')),
    );
  }

  void _editProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditProfileScreen(user: _userInfo!),
      ),
    ).then((_) => _loadUserInfo());
  }

  Future<void> _changeProfilePhoto() async {
    final newProfileUrl = await ImagePickerService.pickProfileImage(context);
    if (newProfileUrl != null) {
      setState(() {
        _userInfo = _userInfo!.copyWith(avatar: newProfileUrl);
      });

      // تحديث البيانات في الخدمة
      final mockDataService = MockDataService();
      await mockDataService.updateUserProfileImage(widget.userId, newProfileUrl);
    }
  }

  Future<void> _changeCoverPhoto() async {
    final newCoverUrl = await ImagePickerService.pickCoverImage(context);
    if (newCoverUrl != null) {
      setState(() {
        _userInfo = _userInfo!.copyWith(coverImageUrl: newCoverUrl);
      });

      // تحديث البيانات في الخدمة
      final mockDataService = MockDataService();
      await mockDataService.updateUserCoverImage(widget.userId, newCoverUrl);
    }
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }

  List<PopupMenuEntry<String>> _buildMenuItems() {
    final isOwnProfile = widget.userId == 'current_user';

    if (isOwnProfile) {
      // قائمة للملف الشخصي الخاص
      return [
        const PopupMenuItem<String>(
          value: 'edit_profile',
          child: Row(
            children: [
              Icon(Icons.edit, color: Colors.black),
              SizedBox(width: 8),
              Text('تعديل الملف الشخصي'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'activity_log',
          child: Row(
            children: [
              Icon(Icons.history, color: Colors.black),
              SizedBox(width: 8),
              Text('سجل النشاط'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'archive',
          child: Row(
            children: [
              Icon(Icons.archive, color: Colors.black),
              SizedBox(width: 8),
              Text('الأرشيف'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'privacy_settings',
          child: Row(
            children: [
              Icon(Icons.privacy_tip, color: Colors.black),
              SizedBox(width: 8),
              Text('إعدادات الخصوصية'),
            ],
          ),
        ),
      ];
    } else {
      // قائمة للملفات الشخصية للآخرين
      return [
        const PopupMenuItem<String>(
          value: 'send_message',
          child: Row(
            children: [
              Icon(Icons.message, color: Colors.black),
              SizedBox(width: 8),
              Text('إرسال رسالة'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'follow_unfollow',
          child: Row(
            children: [
              Icon(Icons.person_add, color: Colors.black),
              SizedBox(width: 8),
              Text('متابعة'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'block_user',
          child: Row(
            children: [
              Icon(Icons.block, color: Colors.red),
              SizedBox(width: 8),
              Text('حظر المستخدم', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'report_user',
          child: Row(
            children: [
              Icon(Icons.report, color: Colors.red),
              SizedBox(width: 8),
              Text('الإبلاغ عن المستخدم', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ];
    }
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'edit_profile':
        _navigateToEditProfile();
        break;
      case 'activity_log':
        _navigateToActivityLog();
        break;
      case 'archive':
        _navigateToArchive();
        break;
      case 'privacy_settings':
        _navigateToPrivacySettings();
        break;
      case 'send_message':
        _sendMessage();
        break;
      case 'follow_unfollow':
        _toggleFollow();
        break;
      case 'block_user':
        _showBlockUserDialog();
        break;
      case 'report_user':
        _showReportUserDialog();
        break;
    }
  }

  void _navigateToEditProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditProfileScreen(user: _userInfo!),
      ),
    );
  }

  void _navigateToActivityLog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ActivityLogScreen(),
      ),
    );
  }

  void _navigateToArchive() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ArchiveScreen(),
      ),
    );
  }

  void _navigateToPrivacySettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }



  void _showBlockUserDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حظر المستخدم'),
        content: const Text('هل أنت متأكد من أنك تريد حظر هذا المستخدم؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حظر المستخدم')),
              );
            },
            child: const Text('حظر', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showReportUserDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن المستخدم'),
        content: const Text('سيتم مراجعة البلاغ من قبل فريق الإدارة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إرسال البلاغ')),
              );
            },
            child: const Text('إبلاغ', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            widget.userName,
            style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_userInfo == null) {
      return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: const Center(child: Text('لم يتم العثور على المستخدم')),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              pinned: true,
              expandedHeight: 0,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () => Navigator.pop(context),
              ),
              title: Text(
                _userInfo!.name,
                style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
              ),
              actions: [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.black),
                  onSelected: _handleMenuSelection,
                  itemBuilder: (BuildContext context) => _buildMenuItems(),
                ),
              ],
            ),
          ];
        },
        body: SingleChildScrollView(
          child: Column(
            children: [
              // صورة الغلاف
              _buildCoverSection(),
              
              // صورة الملف الشخصي والاسم
              _buildProfileSection(),
              
              // الإحصائيات (المنشورات، المتابعين، المتابَعين)
              _buildStatsSection(),
              
              // الأزرار (تعديل/متابعة/رسالة)
              _buildActionButtons(),
              
              const Divider(height: 1, color: Colors.grey),
              
              // التبويبات (المنشورات، الصور، الفيديوهات، المعلومات)
              _buildTabBar(),
              
              // محتوى التبويبات
              _buildTabContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCoverSection() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Stack(
        children: [
          // صورة الغلاف أو placeholder
          Container(
            height: 200,
            width: double.infinity,
            color: Colors.grey[100],
            child: _userInfo!.coverImageUrl != null
                ? Image.network(
                    _userInfo!.coverImageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildCoverPlaceholder();
                    },
                  )
                : _buildCoverPlaceholder(),
          ),
          
          // زر تغيير صورة الغلاف (للمستخدم الحالي فقط)
          if (widget.userId == 'current_user')
            Positioned(
              bottom: 16,
              right: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.camera_alt, color: Colors.black),
                  onPressed: _changeCoverPhoto,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCoverPlaceholder() {
    return Container(
      color: Colors.grey[100],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera_outlined,
              size: 40,
              color: Colors.grey,
            ),
            SizedBox(height: 8),
            Text(
              'لا توجد صورة غلاف',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // صورة الملف الشخصي مع قلم التعديل
          Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 4),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.3),
                      spreadRadius: 2,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: SmartAvatarWithText(
                  user: _userInfo,
                  radius: 60,
                ),
              ),
              
              // قلم التعديل (للمستخدم الحالي فقط)
              if (widget.userId == 'current_user')
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.camera_alt, size: 20, color: Colors.black),
                      onPressed: _changeProfilePhoto,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // اسم المستخدم مع علامة التوثيق
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _userInfo!.name,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (_userInfo!.isVerified == true) ...[
                const SizedBox(width: 8),
                const Icon(
                  Icons.verified,
                  color: Colors.blue,
                  size: 24,
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 8),
          
          // النبذة الشخصية
          if (_userInfo!.bio != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _userInfo!.bio!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatItem(_userInfo!.postsCount.toString(), 'منشور'),
          Container(height: 40, width: 1, color: Colors.grey[300]),
          _buildStatItem(_formatCount(_userInfo!.followersCount), 'متابع'),
          Container(height: 40, width: 1, color: Colors.grey[300]),
          _buildStatItem(_formatCount(_userInfo!.followingCount), 'يتابع'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: widget.userId == 'current_user'
          ? SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _editProfile,
                icon: const Icon(Icons.edit),
                label: const Text('تعديل الملف الشخصي'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            )
          : Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _toggleFollow,
                    icon: Icon(_isFollowing ? Icons.check : Icons.person_add),
                    label: Text(_isFollowing ? 'متابَع' : 'متابعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isFollowing ? Colors.grey[300] : AppTheme.primaryColor,
                      foregroundColor: _isFollowing ? Colors.black87 : Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _sendMessage,
                    icon: const Icon(Icons.message),
                    label: const Text('رسالة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: AppTheme.primaryColor,
        indicatorWeight: 3,
        tabs: const [
          Tab(text: 'المنشورات'),
          Tab(text: 'الصور'),
          Tab(text: 'الفيديوهات'),
          Tab(text: 'المعلومات'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return Container(
      height: 600, // ارتفاع ثابت للمحتوى
      color: Colors.grey[50],
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildPostsTab(),
          _buildPhotosTab(),
          _buildVideosTab(),
          _buildInfoTab(),
        ],
      ),
    );
  }

  Widget _buildPostsTab() {
    if (_userPosts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.post_add, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد منشورات',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: _userPosts.length,
      itemBuilder: (context, index) {
        return PostCard(post: _userPosts[index]);
      },
    );
  }

  Widget _buildPhotosTab() {
    // جمع جميع الصور من جميع المنشورات
    final allImages = <PostMedia>[];
    for (final post in _userPhotos) {
      final images = post.media.where((m) => m.type == PostType.image);
      allImages.addAll(images);
    }

    if (allImages.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.photo_library, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد صور',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: allImages.length,
      itemBuilder: (context, index) {
        final image = allImages[index];

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[200],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              image.url,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Center(
                  child: Icon(Icons.broken_image, color: Colors.grey),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildVideosTab() {
    // جمع جميع الفيديوهات من جميع المنشورات
    final allVideos = <PostMedia>[];
    for (final post in _userVideos) {
      final videos = post.media.where((m) => m.type == PostType.video);
      allVideos.addAll(videos);
    }

    if (allVideos.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد فيديوهات',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 16 / 9,
      ),
      itemCount: allVideos.length,
      itemBuilder: (context, index) {
        final video = allVideos[index];

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[200],
          ),
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.grey[300],
                  child: video.thumbnail != null
                      ? Image.network(
                          video.thumbnail!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Center(
                              child: Icon(Icons.play_circle_fill, size: 48, color: Colors.white),
                            );
                          },
                        )
                      : const Center(
                          child: Icon(Icons.play_circle_fill, size: 48, color: Colors.white),
                        ),
                ),
              ),

              // أيقونة التشغيل
              const Positioned.fill(
                child: Center(
                  child: Icon(Icons.play_circle_fill, size: 48, color: Colors.white),
                ),
              ),

              // مدة الفيديو
              Positioned(
                bottom: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    video.duration != null
                        ? '${video.duration!.inMinutes}:${(video.duration!.inSeconds % 60).toString().padLeft(2, '0')}'
                        : '0:30',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('المعلومات الأساسية', [
            if (_userInfo!.work != null) _buildInfoItem(Icons.work, 'العمل', _userInfo!.work!),
            if (_userInfo!.education != null) _buildInfoItem(Icons.school, 'التعليم', _userInfo!.education!),
            if (_userInfo!.city != null) _buildInfoItem(Icons.location_on, 'المدينة', _userInfo!.city!),
            if (_userInfo!.country != null) _buildInfoItem(Icons.public, 'البلد', _userInfo!.country!),
          ]),

          const SizedBox(height: 24),

          _buildInfoSection('معلومات الاتصال', [
            _buildInfoItem(Icons.email, 'البريد الإلكتروني', _userInfo!.email),
          ]),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> items) {
    if (items.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}