import 'package:flutter/material.dart';
import '../models/comment.dart';
import '../models/reaction_types.dart';
import 'professional_reactions.dart';

class ReactionPicker extends StatefulWidget {
  final Function(ReactionType) onReactionSelected;
  final VoidCallback onClose;
  final ReactionType? currentReaction;

  const ReactionPicker({
    super.key,
    required this.onReactionSelected,
    required this.onClose,
    this.currentReaction,
  });

  @override
  State<ReactionPicker> createState() => _ReactionPickerState();
}

class _ReactionPickerState extends State<ReactionPicker>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(35),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: ReactionType.values.map((reactionType) {
                  return _buildReactionButton(reactionType);
                }).toList(),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReactionButton(ReactionType reactionType) {
    final reaction = CommentReaction(
      id: '',
      userId: '',
      userName: '',
      type: reactionType,
      timestamp: DateTime.now(),
    );

    final isSelected = widget.currentReaction == reactionType;

    return GestureDetector(
      onTap: () {
        widget.onReactionSelected(reactionType);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? _getReactionColor(reactionType).withValues(alpha: 0.15) : Colors.transparent,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة التفاعل
            Text(
              reaction.emoji,
              style: TextStyle(
                fontSize: isSelected ? 28 : 24,
              ),
            ),

            const SizedBox(height: 4),

            // اسم التفاعل
            Text(
              reaction.label,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.w700,
                color: isSelected ? _getReactionColor(reactionType) : Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getReactionColor(ReactionType type) {
    switch (type) {
      case ReactionType.like:
        return Colors.blue;
      case ReactionType.love:
        return Colors.red;
      case ReactionType.haha:
        return Colors.orange;
      case ReactionType.wow:
        return Colors.yellow;
      case ReactionType.sad:
        return Colors.blue;
      case ReactionType.angry:
        return Colors.red;
    }
  }
}

// Widget لعرض التفاعل الحالي
class ReactionButton extends StatefulWidget {
  final ReactionType? currentReaction;
  final int reactionCount;
  final Function(ReactionType) onReactionChanged;
  final VoidCallback onReactionRemoved;

  const ReactionButton({
    super.key,
    this.currentReaction,
    required this.reactionCount,
    required this.onReactionChanged,
    required this.onReactionRemoved,
  });

  @override
  State<ReactionButton> createState() => _ReactionButtonState();
}

class _ReactionButtonState extends State<ReactionButton> {
  bool _showPicker = false;
  OverlayEntry? _overlayEntry;

  @override
  Widget build(BuildContext context) {
    final hasReaction = widget.currentReaction != null;
    final reactionData = hasReaction
        ? ReactionData.getReaction(widget.currentReaction!)
        : ReactionData.getReaction(ReactionType.like);

    return GestureDetector(
      onTap: () {
        if (hasReaction) {
          widget.onReactionRemoved();
        } else {
          widget.onReactionChanged(ReactionType.like);
        }
        _animateReactionTap();
      },
      onLongPress: _showReactionPicker,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: hasReaction
              ? reactionData.color.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: hasReaction
              ? Border.all(color: reactionData.color.withValues(alpha: 0.3))
              : null,
          boxShadow: hasReaction
              ? [
                  BoxShadow(
                    color: reactionData.color.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة التفاعل مع تأثير الانبثاق
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: Text(
                reactionData.emoji,
                style: TextStyle(
                  fontSize: hasReaction ? 20 : 18,
                  shadows: hasReaction
                      ? [
                          Shadow(
                            color: reactionData.color.withValues(alpha: 0.5),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ]
                      : null,
                ),
              ),
            ),

            const SizedBox(width: 6),

            // نص التفاعل
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                color: hasReaction ? reactionData.color : Colors.grey[600],
                fontWeight: hasReaction ? FontWeight.bold : FontWeight.w600,
                fontSize: 14,
              ),
              child: Text(hasReaction ? reactionData.name : 'إعجاب'),
            ),

            // عدد التفاعلات
            if (widget.reactionCount > 0) ...[
              const SizedBox(width: 4),
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: hasReaction
                      ? reactionData.color.withValues(alpha: 0.2)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${widget.reactionCount}',
                  style: TextStyle(
                    color: hasReaction ? reactionData.color : Colors.grey[600],
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showReactionPicker() {
    if (_showPicker) return;

    _showPicker = true;

    final renderBox = context.findRenderObject() as RenderBox;
    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // حساب الموضع المناسب لإظهار التفاعلات
    double left = buttonPosition.dx - 100; // توسيط التفاعلات
    double bottom = screenHeight - buttonPosition.dy + 10;

    // التأكد من عدم الخروج من حدود الشاشة
    if (left < 10) left = 10;
    if (left + 350 > screenWidth) left = screenWidth - 360;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: left,
        bottom: bottom,
        child: Material(
          color: Colors.transparent,
          child: SizedBox(
            width: 350, // عرض ثابت للتفاعلات
            child: ProfessionalReactionPicker(
              currentReaction: widget.currentReaction,
              onReactionSelected: (reaction) {
                widget.onReactionChanged(reaction);
                _hideReactionPicker();
              },
              onClose: _hideReactionPicker,
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);

    // إخفاء المنتقي بعد 4 ثوان
    Future.delayed(const Duration(seconds: 4), () {
      _hideReactionPicker();
    });
  }

  void _hideReactionPicker() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _showPicker = false;
    }
  }

  void _animateReactionTap() {
    // تأثير بسيط للنقر - يمكن إضافة انيميشن أكثر تعقيداً هنا
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _hideReactionPicker();
    super.dispose();
  }
}
