import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import '../providers/social_provider.dart';
import '../models/post.dart';

class ImageViewer extends StatefulWidget {
  final List<PostMedia> images;
  final int initialIndex;
  final String postId;
  final String authorName;

  const ImageViewer({
    super.key,
    required this.images,
    required this.initialIndex,
    required this.postId,
    required this.authorName,
  });

  @override
  State<ImageViewer> createState() => _ImageViewerState();
}

class _ImageViewerState extends State<ImageViewer> {
  late PageController _pageController;
  int _currentIndex = 0;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () {
          setState(() {
            _showControls = !_showControls;
          });
        },
        child: Stack(
          children: [
            // عارض الصور
            PageView.builder(
              controller: _pageController,
              itemCount: widget.images.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemBuilder: (context, index) {
                final image = widget.images[index];
                return Center(
                  child: InteractiveViewer(
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: _buildImage(image),
                  ),
                );
              },
            ),

            // شريط علوي
            if (_showControls)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.7),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          // زر الرجوع
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                          
                          const SizedBox(width: 16),
                          
                          // معلومات الصورة
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.authorName,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (widget.images.length > 1)
                                  Text(
                                    '${_currentIndex + 1} من ${widget.images.length}',
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 14,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          
                          // زر الخيارات
                          IconButton(
                            onPressed: () => _showImageOptions(),
                            icon: const Icon(
                              Icons.more_vert,
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

            // مؤشر الصور (إذا كان هناك أكثر من صورة)
            if (widget.images.length > 1 && _showControls)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    widget.images.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index == _currentIndex
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.5),
                      ),
                    ),
                  ),
                ),
              ),

            // شريط سفلي مع الأزرار
            if (_showControls)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.7),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // زر الحفظ في المفضلة
                          _buildActionButton(
                            icon: Icons.bookmark_border,
                            label: 'حفظ',
                            onTap: () => _saveToFavorites(),
                          ),
                          
                          // زر التنزيل
                          _buildActionButton(
                            icon: Icons.download,
                            label: 'تنزيل',
                            onTap: () => _downloadImage(),
                          ),
                          
                          // زر المشاركة
                          _buildActionButton(
                            icon: Icons.share,
                            label: 'مشاركة',
                            onTap: () => _shareImage(),
                          ),
                          
                          // زر الخيارات الإضافية
                          _buildActionButton(
                            icon: Icons.more_horiz,
                            label: 'المزيد',
                            onTap: () => _showMoreOptions(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildImage(PostMedia image) {
    final isLocalFile = image.url.startsWith('/') || image.url.contains('\\');
    
    if (isLocalFile) {
      return Image.file(
        File(image.url),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[800],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, color: Colors.white, size: 64),
                  SizedBox(height: 16),
                  Text(
                    'لا يمكن تحميل الصورة',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } else {
      return Image.network(
        image.url,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
              color: Colors.white,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[800],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, color: Colors.white, size: 64),
                  SizedBox(height: 16),
                  Text(
                    'لا يمكن تحميل الصورة',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دوال الإجراءات
  void _saveToFavorites() {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);
    socialProvider.savePost(widget.postId);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.bookmark, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('تم حفظ الصورة في المفضلة! 📌'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _downloadImage() async {
    try {
      final currentImage = widget.images[_currentIndex];

      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );

      if (currentImage.url.startsWith('http')) {
        // تنزيل من الشبكة
        await _downloadFromNetwork(currentImage.url);
      } else {
        // نسخ الملف المحلي
        await _copyLocalFile(currentImage.url);
      }

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.download_done, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                const Text('تم تنزيل الصورة بنجاح! 📱'),
              ],
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تنزيل الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _downloadFromNetwork(String url) async {
    try {
      // محاكاة تنزيل من الشبكة
      await Future.delayed(const Duration(seconds: 2));

      // في التطبيق الحقيقي، سيتم تنزيل الصورة وحفظها في معرض الصور
      // هنا نحاكي العملية
      final socialProvider = Provider.of<SocialProvider>(context, listen: false);
      socialProvider.addDownloadedImage(url, 'تم تنزيل الصورة من الشبكة');

    } catch (e) {
      throw Exception('فشل في تنزيل الصورة من الشبكة: $e');
    }
  }

  Future<void> _copyLocalFile(String filePath) async {
    try {
      // نسخ الملف المحلي إلى مجلد التنزيلات
      final file = File(filePath);
      if (await file.exists()) {
        // محاكاة نسخ الملف
        await Future.delayed(const Duration(seconds: 1));

        final socialProvider = Provider.of<SocialProvider>(context, listen: false);
        socialProvider.addDownloadedImage(filePath, 'تم نسخ الصورة إلى معرض الصور');
      } else {
        throw Exception('الملف غير موجود');
      }
    } catch (e) {
      throw Exception('فشل في نسخ الصورة: $e');
    }
  }

  void _shareImage() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[600],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            const Text(
              'مشاركة الصورة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // خيارات المشاركة
            _buildShareOption(
              icon: Icons.share,
              title: 'مشاركة خارجية',
              subtitle: 'مشاركة عبر التطبيقات الأخرى',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _performExternalShare();
              },
            ),

            _buildShareOption(
              icon: Icons.post_add,
              title: 'مشاركة كمنشور',
              subtitle: 'نشر في الصفحة الرئيسية',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                _shareAsPost();
              },
            ),

            _buildShareOption(
              icon: Icons.message,
              title: 'إرسال في رسالة',
              subtitle: 'إرسال لصديق في المحادثات',
              color: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _sendInMessage();
              },
            ),

            _buildShareOption(
              icon: Icons.group,
              title: 'مشاركة في مجموعة',
              subtitle: 'نشر في إحدى المجموعات',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                _shareInGroup();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[700]!),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[500],
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _performExternalShare() async {
    try {
      final currentImage = widget.images[_currentIndex];

      // إظهار قائمة التطبيقات الخارجية
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.grey[900],
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'مشاركة خارجية',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // شبكة التطبيقات الخارجية
              GridView.count(
                shrinkWrap: true,
                crossAxisCount: 4,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                children: [
                  _buildExternalAppOption(
                    icon: Icons.facebook,
                    label: 'Facebook',
                    color: const Color(0xFF1877F2),
                    onTap: () => _shareToFacebook(currentImage),
                  ),
                  _buildExternalAppOption(
                    icon: Icons.camera_alt,
                    label: 'Instagram',
                    color: const Color(0xFFE4405F),
                    onTap: () => _shareToInstagram(currentImage),
                  ),
                  _buildExternalAppOption(
                    icon: Icons.alternate_email,
                    label: 'Twitter',
                    color: const Color(0xFF1DA1F2),
                    onTap: () => _shareToTwitter(currentImage),
                  ),
                  _buildExternalAppOption(
                    icon: Icons.chat,
                    label: 'WhatsApp',
                    color: const Color(0xFF25D366),
                    onTap: () => _shareToWhatsApp(currentImage),
                  ),
                  _buildExternalAppOption(
                    icon: Icons.telegram,
                    label: 'Telegram',
                    color: const Color(0xFF0088CC),
                    onTap: () => _shareToTelegram(currentImage),
                  ),
                  _buildExternalAppOption(
                    icon: Icons.email,
                    label: 'Email',
                    color: Colors.orange,
                    onTap: () => _shareViaEmail(currentImage),
                  ),
                  _buildExternalAppOption(
                    icon: Icons.sms,
                    label: 'SMS',
                    color: Colors.green,
                    onTap: () => _shareViaSMS(currentImage),
                  ),
                  _buildExternalAppOption(
                    icon: Icons.more_horiz,
                    label: 'المزيد',
                    color: Colors.grey,
                    onTap: () => _shareToOtherApps(currentImage),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في المشاركة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildExternalAppOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Colors.white, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _shareToFacebook(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Facebook للمشاركة! 📘'),
        backgroundColor: Color(0xFF1877F2),
      ),
    );
  }

  void _shareToInstagram(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Instagram للمشاركة! 📷'),
        backgroundColor: Color(0xFFE4405F),
      ),
    );
  }

  void _shareToTwitter(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Twitter للمشاركة! 🐦'),
        backgroundColor: Color(0xFF1DA1F2),
      ),
    );
  }

  void _shareToWhatsApp(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح WhatsApp للمشاركة! 💬'),
        backgroundColor: Color(0xFF25D366),
      ),
    );
  }

  void _shareToTelegram(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح Telegram للمشاركة! ✈️'),
        backgroundColor: Color(0xFF0088CC),
      ),
    );
  }

  void _shareViaEmail(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح البريد الإلكتروني للمشاركة! 📧'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _shareViaSMS(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح الرسائل النصية للمشاركة! 📱'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareToOtherApps(PostMedia image) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح قائمة التطبيقات الأخرى! 📤'),
        backgroundColor: Colors.grey,
      ),
    );
  }

  void _shareAsPost() {
    final currentImage = widget.images[_currentIndex];
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);

    // إنشاء منشور جديد بالصورة
    socialProvider.shareImageAsPost(
      imageUrl: currentImage.url,
      imageId: currentImage.id,
      originalPostId: widget.postId,
      authorName: widget.authorName,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.post_add, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('تم مشاركة الصورة كمنشور جديد! 📝'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _sendInMessage() {
    final currentImage = widget.images[_currentIndex];

    // إظهار قائمة الأصدقاء
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'إرسال في رسالة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // قائمة الأصدقاء
            ListView.builder(
              shrinkWrap: true,
              itemCount: 5, // عدد الأصدقاء التجريبي
              itemBuilder: (context, index) {
                final friends = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'عمر خالد'];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue,
                    child: Text(
                      friends[index][0],
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(
                    friends[index],
                    style: const TextStyle(color: Colors.white),
                  ),
                  subtitle: const Text(
                    'متصل الآن',
                    style: TextStyle(color: Colors.green),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _sendImageToFriend(currentImage, friends[index]);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _sendImageToFriend(PostMedia image, String friendName) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);

    // إرسال الصورة للصديق
    socialProvider.sendImageInMessage(
      imageUrl: image.url,
      imageId: image.id,
      recipientName: friendName,
      senderId: 'current_user',
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.message, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('تم إرسال الصورة إلى $friendName! 💬'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.purple,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _shareInGroup() {
    final currentImage = widget.images[_currentIndex];

    // إظهار قائمة المجموعات
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'مشاركة في مجموعة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // قائمة المجموعات
            ListView.builder(
              shrinkWrap: true,
              itemCount: 4, // عدد المجموعات التجريبي
              itemBuilder: (context, index) {
                final groups = ['مجموعة الأصدقاء', 'مجموعة العمل', 'مجموعة العائلة', 'مجموعة الهوايات'];
                final groupIcons = [Icons.group, Icons.work, Icons.family_restroom, Icons.sports_soccer];
                final groupColors = [Colors.blue, Colors.green, Colors.purple, Colors.orange];

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: groupColors[index],
                    child: Icon(
                      groupIcons[index],
                      color: Colors.white,
                    ),
                  ),
                  title: Text(
                    groups[index],
                    style: const TextStyle(color: Colors.white),
                  ),
                  subtitle: Text(
                    '${(index + 1) * 15} عضو',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _shareImageInGroup(currentImage, groups[index]);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _shareImageInGroup(PostMedia image, String groupName) {
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);

    // مشاركة الصورة في المجموعة
    socialProvider.shareImageInGroup(
      imageUrl: image.url,
      imageId: image.id,
      groupName: groupName,
      sharerId: 'current_user',
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.group, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('تم مشاركة الصورة في $groupName! 👥'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showImageOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'خيارات الصورة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            ListTile(
              leading: const Icon(Icons.info, color: Colors.blue),
              title: const Text('معلومات الصورة', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showImageInfo();
              },
            ),

            ListTile(
              leading: const Icon(Icons.copy, color: Colors.green),
              title: const Text('نسخ رابط الصورة', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _copyImageLink();
              },
            ),

            ListTile(
              leading: const Icon(Icons.report, color: Colors.red),
              title: const Text('الإبلاغ عن الصورة', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _reportImage();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showMoreOptions() {
    _showImageOptions();
  }

  void _showImageInfo() {
    final currentImage = widget.images[_currentIndex];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('معلومات الصورة', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المؤلف: ${widget.authorName}', style: const TextStyle(color: Colors.white)),
            const SizedBox(height: 8),
            Text('الصورة ${_currentIndex + 1} من ${widget.images.length}', style: const TextStyle(color: Colors.white)),
            const SizedBox(height: 8),
            Text('النوع: ${currentImage.url.startsWith('http') ? 'شبكة' : 'محلي'}', style: const TextStyle(color: Colors.white)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _copyImageLink() async {
    final currentImage = widget.images[_currentIndex];
    final imageLink = 'https://arzawo.app/image/${currentImage.id}';

    await Clipboard.setData(ClipboardData(text: imageLink));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ رابط الصورة! 🔗'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _reportImage() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('الإبلاغ عن الصورة', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'لماذا تريد الإبلاغ عن هذه الصورة؟',
              style: TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              dropdownColor: Colors.grey[800],
              decoration: InputDecoration(
                labelText: 'سبب الإبلاغ',
                labelStyle: const TextStyle(color: Colors.white),
                border: const OutlineInputBorder(),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey[600]!),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              style: const TextStyle(color: Colors.white),
              items: const [
                DropdownMenuItem(
                  value: 'inappropriate',
                  child: Text('محتوى غير لائق', style: TextStyle(color: Colors.white)),
                ),
                DropdownMenuItem(
                  value: 'spam',
                  child: Text('محتوى مزعج', style: TextStyle(color: Colors.white)),
                ),
                DropdownMenuItem(
                  value: 'harassment',
                  child: Text('تحرش أو تنمر', style: TextStyle(color: Colors.white)),
                ),
                DropdownMenuItem(
                  value: 'violence',
                  child: Text('عنف أو محتوى ضار', style: TextStyle(color: Colors.white)),
                ),
                DropdownMenuItem(
                  value: 'copyright',
                  child: Text('انتهاك حقوق الطبع', style: TextStyle(color: Colors.white)),
                ),
                DropdownMenuItem(
                  value: 'fake',
                  child: Text('محتوى مضلل', style: TextStyle(color: Colors.white)),
                ),
                DropdownMenuItem(
                  value: 'other',
                  child: Text('أخرى', style: TextStyle(color: Colors.white)),
                ),
              ],
              onChanged: (value) {
                _selectedReportReason = value;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _submitImageReport();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إبلاغ', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  String? _selectedReportReason;

  void _submitImageReport() {
    final currentImage = widget.images[_currentIndex];
    final socialProvider = Provider.of<SocialProvider>(context, listen: false);

    // إرسال البلاغ
    socialProvider.reportImage(
      imageId: currentImage.id,
      postId: widget.postId,
      reason: _selectedReportReason ?? 'inappropriate',
      description: 'تم الإبلاغ عن الصورة من عارض الصور',
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.report, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('تم إرسال البلاغ بنجاح! سيتم مراجعته قريباً 🚨'),
          ],
        ),
        duration: const Duration(seconds: 3),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
