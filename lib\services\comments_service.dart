import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/comment.dart';
import '../models/user.dart';
import '../models/reaction_types.dart';

class CommentsService {
  static const String _commentsKey = 'comments';
  
  // Stream للتحديث الفوري
  final StreamController<List<Comment>> _commentsController = 
      StreamController<List<Comment>>.broadcast();
  
  Stream<List<Comment>> get commentsStream => _commentsController.stream;
  
  // تحميل التعليقات لمنشور معين
  Future<List<Comment>> getComments(String postId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final commentsJson = prefs.getStringList('${_commentsKey}_$postId') ?? [];
      
      final comments = commentsJson
          .map((json) => Comment.fromJson(jsonDecode(json)))
          .toList();
      
      // ترتيب حسب التاريخ (الأقدم أولاً للتعليقات الرئيسية)
      comments.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      return comments;
    } catch (e) {
      print('خطأ في تحميل التعليقات: $e');
      return _generateSampleComments(postId);
    }
  }
  
  // حفظ التعليقات
  Future<void> _saveComments(String postId, List<Comment> comments) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final commentsJson = comments
          .map((comment) => jsonEncode(comment.toJson()))
          .toList();
      
      await prefs.setStringList('${_commentsKey}_$postId', commentsJson);
      
      // إرسال التحديث للمستمعين
      _commentsController.add(comments);
    } catch (e) {
      print('خطأ في حفظ التعليقات: $e');
    }
  }
  
  // إضافة تعليق جديد
  Future<Comment> addComment({
    required String postId,
    required String authorId,
    required User author,
    required String content,
    CommentType type = CommentType.text,
    String? mediaUrl,
    String? linkUrl,
    String? parentCommentId,
  }) async {
    final comment = Comment(
      id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
      postId: postId,
      authorId: authorId,
      author: author,
      content: content,
      type: type,
      mediaUrl: mediaUrl,
      linkUrl: linkUrl,
      timestamp: DateTime.now(),
      parentCommentId: parentCommentId,
    );
    
    final comments = await getComments(postId);
    
    if (parentCommentId != null) {
      // إضافة رد على تعليق
      final parentIndex = comments.indexWhere((c) => c.id == parentCommentId);
      if (parentIndex != -1) {
        final updatedParent = comments[parentIndex].copyWith(
          replies: [...comments[parentIndex].replies, comment],
          repliesCount: comments[parentIndex].repliesCount + 1,
        );
        comments[parentIndex] = updatedParent;
      }
    } else {
      // إضافة تعليق جديد
      comments.add(comment);
    }
    
    await _saveComments(postId, comments);
    return comment;
  }
  
  // تعديل تعليق
  Future<Comment> editComment({
    required String postId,
    required String commentId,
    required String newContent,
    String? newMediaUrl,
  }) async {
    final comments = await getComments(postId);
    
    // البحث في التعليقات الرئيسية
    for (int i = 0; i < comments.length; i++) {
      if (comments[i].id == commentId) {
        comments[i] = comments[i].copyWith(
          content: newContent,
          mediaUrl: newMediaUrl,
          editedAt: DateTime.now(),
          isEdited: true,
        );
        await _saveComments(postId, comments);
        return comments[i];
      }
      
      // البحث في الردود
      for (int j = 0; j < comments[i].replies.length; j++) {
        if (comments[i].replies[j].id == commentId) {
          final updatedReplies = [...comments[i].replies];
          updatedReplies[j] = updatedReplies[j].copyWith(
            content: newContent,
            mediaUrl: newMediaUrl,
            editedAt: DateTime.now(),
            isEdited: true,
          );
          
          comments[i] = comments[i].copyWith(replies: updatedReplies);
          await _saveComments(postId, comments);
          return updatedReplies[j];
        }
      }
    }
    
    throw Exception('التعليق غير موجود');
  }
  
  // حذف تعليق
  Future<void> deleteComment({
    required String postId,
    required String commentId,
    String? reason,
  }) async {
    final comments = await getComments(postId);
    
    // البحث في التعليقات الرئيسية
    for (int i = 0; i < comments.length; i++) {
      if (comments[i].id == commentId) {
        if (comments[i].hasReplies) {
          // إذا كان له ردود، نحذف المحتوى فقط
          comments[i] = comments[i].copyWith(
            content: '[تم حذف هذا التعليق]',
            isDeleted: true,
            deletedReason: reason,
          );
        } else {
          // حذف كامل إذا لم يكن له ردود
          comments.removeAt(i);
        }
        await _saveComments(postId, comments);
        return;
      }
      
      // البحث في الردود
      for (int j = 0; j < comments[i].replies.length; j++) {
        if (comments[i].replies[j].id == commentId) {
          final updatedReplies = [...comments[i].replies];
          updatedReplies.removeAt(j);
          
          comments[i] = comments[i].copyWith(
            replies: updatedReplies,
            repliesCount: comments[i].repliesCount - 1,
          );
          await _saveComments(postId, comments);
          return;
        }
      }
    }
  }
  
  // إضافة تفاعل
  Future<void> addReaction({
    required String postId,
    required String commentId,
    required String userId,
    required String userName,
    required ReactionType reactionType,
  }) async {
    final comments = await getComments(postId);
    
    // البحث عن التعليق وإضافة التفاعل
    for (int i = 0; i < comments.length; i++) {
      if (comments[i].id == commentId) {
        comments[i] = _addReactionToComment(comments[i], userId, userName, reactionType);
        await _saveComments(postId, comments);
        return;
      }
      
      // البحث في الردود
      for (int j = 0; j < comments[i].replies.length; j++) {
        if (comments[i].replies[j].id == commentId) {
          final updatedReplies = [...comments[i].replies];
          updatedReplies[j] = _addReactionToComment(updatedReplies[j], userId, userName, reactionType);
          
          comments[i] = comments[i].copyWith(replies: updatedReplies);
          await _saveComments(postId, comments);
          return;
        }
      }
    }
  }
  
  // إزالة تفاعل
  Future<void> removeReaction({
    required String postId,
    required String commentId,
    required String userId,
  }) async {
    final comments = await getComments(postId);
    
    // البحث عن التعليق وإزالة التفاعل
    for (int i = 0; i < comments.length; i++) {
      if (comments[i].id == commentId) {
        comments[i] = _removeReactionFromComment(comments[i], userId);
        await _saveComments(postId, comments);
        return;
      }
      
      // البحث في الردود
      for (int j = 0; j < comments[i].replies.length; j++) {
        if (comments[i].replies[j].id == commentId) {
          final updatedReplies = [...comments[i].replies];
          updatedReplies[j] = _removeReactionFromComment(updatedReplies[j], userId);
          
          comments[i] = comments[i].copyWith(replies: updatedReplies);
          await _saveComments(postId, comments);
          return;
        }
      }
    }
  }
  
  // دالة مساعدة لإضافة التفاعل
  Comment _addReactionToComment(Comment comment, String userId, String userName, ReactionType reactionType) {
    final reactions = [...comment.reactions];
    final reactionCounts = Map<ReactionType, int>.from(comment.reactionCounts);
    
    // إزالة التفاعل السابق إن وجد
    reactions.removeWhere((r) => r.userId == userId);
    
    // تحديث العدادات
    for (final type in ReactionType.values) {
      final oldCount = comment.reactions.where((r) => r.type == type && r.userId != userId).length;
      final newCount = type == reactionType ? oldCount + 1 : oldCount;
      
      if (newCount > 0) {
        reactionCounts[type] = newCount;
      } else {
        reactionCounts.remove(type);
      }
    }
    
    // إضافة التفاعل الجديد
    reactions.add(CommentReaction(
      id: 'reaction_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      userName: userName,
      type: reactionType,
      timestamp: DateTime.now(),
    ));
    
    return comment.copyWith(
      reactions: reactions,
      reactionCounts: reactionCounts,
      totalReactions: reactions.length,
    );
  }
  
  // دالة مساعدة لإزالة التفاعل
  Comment _removeReactionFromComment(Comment comment, String userId) {
    final reactions = comment.reactions.where((r) => r.userId != userId).toList();
    final reactionCounts = <ReactionType, int>{};
    
    // إعادة حساب العدادات
    for (final reaction in reactions) {
      reactionCounts[reaction.type] = (reactionCounts[reaction.type] ?? 0) + 1;
    }
    
    return comment.copyWith(
      reactions: reactions,
      reactionCounts: reactionCounts,
      totalReactions: reactions.length,
    );
  }
  
  // رفع ملف (صورة أو فيديو)
  Future<String> uploadMedia(File file) async {
    // محاكاة رفع الملف
    await Future.delayed(const Duration(seconds: 2));
    return 'https://example.com/media/${file.path.split('/').last}';
  }
  
  // استخراج معلومات الرابط
  Future<Map<String, String?>> extractLinkInfo(String url) async {
    // محاكاة استخراج معلومات الرابط
    await Future.delayed(const Duration(seconds: 1));
    
    return {
      'title': 'عنوان الرابط',
      'description': 'وصف الرابط',
      'image': 'https://example.com/link-preview.jpg',
    };
  }
  
  // إنشاء تعليقات تجريبية
  List<Comment> _generateSampleComments(String postId) {
    final now = DateTime.now();
    
    return [
      Comment(
        id: 'comment_1',
        postId: postId,
        authorId: 'user_1',
        author: User(
          id: 'user_1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          avatar: null,
          gender: 'male',
          joinDate: DateTime.now(),
        ),
        content: 'تعليق رائع! شكراً لك على المشاركة 👍',
        timestamp: now.subtract(const Duration(hours: 2)),
        reactions: [
          CommentReaction(
            id: 'reaction_1',
            userId: 'user_2',
            userName: 'فاطمة أحمد',
            type: ReactionType.like,
            timestamp: now.subtract(const Duration(hours: 1)),
          ),
        ],
        reactionCounts: {ReactionType.like: 1},
        totalReactions: 1,
        replies: [
          Comment(
            id: 'reply_1',
            postId: postId,
            authorId: 'user_2',
            author: User(
              id: 'user_2',
              name: 'فاطمة أحمد',
              email: '<EMAIL>',
              avatar: null,
              gender: 'female',
              joinDate: DateTime.now(),
            ),
            content: 'أتفق معك تماماً!',
            timestamp: now.subtract(const Duration(hours: 1)),
            parentCommentId: 'comment_1',
          ),
        ],
        repliesCount: 1,
      ),
      Comment(
        id: 'comment_2',
        postId: postId,
        authorId: 'user_3',
        author: User(
          id: 'user_3',
          name: 'سعيد علي',
          email: '<EMAIL>',
          avatar: null,
          gender: 'male',
          joinDate: DateTime.now(),
        ),
        content: 'معلومات مفيدة جداً، شكراً لك ❤️',
        timestamp: now.subtract(const Duration(minutes: 30)),
        reactions: [
          CommentReaction(
            id: 'reaction_2',
            userId: 'user_1',
            userName: 'أحمد محمد',
            type: ReactionType.love,
            timestamp: now.subtract(const Duration(minutes: 25)),
          ),
        ],
        reactionCounts: {ReactionType.love: 1},
        totalReactions: 1,
      ),
    ];
  }
  
  void dispose() {
    _commentsController.close();
  }
}
