import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/mock_data_service.dart';

class AuthProvider with ChangeNotifier {
  final MockDataService _dataService = MockDataService();
  
  User? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _currentUser != null;

  Future<void> checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();

    try {
      _currentUser = await _dataService.getCurrentUser();
    } catch (e) {
      _errorMessage = 'خطأ في التحقق من حالة المصادقة';
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final success = await _dataService.login(email, password);
      if (success) {
        _currentUser = await _dataService.getCurrentUser();
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      }
    } catch (e) {
      _errorMessage = 'خطأ في تسجيل الدخول';
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _dataService.logout();
      _currentUser = null;
    } catch (e) {
      _errorMessage = 'خطأ في تسجيل الخروج';
    }

    _isLoading = false;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
