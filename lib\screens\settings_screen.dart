import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_settings_provider.dart';
import '../models/app_settings.dart' as app_settings;
import '../screens/saved_items_screen.dart';
import '../screens/blocked_users_screen.dart';
import '../screens/report_problem_screen.dart';
import '../screens/connectivity_settings_screen.dart';
import '../screens/account_management_screen.dart';
import '../services/auth_service.dart';
import '../screens/facebook_login_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'الإعدادات',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<AppSettingsProvider>(
        builder: (context, settingsProvider, child) {
          if (settingsProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // العناصر المحفوظة
              _buildSettingsSection(
                title: 'المحتوى',
                icon: Icons.bookmark,
                children: [
                  _buildListTile(
                    title: 'العناصر المحفوظة',
                    subtitle: 'المنشورات والصور والفيديوهات المحفوظة',
                    icon: Icons.bookmark_outline,
                    onTap: () => _navigateToSavedItems(context),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // إعدادات الخصوصية والأمان
              _buildSettingsSection(
                title: 'الخصوصية والأمان',
                icon: Icons.privacy_tip,
                children: [
                  _buildSwitchTile(
                    title: 'إيصالات القراءة',
                    subtitle: 'إظهار علامة القراءة في الرسائل',
                    value: settingsProvider.readReceiptsEnabled,
                    onChanged: (value) => settingsProvider.updateReadReceipts(value),
                  ),
                  _buildListTile(
                    title: 'آخر ظهور',
                    subtitle: _getPrivacyDisplayText(settingsProvider.lastSeenPrivacy),
                    icon: Icons.access_time,
                    onTap: () => _showPrivacyDialog(
                      context,
                      'آخر ظهور',
                      settingsProvider.lastSeenPrivacy,
                      (value) => settingsProvider.updateLastSeenPrivacy(value),
                    ),
                  ),
                  _buildListTile(
                    title: 'صورة الملف الشخصي',
                    subtitle: _getPrivacyDisplayText(settingsProvider.profilePhotoPrivacy),
                    icon: Icons.account_circle,
                    onTap: () => _showPrivacyDialog(
                      context,
                      'صورة الملف الشخصي',
                      settingsProvider.profilePhotoPrivacy,
                      (value) => settingsProvider.updateProfilePhotoPrivacy(value),
                    ),
                  ),
                  _buildListTile(
                    title: 'الحالة',
                    subtitle: _getPrivacyDisplayText(settingsProvider.statusPrivacy),
                    icon: Icons.info_outline,
                    onTap: () => _showPrivacyDialog(
                      context,
                      'الحالة',
                      settingsProvider.statusPrivacy,
                      (value) => settingsProvider.updateStatusPrivacy(value),
                    ),
                  ),
                  _buildListTile(
                    title: 'الحسابات المحظورة',
                    subtitle: '${settingsProvider.blockedUsers.length} حساب محظور',
                    icon: Icons.block,
                    onTap: () => _navigateToBlockedUsers(context),
                  ),
                  _buildListTile(
                    title: 'تقرير مشكلة',
                    subtitle: 'الإبلاغ عن مشكلة أو محتوى غير مناسب',
                    icon: Icons.report,
                    onTap: () => _navigateToReportProblem(context),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // إعدادات المظهر
              _buildSettingsSection(
                title: 'المظهر',
                icon: Icons.palette,
                children: [
                  _buildSwitchTile(
                    title: 'الوضع الليلي',
                    subtitle: 'تفعيل المظهر الداكن',
                    value: settingsProvider.isDarkMode,
                    onChanged: (value) => settingsProvider.updateThemeMode(
                      value ? app_settings.ThemeMode.dark : app_settings.ThemeMode.light,
                    ),
                  ),
                  _buildListTile(
                    title: 'حجم الخط',
                    subtitle: settingsProvider.settings.fontSizeDisplayName,
                    icon: Icons.text_fields,
                    onTap: () => _showFontSizeDialog(context, settingsProvider),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // إعدادات التحميل
              _buildSettingsSection(
                title: 'التحميل التلقائي',
                icon: Icons.download,
                children: [
                  _buildListTile(
                    title: 'الصور',
                    subtitle: _getAutoDownloadDisplayText(settingsProvider.autoDownloadPhotos),
                    icon: Icons.image,
                    onTap: () => _showAutoDownloadDialog(
                      context,
                      'الصور',
                      settingsProvider.autoDownloadPhotos,
                      (value) => settingsProvider.updateAutoDownloadPhotos(value),
                    ),
                  ),
                  _buildListTile(
                    title: 'الفيديوهات',
                    subtitle: _getAutoDownloadDisplayText(settingsProvider.autoDownloadVideos),
                    icon: Icons.video_library,
                    onTap: () => _showAutoDownloadDialog(
                      context,
                      'الفيديوهات',
                      settingsProvider.autoDownloadVideos,
                      (value) => settingsProvider.updateAutoDownloadVideos(value),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // إعدادات التخزين
              _buildSettingsSection(
                title: 'التخزين',
                icon: Icons.storage,
                children: [
                  _buildListTile(
                    title: 'مسح ذاكرة التخزين المؤقت',
                    subtitle: 'المساحة المستخدمة: ${settingsProvider.settings.formattedCacheSize}',
                    icon: Icons.cleaning_services,
                    onTap: () => _showClearCacheDialog(context, settingsProvider),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // إعدادات الاتصال والشبكة
              _buildSettingsSection(
                title: 'الاتصال والشبكة',
                icon: Icons.wifi,
                children: [
                  _buildListTile(
                    title: 'إعدادات الاتصال',
                    subtitle: 'إدارة الاتصال والتخزين المؤقت',
                    icon: Icons.network_check,
                    onTap: () => _navigateToConnectivitySettings(context),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // إعدادات النسخ الاحتياطي
              _buildSettingsSection(
                title: 'النسخ الاحتياطي',
                icon: Icons.backup,
                children: [
                  _buildSwitchTile(
                    title: 'النسخ الاحتياطي التلقائي',
                    subtitle: settingsProvider.backupEnabled
                        ? 'آخر نسخة: ${settingsProvider.settings.formattedLastBackupDate ?? "لم يتم"}'
                        : 'غير مفعل',
                    value: settingsProvider.backupEnabled,
                    onChanged: (value) => settingsProvider.updateBackupEnabled(value),
                  ),
                  if (settingsProvider.backupEnabled)
                    _buildListTile(
                      title: 'إعدادات النسخ الاحتياطي',
                      subtitle: 'إدارة النسخ الاحتياطي واستعادة البيانات',
                      icon: Icons.settings_backup_restore,
                      onTap: () => _navigateToBackupSettings(context),
                    ),
                ],
              ),

              const SizedBox(height: 20),

              // إدارة الحساب
              _buildSettingsSection(
                title: 'إدارة الحساب',
                icon: Icons.account_circle,
                children: [
                  _buildListTile(
                    title: 'إدارة الحساب',
                    subtitle: 'إلغاء التنشيط أو حذف الحساب',
                    icon: Icons.manage_accounts,
                    onTap: () => _navigateToAccountManagement(context),
                  ),
                  _buildListTile(
                    title: 'تسجيل الخروج',
                    subtitle: 'الخروج من الحساب الحالي',
                    icon: Icons.logout,
                    onTap: () => _showLogoutDialog(context),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // معلومات التطبيق
              _buildSettingsSection(
                title: 'معلومات التطبيق',
                icon: Icons.info,
                children: [
                  _buildListTile(
                    title: 'الإصدار',
                    subtitle: '1.0.0',
                    icon: Icons.info_outline,
                    onTap: null,
                  ),
                  _buildListTile(
                    title: 'شروط الاستخدام',
                    subtitle: 'قراءة شروط وأحكام الاستخدام',
                    icon: Icons.description,
                    onTap: () => _showTermsOfService(context),
                  ),
                  _buildListTile(
                    title: 'سياسة الخصوصية',
                    subtitle: 'قراءة سياسة الخصوصية',
                    icon: Icons.policy,
                    onTap: () => _showPrivacyPolicy(context),
                  ),
                ],
              ),

              const SizedBox(height: 40),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8, bottom: 12),
          child: Row(
            children: [
              Icon(
                icon,
                color: Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.blue,
    );
  }

  Widget _buildListTile({
    required String title,
    required String subtitle,
    required IconData icon,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      trailing: onTap != null
          ? Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16)
          : null,
      onTap: onTap,
    );
  }

  String _getPrivacyDisplayText(app_settings.PrivacySetting privacy) {
    switch (privacy) {
      case app_settings.PrivacySetting.everyone:
        return 'الجميع';
      case app_settings.PrivacySetting.friends:
        return 'الأصدقاء فقط';
      case app_settings.PrivacySetting.nobody:
        return 'لا أحد';
    }
  }

  String _getAutoDownloadDisplayText(app_settings.AutoDownloadSetting setting) {
    switch (setting) {
      case app_settings.AutoDownloadSetting.never:
        return 'أبداً';
      case app_settings.AutoDownloadSetting.wifiOnly:
        return 'Wi-Fi فقط';
      case app_settings.AutoDownloadSetting.always:
        return 'دائماً';
    }
  }

  void _navigateToSavedItems(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SavedItemsScreen(),
      ),
    );
  }

  void _navigateToBlockedUsers(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BlockedUsersScreen(),
      ),
    );
  }

  void _navigateToReportProblem(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ReportProblemScreen(),
      ),
    );
  }

  void _navigateToConnectivitySettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ConnectivitySettingsScreen(),
      ),
    );
  }

  void _navigateToBackupSettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('شاشة إعدادات النسخ الاحتياطي قريباً!')),
    );
  }

  void _showPrivacyDialog(
    BuildContext context,
    String title,
    app_settings.PrivacySetting currentValue,
    Function(app_settings.PrivacySetting) onChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إعدادات $title'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: app_settings.PrivacySetting.values.map((privacy) {
            return RadioListTile<app_settings.PrivacySetting>(
              title: Text(_getPrivacyDisplayText(privacy)),
              value: privacy,
              groupValue: currentValue,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context, AppSettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: app_settings.FontSize.values.map((fontSize) {
            return RadioListTile<app_settings.FontSize>(
              title: Text(_getFontSizeDisplayText(fontSize)),
              value: fontSize,
              groupValue: provider.fontSize,
              onChanged: (value) {
                if (value != null) {
                  provider.updateFontSize(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showAutoDownloadDialog(
    BuildContext context,
    String title,
    app_settings.AutoDownloadSetting currentValue,
    Function(app_settings.AutoDownloadSetting) onChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تحميل $title'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: app_settings.AutoDownloadSetting.values.map((setting) {
            return RadioListTile<app_settings.AutoDownloadSetting>(
              title: Text(_getAutoDownloadDisplayText(setting)),
              value: setting,
              groupValue: currentValue,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context, AppSettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح ذاكرة التخزين المؤقت'),
        content: Text(
          'هل تريد مسح جميع الملفات المؤقتة؟\n'
          'المساحة المستخدمة: ${provider.settings.formattedCacheSize}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await provider.clearCache();
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم مسح ذاكرة التخزين المؤقت')),
                );
              }
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('شروط الاستخدام'),
        content: const SingleChildScrollView(
          child: Text(
            'شروط وأحكام استخدام تطبيق Arzawo:\n\n'
            '1. يجب استخدام التطبيق بطريقة قانونية ومسؤولة\n'
            '2. عدم نشر محتوى مسيء أو غير لائق\n'
            '3. احترام خصوصية المستخدمين الآخرين\n'
            '4. عدم انتهاك حقوق الملكية الفكرية\n'
            '5. الالتزام بقوانين البلد المحلية\n\n'
            'للمزيد من التفاصيل، يرجى زيارة موقعنا الإلكتروني.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سياسة الخصوصية'),
        content: const SingleChildScrollView(
          child: Text(
            'سياسة الخصوصية لتطبيق Arzawo:\n\n'
            '1. نحن نحترم خصوصيتك ونحمي بياناتك الشخصية\n'
            '2. لا نشارك معلوماتك مع أطراف ثالثة دون موافقتك\n'
            '3. نستخدم التشفير لحماية بياناتك\n'
            '4. يمكنك حذف حسابك وبياناتك في أي وقت\n'
            '5. نجمع فقط البيانات الضرورية لتشغيل التطبيق\n\n'
            'للمزيد من التفاصيل، يرجى زيارة موقعنا الإلكتروني.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _navigateToAccountManagement(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AccountManagementScreen(),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              // استخدام AuthService الجديد
              final authService = AuthService();
              await authService.logout();

              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const FacebookLoginScreen()),
                  (route) => false,
                );
              }
            },
            child: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  String _getFontSizeDisplayText(app_settings.FontSize fontSize) {
    switch (fontSize) {
      case app_settings.FontSize.small:
        return 'صغير';
      case app_settings.FontSize.medium:
        return 'متوسط';
      case app_settings.FontSize.large:
        return 'كبير';
      case app_settings.FontSize.extraLarge:
        return 'كبير جداً';
    }
  }
}