import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/message.dart';
import '../models/user.dart';
import '../models/chat.dart';
import '../theme/app_theme.dart';

class MessageContextMenu extends StatelessWidget {
  final Message message;
  final bool isMe;
  final VoidCallback? onReply;
  final VoidCallback? onForward;
  final VoidCallback? onShare;
  final VoidCallback? onPin;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final Function(String emoji)? onReact;

  const MessageContextMenu({
    super.key,
    required this.message,
    required this.isMe,
    this.onReply,
    this.onForward,
    this.onShare,
    this.onPin,
    this.onEdit,
    this.onDelete,
    this.onReact,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // تفاعلات الإيموجي
          _buildReactionsRow(),
          
          const Divider(height: 1),
          
          // خيارات الإجراءات
          _buildActionsColumn(context),
        ],
      ),
    );
  }

  Widget _buildReactionsRow() {
    final reactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: reactions.map((emoji) {
          final isSelected = message.reactions.containsValue(emoji);
          
          return GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              onReact?.call(emoji);
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryColor.withValues(alpha: 0.1) : Colors.transparent,
                shape: BoxShape.circle,
                border: isSelected ? Border.all(color: AppTheme.primaryColor, width: 2) : null,
              ),
              child: Center(
                child: Text(
                  emoji,
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionsColumn(BuildContext context) {
    return Column(
      children: [
        // الرد على الرسالة
        _buildActionTile(
          icon: Icons.reply,
          title: 'رد',
          onTap: () {
            Navigator.pop(context);
            onReply?.call();
          },
        ),
        
        // إعادة توجيه
        _buildActionTile(
          icon: Icons.forward,
          title: 'إعادة توجيه',
          onTap: () {
            Navigator.pop(context);
            onForward?.call();
          },
        ),
        
        // مشاركة
        _buildActionTile(
          icon: Icons.share,
          title: 'مشاركة',
          onTap: () {
            Navigator.pop(context);
            onShare?.call();
          },
        ),
        
        // تثبيت/إلغاء تثبيت
        _buildActionTile(
          icon: message.isPinned ? Icons.push_pin : Icons.push_pin_outlined,
          title: message.isPinned ? 'إلغاء التثبيت' : 'تثبيت',
          onTap: () {
            Navigator.pop(context);
            onPin?.call();
          },
        ),
        
        // نسخ النص (للرسائل النصية فقط)
        if (message.type == MessageType.text)
          _buildActionTile(
            icon: Icons.copy,
            title: 'نسخ النص',
            onTap: () {
              Navigator.pop(context);
              Clipboard.setData(ClipboardData(text: message.content));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم نسخ النص')),
              );
            },
          ),
        
        // تعديل (للمرسل فقط)
        if (isMe && message.type == MessageType.text)
          _buildActionTile(
            icon: Icons.edit,
            title: 'تعديل',
            onTap: () {
              Navigator.pop(context);
              onEdit?.call();
            },
          ),
        
        // حذف (للمرسل فقط)
        if (isMe)
          _buildActionTile(
            icon: Icons.delete,
            title: 'حذف',
            color: Colors.red,
            onTap: () {
              Navigator.pop(context);
              onDelete?.call();
            },
          ),
      ],
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? Colors.black87),
      title: Text(
        title,
        style: TextStyle(
          color: color ?? Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }
}

// Dialog لإعادة التوجيه
class ForwardMessageDialog extends StatefulWidget {
  final Message message;
  final List<Chat> contacts;

  const ForwardMessageDialog({
    super.key,
    required this.message,
    required this.contacts,
  });

  @override
  State<ForwardMessageDialog> createState() => _ForwardMessageDialogState();
}

class _ForwardMessageDialogState extends State<ForwardMessageDialog> {
  final List<Chat> _selectedChats = [];
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final filteredContacts = widget.contacts.where((chat) {
      return chat.otherUser.name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // العنوان
            Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
                const Expanded(
                  child: Text(
                    'إعادة توجيه إلى',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                TextButton(
                  onPressed: _selectedChats.isNotEmpty ? _forwardMessage : null,
                  child: const Text('إرسال'),
                ),
              ],
            ),
            
            const Divider(),
            
            // شريط البحث
            TextField(
              decoration: const InputDecoration(
                hintText: 'البحث في جهات الاتصال...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // قائمة جهات الاتصال
            Expanded(
              child: ListView.builder(
                itemCount: filteredContacts.length,
                itemBuilder: (context, index) {
                  final chat = filteredContacts[index];
                  final isSelected = _selectedChats.contains(chat);

                  return CheckboxListTile(
                    value: isSelected,
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _selectedChats.add(chat);
                        } else {
                          _selectedChats.remove(chat);
                        }
                      });
                    },
                    title: Text(chat.otherUser.name),
                    subtitle: Text(chat.lastMessage?.content ?? 'لا توجد رسائل'),
                    secondary: CircleAvatar(
                      backgroundImage: chat.otherUser.avatar != null
                          ? NetworkImage(chat.otherUser.avatar!)
                          : null,
                      child: chat.otherUser.avatar == null
                          ? Text(chat.otherUser.name[0].toUpperCase())
                          : null,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _forwardMessage() {
    // تنفيذ إعادة التوجيه
    Navigator.pop(context, _selectedChats);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إعادة توجيه الرسالة إلى ${_selectedChats.length} محادثات'),
      ),
    );
  }
}
