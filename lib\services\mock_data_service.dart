import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user.dart';
import '../models/message.dart';
import '../models/chat.dart';

class MockDataService {
  static const String _usersKey = 'mock_users';
  static const String _messagesKey = 'mock_messages';
  static const String _currentUserKey = 'current_user';
  
  final Uuid _uuid = const Uuid();
  
  // بيانات تجريبية للمستخدمين
  List<User> get mockUsers => [
    User(
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      isOnline: true,
      joinDate: DateTime.now().subtract(const Duration(days: 365)),
      bio: 'مطور تطبيقات محترف',
      city: 'الرياض',
      country: 'السعودية',
      work: 'مطور Flutter',
      education: 'جامعة الملك سعود',
      followersCount: 1250,
      followingCount: 856,
      postsCount: 127,
    ),
    User(
      id: '2',
      name: 'فاطمة علي',
      email: '<EMAIL>',
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 30)),
      joinDate: DateTime.now().subtract(const Duration(days: 200)),
      bio: 'مصممة UI/UX',
      city: 'جدة',
      country: 'السعودية',
      work: 'مصممة',
      education: 'جامعة الملك عبدالعزيز',
      followersCount: 890,
      followingCount: 432,
      postsCount: 89,
    ),
    User(
      id: '3',
      name: 'محمد حسن',
      email: '<EMAIL>',
      isOnline: true,
      joinDate: DateTime.now().subtract(const Duration(days: 150)),
      bio: 'مهندس برمجيات',
      city: 'الدمام',
      country: 'السعودية',
      work: 'مهندس',
      education: 'جامعة الملك فهد',
      followersCount: 567,
      followingCount: 234,
      postsCount: 45,
    ),
    User(
      id: '4',
      name: 'عائشة أحمد',
      email: '<EMAIL>',
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      joinDate: DateTime.now().subtract(const Duration(days: 300)),
      bio: 'كاتبة ومدونة',
      city: 'مكة',
      country: 'السعودية',
      work: 'كاتبة',
      education: 'جامعة أم القرى',
      followersCount: 2100,
      followingCount: 678,
      postsCount: 234,
    ),
    User(
      id: '5',
      name: 'عمر خالد',
      email: '<EMAIL>',
      isOnline: true,
      joinDate: DateTime.now().subtract(const Duration(days: 90)),
      bio: 'مصور فوتوغرافي',
      city: 'المدينة المنورة',
      country: 'السعودية',
      work: 'مصور',
      education: 'جامعة طيبة',
      followersCount: 1890,
      followingCount: 345,
      postsCount: 156,
    ),
  ];

  // بيانات تجريبية للرسائل
  List<Message> get mockMessages => [
    // رسائل نصية عادية
    Message(
      id: _uuid.v4(),
      senderId: '1',
      receiverId: 'current_user',
      content: 'مرحباً! كيف حالك؟ 😊',
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      isRead: true,
      status: MessageStatus.read,
    ),
    Message(
      id: _uuid.v4(),
      senderId: 'current_user',
      receiverId: '1',
      content: 'أهلاً وسهلاً، بخير والحمد لله 🙏',
      timestamp: DateTime.now().subtract(const Duration(minutes: 3)),
      isRead: true,
      status: MessageStatus.read,
    ),

    // رسالة صورة
    Message(
      id: _uuid.v4(),
      senderId: '2',
      receiverId: 'current_user',
      content: 'شاهد هذه الصورة الجميلة!',
      type: MessageType.image,
      mediaUrl: 'https://picsum.photos/400/300',
      timestamp: DateTime.now().subtract(const Duration(hours: 1)),
      isRead: false,
      status: MessageStatus.delivered,
    ),

    // رسالة صوتية
    Message(
      id: _uuid.v4(),
      senderId: '3',
      receiverId: 'current_user',
      content: 'رسالة صوتية',
      type: MessageType.audio,
      audioDuration: const Duration(seconds: 45),
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      isRead: true,
      status: MessageStatus.read,
    ),

    // رسالة موقع
    Message(
      id: _uuid.v4(),
      senderId: '4',
      receiverId: 'current_user',
      content: 'موقعي الحالي',
      type: MessageType.location,
      latitude: 24.7136,
      longitude: 46.6753,
      locationName: 'الرياض، المملكة العربية السعودية',
      timestamp: DateTime.now().subtract(const Duration(hours: 3)),
      isRead: false,
      status: MessageStatus.delivered,
    ),

    // رسالة إيموجي كبيرة
    Message(
      id: _uuid.v4(),
      senderId: '1',
      receiverId: 'current_user',
      content: '🎉🎊🥳',
      type: MessageType.emoji,
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      isRead: true,
      status: MessageStatus.read,
    ),
  ];

  Future<bool> login(String email, String password) async {
    // محاكاة تسجيل الدخول
    if (email.isNotEmpty && password.length >= 6) {
      final prefs = await SharedPreferences.getInstance();
      final currentUser = User(
        id: 'current_user',
        name: 'المستخدم الحالي',
        email: email,
        isOnline: true,
        joinDate: DateTime.now().subtract(const Duration(days: 30)),
        bio: 'مرحباً بكم في ملفي الشخصي!',
        city: 'الرياض',
        country: 'السعودية',
        work: 'مستخدم تطبيق Arzawo',
        education: 'جامعة',
        followersCount: 42,
        followingCount: 38,
        postsCount: 15,
      );
      
      await prefs.setString(_currentUserKey, jsonEncode(currentUser.toJson()));
      await _initializeMockData();
      return true;
    }
    return false;
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentUserKey);
  }

  Future<User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_currentUserKey);
    if (userJson != null) {
      return User.fromJson(jsonDecode(userJson));
    }
    return null;
  }

  Future<List<Chat>> getChats() async {
    final users = mockUsers;
    final messages = await getMessages();
    
    return users.map((user) {
      final userMessages = messages
          .where((m) => m.senderId == user.id || m.receiverId == user.id)
          .toList();
      
      userMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      final unreadCount = userMessages
          .where((m) => m.senderId == user.id && !m.isRead)
          .length;
      
      return Chat(
        id: user.id,
        otherUser: user,
        lastMessage: userMessages.isNotEmpty ? userMessages.first : null,
        unreadCount: unreadCount,
        lastActivity: userMessages.isNotEmpty 
            ? userMessages.first.timestamp 
            : DateTime.now(),
      );
    }).toList();
  }

  Future<List<Message>> getMessages([String? chatId]) async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson = prefs.getStringList(_messagesKey) ?? [];
    
    final messages = messagesJson
        .map((json) => Message.fromJson(jsonDecode(json)))
        .toList();
    
    if (chatId != null) {
      return messages
          .where((m) => m.senderId == chatId || m.receiverId == chatId)
          .toList();
    }
    
    return messages;
  }

  Future<void> sendMessage(String receiverId, String content, {MessageType type = MessageType.text}) async {
    final message = Message(
      id: _uuid.v4(),
      senderId: 'current_user',
      receiverId: receiverId,
      content: content,
      type: type,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
    );

    await _saveMessage(message);
  }

  Future<void> _saveMessage(Message message) async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson = prefs.getStringList(_messagesKey) ?? [];
    messagesJson.add(jsonEncode(message.toJson()));
    await prefs.setStringList(_messagesKey, messagesJson);
  }

  Future<void> _initializeMockData() async {
    final prefs = await SharedPreferences.getInstance();
    
    // حفظ المستخدمين التجريبيين
    final usersJson = mockUsers.map((u) => jsonEncode(u.toJson())).toList();
    await prefs.setStringList(_usersKey, usersJson);
    
    // حفظ الرسائل التجريبية
    final messagesJson = mockMessages.map((m) => jsonEncode(m.toJson())).toList();
    await prefs.setStringList(_messagesKey, messagesJson);
  }

  // تحديث صورة الغلاف
  Future<void> updateUserCoverImage(String userId, String coverImageUrl) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // في التطبيق الحقيقي، سيتم تحديث قاعدة البيانات
  }

  // تحديث صورة الملف الشخصي
  Future<void> updateUserProfileImage(String userId, String profileImageUrl) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // في التطبيق الحقيقي، سيتم تحديث قاعدة البيانات
  }

  // تحديث بيانات المستخدم
  Future<void> updateUser(User user) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
  }

  // إنشاء رسائل تجريبية لمحادثة معينة
  List<Message> getMockMessages(String chatId) {
    return [
      Message(
        id: 'msg_1_$chatId',
        senderId: 'other_user',
        receiverId: 'current_user',
        content: 'مرحبا! كيف حالك؟',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        type: MessageType.text,
        status: MessageStatus.read,
      ),
      Message(
        id: 'msg_2_$chatId',
        senderId: 'current_user',
        receiverId: 'other_user',
        content: 'أهلاً وسهلاً! بخير والحمد لله، وأنت كيف حالك؟',
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 45)),
        type: MessageType.text,
        status: MessageStatus.read,
      ),
      Message(
        id: 'msg_3_$chatId',
        senderId: 'other_user',
        receiverId: 'current_user',
        content: 'الحمد لله بخير، هل أنت متفرغ للحديث؟',
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
        type: MessageType.text,
        status: MessageStatus.read,
      ),
      Message(
        id: 'msg_4_$chatId',
        senderId: 'current_user',
        receiverId: 'other_user',
        content: 'نعم متفرغ، تفضل',
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 15)),
        type: MessageType.text,
        status: MessageStatus.read,
      ),
      Message(
        id: 'msg_5_$chatId',
        senderId: 'other_user',
        receiverId: 'current_user',
        content: 'ممتاز! أردت أن أسألك عن المشروع الجديد',
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        type: MessageType.text,
        status: MessageStatus.delivered,
      ),
    ];
  }

  // إنشاء محادثات تجريبية
  List<Chat> generateMockChats() {
    return [
      Chat(
        id: 'chat_1',
        otherUser: mockUsers[0],
        lastMessage: Message(
          id: 'msg_1',
          senderId: mockUsers[0].id,
          receiverId: 'current_user',
          content: 'مرحبا! كيف حالك؟',
          timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
          type: MessageType.text,
          status: MessageStatus.delivered,
        ),
        lastActivity: DateTime.now().subtract(const Duration(minutes: 5)),
        unreadCount: 2,
      ),
      Chat(
        id: 'chat_2',
        otherUser: mockUsers[1],
        lastMessage: Message(
          id: 'msg_2',
          senderId: 'current_user',
          receiverId: mockUsers[1].id,
          content: 'شكراً لك على المساعدة',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          type: MessageType.text,
          status: MessageStatus.read,
        ),
        lastActivity: DateTime.now().subtract(const Duration(hours: 1)),
        unreadCount: 0,
      ),
      Chat(
        id: 'chat_3',
        otherUser: mockUsers[2],
        lastMessage: Message(
          id: 'msg_3',
          senderId: mockUsers[2].id,
          receiverId: 'current_user',
          content: 'هل يمكننا الاجتماع غداً؟',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
          type: MessageType.text,
          status: MessageStatus.delivered,
        ),
        lastActivity: DateTime.now().subtract(const Duration(hours: 3)),
        unreadCount: 1,
      ),
    ];
  }
}
