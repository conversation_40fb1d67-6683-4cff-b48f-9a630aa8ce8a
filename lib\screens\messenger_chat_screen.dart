import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../providers/messenger_provider.dart';
import '../models/chat.dart';
import '../models/message.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import '../widgets/advanced_message_bubble.dart';
import '../widgets/message_context_menu.dart';
import 'voice_call_screen.dart';
import 'video_call_screen.dart';
import 'facebook_profile_screen.dart';
import 'package:flutter/services.dart';

class MessengerChatScreen extends StatefulWidget {
  final Chat chat;

  const MessengerChatScreen({
    super.key,
    required this.chat,
  });

  @override
  State<MessengerChatScreen> createState() => _MessengerChatScreenState();
}

class _MessengerChatScreenState extends State<MessengerChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isRecording = false;
  Message? _replyingToMessage;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMessages();
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadMessages() {
    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.loadChatMessages(widget.chat.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: GestureDetector(
          onTap: () => _viewProfile(),
          child: Row(
            children: [
              SmartAvatarWithText(
                user: widget.chat.otherUser,
                radius: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.chat.type == ChatType.group
                          ? (widget.chat.groupName ?? widget.chat.otherUser.name)
                          : widget.chat.otherUser.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (widget.chat.type != ChatType.group)
                      Text(
                        widget.chat.otherUser.isOnline
                            ? 'متصل الآن'
                            : 'غير متصل',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white70,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          IconButton(
            onPressed: _makeVoiceCall,
            icon: const Icon(Icons.call),
          ),
          IconButton(
            onPressed: _makeVideoCall,
            icon: const Icon(Icons.videocam),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'view_profile',
                child: Row(
                  children: [
                    Icon(Icons.person, color: Colors.black),
                    SizedBox(width: 8),
                    Text('عرض الملف الشخصي'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'media',
                child: Row(
                  children: [
                    Icon(Icons.photo_library, color: Colors.black),
                    SizedBox(width: 8),
                    Text('الوسائط والملفات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'search',
                child: Row(
                  children: [
                    Icon(Icons.search, color: Colors.black),
                    SizedBox(width: 8),
                    Text('البحث في المحادثة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'mute',
                child: Row(
                  children: [
                    Icon(Icons.volume_off, color: Colors.black),
                    SizedBox(width: 8),
                    Text('كتم الإشعارات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'wallpaper',
                child: Row(
                  children: [
                    Icon(Icons.wallpaper, color: Colors.black),
                    SizedBox(width: 8),
                    Text('تغيير الخلفية'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_chat',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('مسح المحادثة', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'block_user',
                child: Row(
                  children: [
                    Icon(Icons.block, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حظر المستخدم', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقة الرسائل
          Expanded(
            child: Consumer<MessengerProvider>(
              builder: (context, messengerProvider, child) {
                if (messengerProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final messages = messengerProvider.currentChatMessages;

                if (messages.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isMe = message.senderId == 'current_user';
                    
                    return AdvancedMessageBubble(
                      message: message,
                      isMe: isMe,
                      onReply: () => _replyToMessage(message),
                      onForward: () => _forwardMessage(message),
                      onShare: () => _shareMessage(message),
                      onPin: () => _togglePinMessage(message),
                      onEdit: isMe ? () => _editMessage(message) : null,
                      onDelete: isMe ? () => _deleteMessage(message) : null,
                      onReact: (emoji) => _reactToMessage(message, emoji),
                      onArchive: () => _archiveMessage(message),
                      onMarkUnread: () => _markMessageUnread(message),
                    );
                  },
                );
              },
            ),
          ),

          // شريط الكتابة
          Container(
            color: Colors.white,
            padding: EdgeInsets.only(
              left: 8,
              right: 8,
              top: 8,
              bottom: MediaQuery.of(context).padding.bottom + 8,
            ),
            child: SafeArea(
              top: false,
              child: Column(
                children: [
                  // عرض الرسالة المرد عليها
                  if (_replyingToMessage != null)
                    _buildReplyPreview(),

                  Row(
                    children: [
                      // زر المرفقات
                      IconButton(
                        onPressed: _showAttachmentOptions,
                        icon: const Icon(Icons.attach_file, color: AppTheme.primaryColor),
                      ),

                      // حقل النص
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: TextField(
                            controller: _messageController,
                            decoration: const InputDecoration(
                              hintText: 'اكتب رسالة...',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            ),
                            maxLines: null,
                            onChanged: (text) {
                              setState(() {}); // لتحديث أيقونة الإرسال
                              // تحديث حالة الكتابة
                              final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
                              messengerProvider.setTyping(text.isNotEmpty);
                            },
                          ),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // زر الإرسال أو التسجيل الصوتي
                      GestureDetector(
                        onTap: _messageController.text.trim().isNotEmpty ? _sendTextMessage : null,
                        onLongPressStart: (_) => _startVoiceRecording(),
                        onLongPressEnd: (_) => _stopVoiceRecording(),
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _messageController.text.trim().isNotEmpty
                                ? Icons.send
                                : (_isRecording ? Icons.stop : Icons.mic),
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReplyPreview() {
    if (_replyingToMessage == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(color: AppTheme.primaryColor, width: 4),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الرد على ${_replyingToMessage!.senderId == 'current_user' ? 'نفسك' : widget.chat.otherUser.name}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _replyingToMessage!.content,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _replyingToMessage = null;
              });
            },
            icon: const Icon(Icons.close, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SmartAvatarWithText(
            user: widget.chat.otherUser,
            radius: 50,
          ),
          const SizedBox(height: 16),
          Text(
            widget.chat.type == ChatType.group
                ? widget.chat.groupName ?? widget.chat.otherUser.name
                : widget.chat.otherUser.name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ محادثة جديدة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _messageController.text = 'مرحباً! 👋';
              _sendTextMessage();
            },
            icon: const Icon(Icons.waving_hand),
            label: const Text('قل مرحباً'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _sendTextMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    final message = Message(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      senderId: 'current_user',
      receiverId: widget.chat.otherUser.id,
      content: text,
      timestamp: DateTime.now(),
      type: MessageType.text,
      status: MessageStatus.sent,
      replyToMessage: _replyingToMessage,
    );

    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.sendMessage(message);

    _messageController.clear();
    setState(() {
      _replyingToMessage = null; // إلغاء الرد بعد الإرسال
    });
    messengerProvider.setTyping(false);

    // التمرير لأسفل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  icon: Icons.photo_camera,
                  label: 'كاميرا',
                  color: Colors.red,
                  onTap: () => _pickImage(ImageSource.camera),
                ),
                _buildAttachmentOption(
                  icon: Icons.photo_library,
                  label: 'معرض الصور',
                  color: Colors.purple,
                  onTap: () => _pickImage(ImageSource.gallery),
                ),
                _buildAttachmentOption(
                  icon: Icons.videocam,
                  label: 'فيديو',
                  color: Colors.blue,
                  onTap: _pickVideo,
                ),
                _buildAttachmentOption(
                  icon: Icons.description,
                  label: 'ملف',
                  color: Colors.orange,
                  onTap: _pickFile,
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  icon: Icons.location_on,
                  label: 'الموقع',
                  color: Colors.green,
                  onTap: _shareLocation,
                ),
                _buildAttachmentOption(
                  icon: Icons.person,
                  label: 'جهة اتصال',
                  color: Colors.teal,
                  onTap: _shareContact,
                ),
                _buildAttachmentOption(
                  icon: Icons.poll,
                  label: 'استطلاع',
                  color: Colors.indigo,
                  onTap: _createPoll,
                ),
                _buildAttachmentOption(
                  icon: Icons.event,
                  label: 'حدث',
                  color: Colors.pink,
                  onTap: _createEvent,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.white, size: 30),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: source);

    if (image != null) {
      _sendMediaMessage(image.path, MessageType.image);
    }
  }

  Future<void> _pickVideo() async {
    final ImagePicker picker = ImagePicker();
    final XFile? video = await picker.pickVideo(source: ImageSource.gallery);

    if (video != null) {
      _sendMediaMessage(video.path, MessageType.video);
    }
  }

  Future<void> _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      final file = result.files.single;
      _sendFileMessage(file.path!, file.name, file.size);
    }
  }

  void _sendMediaMessage(String filePath, MessageType type) {
    final message = Message(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      senderId: 'current_user',
      receiverId: widget.chat.otherUser.id,
      content: type == MessageType.image ? 'صورة' : 'فيديو',
      timestamp: DateTime.now(),
      type: type,
      status: MessageStatus.sent,
      mediaUrl: filePath,
    );

    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.sendMessage(message);
  }

  void _sendFileMessage(String filePath, String fileName, int fileSize) {
    final message = Message(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      senderId: 'current_user',
      receiverId: widget.chat.otherUser.id,
      content: 'ملف: $fileName',
      timestamp: DateTime.now(),
      type: MessageType.file,
      status: MessageStatus.sent,
      mediaUrl: filePath,
      fileName: fileName,
      fileSize: fileSize,
    );

    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.sendMessage(message);
  }

  void _shareLocation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة الموقع قيد التطوير')),
    );
  }

  void _shareContact() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة جهة الاتصال قيد التطوير')),
    );
  }

  void _createPoll() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء استطلاع قيد التطوير')),
    );
  }

  void _createEvent() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حدث قيد التطوير')),
    );
  }

  void _startVoiceRecording() {
    setState(() {
      _isRecording = true;
    });

    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.setRecording(true);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بدء تسجيل الرسالة الصوتية...')),
    );
  }

  void _stopVoiceRecording() {
    setState(() {
      _isRecording = false;
    });

    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.setRecording(false);

    // محاكاة إرسال رسالة صوتية
    final message = Message(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      senderId: 'current_user',
      receiverId: widget.chat.otherUser.id,
      content: 'رسالة صوتية',
      timestamp: DateTime.now(),
      type: MessageType.audio,
      status: MessageStatus.sent,
      audioDuration: const Duration(seconds: 5),
    );

    messengerProvider.sendMessage(message);
  }

  void _makeVoiceCall() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VoiceCallScreen(
          user: widget.chat.otherUser,
          isIncoming: false,
        ),
      ),
    );
  }

  void _makeVideoCall() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoCallScreen(
          otherUser: widget.chat.otherUser,
          isIncoming: false,
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'view_profile':
        _viewProfile();
        break;
      case 'media':
        _showMediaAndFiles();
        break;
      case 'search':
        _searchInChat();
        break;
      case 'mute':
        _toggleMuteNotifications();
        break;
      case 'wallpaper':
        _changeWallpaper();
        break;
      case 'clear_chat':
        _showClearChatDialog();
        break;
      case 'block_user':
        _blockUser();
        break;
    }
  }

  void _viewProfile() {
    // الانتقال إلى صفحة الملف الشخصي في الشبكة الاجتماعية (مثل فيسبوك)
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FacebookProfileScreen(
          userId: widget.chat.otherUser.id,
          userName: widget.chat.otherUser.name,
        ),
      ),
    );
  }



  void _showMediaAndFiles() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقبض السحب
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 20),

            const Text(
              'الوسائط والملفات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // تبويبات
            DefaultTabController(
              length: 3,
              child: Expanded(
                child: Column(
                  children: [
                    const TabBar(
                      tabs: [
                        Tab(text: 'الصور'),
                        Tab(text: 'الفيديوهات'),
                        Tab(text: 'الملفات'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          _buildMediaGrid('images'),
                          _buildMediaGrid('videos'),
                          _buildFilesList(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaGrid(String type) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: 6, // محاكاة
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            type == 'images' ? Icons.image : Icons.videocam,
            size: 40,
            color: Colors.grey[600],
          ),
        );
      },
    );
  }

  Widget _buildFilesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 5, // محاكاة
      itemBuilder: (context, index) {
        return ListTile(
          leading: const Icon(Icons.description),
          title: Text('ملف ${index + 1}.pdf'),
          subtitle: const Text('2.5 MB'),
          trailing: IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {},
          ),
        );
      },
    );
  }

  void _searchInChat() {
    showSearch(
      context: context,
      delegate: ChatSearchDelegate(),
    );
  }

  void _toggleMuteNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('كتم الإشعارات'),
        content: const Text('لكم من الوقت تريد كتم الإشعارات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _muteFor('1 hour');
            },
            child: const Text('ساعة واحدة'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _muteFor('8 hours');
            },
            child: const Text('8 ساعات'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _muteFor('1 week');
            },
            child: const Text('أسبوع'),
          ),
        ],
      ),
    );
  }

  void _changeWallpaper() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'تغيير خلفية المحادثة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 3,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: [
                _buildWallpaperOption('افتراضي', Colors.grey[100]!),
                _buildWallpaperOption('أزرق', Colors.blue[100]!),
                _buildWallpaperOption('أخضر', Colors.green[100]!),
                _buildWallpaperOption('وردي', Colors.pink[100]!),
                _buildWallpaperOption('بنفسجي', Colors.purple[100]!),
                _buildWallpaperOption('مخصص', Colors.orange[100]!),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWallpaperOption(String name, Color color) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تغيير الخلفية إلى $name')),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Center(
          child: Text(
            name,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }



  void _muteFor(String duration) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم كتم الإشعارات لمدة $duration')),
    );
  }

  void _showClearChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح المحادثة'),
        content: Text('هل تريد مسح جميع الرسائل مع ${widget.chat.otherUser.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح المحادثة')),
              );
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // دوال التفاعل مع الرسائل
  void _replyToMessage(Message message) {
    setState(() {
      _replyingToMessage = message;
    });
    _messageController.text = '';
  }

  void _forwardMessage(Message message) async {
    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    final result = await showDialog<List<Chat>>(
      context: context,
      builder: (context) => ForwardMessageDialog(
        message: message,
        contacts: messengerProvider.chats, // تمرير جميع المحادثات
      ),
    );

    if (result != null && result.isNotEmpty && mounted) {
      // إرسال الرسالة لكل محادثة مختارة
      for (final chat in result) {
        final forwardedMessage = Message(
          id: 'msg_${DateTime.now().millisecondsSinceEpoch}_${chat.id}',
          senderId: 'current_user',
          receiverId: chat.otherUser.id,
          content: message.content,
          timestamp: DateTime.now(),
          type: message.type,
          status: MessageStatus.sent,
          mediaUrl: message.mediaUrl,
          fileName: message.fileName,
          fileSize: message.fileSize,
        );

        messengerProvider.sendMessage(forwardedMessage);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم إعادة توجيه الرسالة إلى ${result.length} محادثات')),
      );
    }
  }

  void _shareMessage(Message message) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'مشاركة الرسالة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            // خيارات المشاركة الداخلية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShareOption(
                  icon: Icons.copy,
                  label: 'نسخ',
                  onTap: () {
                    Navigator.pop(context);
                    Clipboard.setData(ClipboardData(text: message.content));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم نسخ النص')),
                    );
                  },
                ),
                _buildShareOption(
                  icon: Icons.forward,
                  label: 'إعادة توجيه',
                  onTap: () {
                    Navigator.pop(context);
                    _forwardMessage(message);
                  },
                ),
                _buildShareOption(
                  icon: Icons.group,
                  label: 'مشاركة في مجموعة',
                  onTap: () {
                    Navigator.pop(context);
                    _shareToGroup(message);
                  },
                ),
                _buildShareOption(
                  icon: Icons.post_add,
                  label: 'مشاركة كمنشور',
                  onTap: () {
                    Navigator.pop(context);
                    _shareAsPost(message);
                  },
                ),
              ],
            ),

            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 10),

            // خيارات المشاركة الخارجية
            const Text(
              'مشاركة خارجية',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 15),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShareOption(
                  icon: Icons.facebook,
                  label: 'فيسبوك',
                  color: const Color(0xFF1877F2),
                  onTap: () {
                    Navigator.pop(context);
                    _shareToFacebook(message);
                  },
                ),
                _buildShareOption(
                  icon: Icons.chat,
                  label: 'واتساب',
                  color: const Color(0xFF25D366),
                  onTap: () {
                    Navigator.pop(context);
                    _shareToWhatsApp(message);
                  },
                ),
                _buildShareOption(
                  icon: Icons.alternate_email,
                  label: 'تويتر',
                  color: const Color(0xFF1DA1F2),
                  onTap: () {
                    Navigator.pop(context);
                    _shareToTwitter(message);
                  },
                ),
                _buildShareOption(
                  icon: Icons.link,
                  label: 'نسخ الرابط',
                  onTap: () {
                    Navigator.pop(context);
                    _copyMessageLink(message);
                  },
                ),
              ],
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color ?? Colors.grey[200],
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color != null ? Colors.white : Colors.black87,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _togglePinMessage(Message message) {
    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);

    // تحديث حالة التثبيت
    final updatedMessage = message.copyWith(isPinned: !message.isPinned);
    messengerProvider.updateMessage(updatedMessage);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(updatedMessage.isPinned ? 'تم تثبيت الرسالة' : 'تم إلغاء تثبيت الرسالة'),
      ),
    );
  }

  void _editMessage(Message message) {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController(text: message.content);
        return AlertDialog(
          title: const Text('تعديل الرسالة'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: 'اكتب الرسالة المعدلة...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // تنفيذ تعديل الرسالة
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم تعديل الرسالة')),
                );
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  void _deleteMessage(Message message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الرسالة'),
        content: const Text('هل تريد حذف هذه الرسالة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // تنفيذ حذف الرسالة
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف الرسالة')),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _reactToMessage(Message message, String emoji) {
    // تنفيذ إضافة التفاعل
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم إضافة تفاعل $emoji')),
    );
  }

  // دوال المشاركة
  void _shareToGroup(Message message) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة في مجموعة - قريباً')),
    );
  }

  void _shareAsPost(Message message) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة كمنشور - قريباً')),
    );
  }

  void _shareToFacebook(Message message) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة على فيسبوك - قريباً')),
    );
  }

  void _shareToWhatsApp(Message message) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة على واتساب - قريباً')),
    );
  }

  void _shareToTwitter(Message message) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة على تويتر - قريباً')),
    );
  }

  void _copyMessageLink(Message message) {
    final link = 'https://arzawo.com/message/${message.id}';
    Clipboard.setData(ClipboardData(text: link));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم نسخ رابط الرسالة')),
    );
  }

  // دوال السحب والخيارات الجديدة
  void _archiveMessage(Message message) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم أرشفة الرسالة')),
    );
  }

  void _markMessageUnread(Message message) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديد الرسالة كغير مقروءة')),
    );
  }



  // دوال الحظر
  void _blockUser() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حظر المستخدم'),
        content: Text('هل تريد حظر ${widget.chat.otherUser.name}؟ لن تتمكن من تلقي رسائل منه.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performBlock();
            },
            child: const Text('حظر', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _performBlock() {
    // تنفيذ الحظر
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم حظر ${widget.chat.otherUser.name}')),
    );

    // العودة للشاشة السابقة
    Navigator.pop(context);
  }
}

// فئة البحث في المحادثة
class ChatSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return Center(
      child: Text('البحث عن: $query'),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return ListView(
      children: [
        ListTile(
          title: const Text('مرحبا'),
          onTap: () {
            query = 'مرحبا';
            showResults(context);
          },
        ),
        ListTile(
          title: const Text('كيف حالك'),
          onTap: () {
            query = 'كيف حالك';
            showResults(context);
          },
        ),
      ],
    );
  }
}
