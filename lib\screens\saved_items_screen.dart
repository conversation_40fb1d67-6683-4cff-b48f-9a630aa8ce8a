import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/saved_item.dart';
import '../providers/saved_items_provider.dart';

class SavedItemsScreen extends StatefulWidget {
  const SavedItemsScreen({super.key});

  @override
  State<SavedItemsScreen> createState() => _SavedItemsScreenState();
}

class _SavedItemsScreenState extends State<SavedItemsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    
    // تحميل العناصر المحفوظة عند بدء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<SavedItemsProvider>(context, listen: false).loadSavedItems();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'العناصر المحفوظة',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onPressed: _showOptionsMenu,
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(100),
          child: Column(
            children: [
              // شريط البحث
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في العناصر المحفوظة...',
                    prefixIcon: const Icon(Icons.search, color: Colors.grey),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, color: Colors.grey),
                            onPressed: () {
                              _searchController.clear();
                              _clearSearch();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                  onChanged: _performSearch,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // تبويبات الفلترة
              TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: Colors.blue,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Colors.blue,
                onTap: _onTabChanged,
                tabs: const [
                  Tab(text: 'الكل'),
                  Tab(text: 'المنشورات'),
                  Tab(text: 'الصور'),
                  Tab(text: 'الفيديوهات'),
                  Tab(text: 'القصص'),
                  Tab(text: 'مجموعات'),
                ],
              ),
            ],
          ),
        ),
      ),
      body: Consumer<SavedItemsProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    provider.errorMessage!,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.loadSavedItems(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (provider.savedItems.isEmpty) {
            return _buildEmptyState();
          }

          return RefreshIndicator(
            onRefresh: () => provider.loadSavedItems(),
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: provider.savedItems.length,
              itemBuilder: (context, index) {
                final savedItem = provider.savedItems[index];
                return _SavedItemCard(
                  savedItem: savedItem,
                  onTap: () => _openSavedItem(savedItem),
                  onRemove: () => _removeSavedItem(savedItem),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    final provider = Provider.of<SavedItemsProvider>(context, listen: false);
    final hasFilter = provider.selectedFilter != null;
    final hasSearch = provider.searchQuery.isNotEmpty;
    
    String title;
    String subtitle;
    IconData icon;
    
    if (hasSearch) {
      title = 'لا توجد نتائج';
      subtitle = 'جرب البحث بكلمات مختلفة';
      icon = Icons.search_off;
    } else if (hasFilter) {
      title = 'لا توجد عناصر من هذا النوع';
      subtitle = 'احفظ عناصر من هذا النوع لتظهر هنا';
      icon = Icons.filter_list_off;
    } else {
      title = 'لا توجد عناصر محفوظة';
      subtitle = 'احفظ المنشورات والصور والفيديوهات لتظهر هنا';
      icon = Icons.bookmark_border;
    }
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              subtitle,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          if (hasFilter || hasSearch) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _clearFilters,
              child: const Text('مسح الفلاتر'),
            ),
          ],
        ],
      ),
    );
  }

  void _onTabChanged(int index) {
    final provider = Provider.of<SavedItemsProvider>(context, listen: false);
    
    SavedItemType? filter;
    switch (index) {
      case 0:
        filter = null; // الكل
        break;
      case 1:
        filter = SavedItemType.post;
        break;
      case 2:
        filter = SavedItemType.image;
        break;
      case 3:
        filter = SavedItemType.video;
        break;
      case 4:
        filter = SavedItemType.story;
        break;
      case 5:
        filter = SavedItemType.groupPost;
        break;
    }
    
    provider.setFilter(filter);
  }

  void _performSearch(String query) {
    final provider = Provider.of<SavedItemsProvider>(context, listen: false);
    provider.setSearchQuery(query);
  }

  void _clearSearch() {
    final provider = Provider.of<SavedItemsProvider>(context, listen: false);
    provider.setSearchQuery('');
  }

  void _clearFilters() {
    final provider = Provider.of<SavedItemsProvider>(context, listen: false);
    provider.setFilter(null);
    provider.setSearchQuery('');
    _searchController.clear();
    _tabController.animateTo(0);
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete_sweep),
              title: const Text('مسح جميع العناصر المحفوظة'),
              onTap: () {
                Navigator.pop(context);
                _showClearAllDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('معلومات العناصر المحفوظة'),
              onTap: () {
                Navigator.pop(context);
                _showStatsDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _openSavedItem(SavedItem savedItem) {
    // فتح العنصر المحفوظ
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح ${savedItem.typeDisplayName}: ${savedItem.title}'),
      ),
    );
  }

  void _removeSavedItem(SavedItem savedItem) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة العنصر المحفوظ'),
        content: Text('هل تريد إزالة "${savedItem.title}" من العناصر المحفوظة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<SavedItemsProvider>(context, listen: false)
                  .removeSavedItem(savedItem.id);
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إزالة العنصر من المحفوظات'),
                ),
              );
            },
            child: const Text('إزالة'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع العناصر المحفوظة'),
        content: const Text('هل تريد مسح جميع العناصر المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<SavedItemsProvider>(context, listen: false)
                  .clearAllSavedItems();
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح جميع العناصر المحفوظة'),
                ),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showStatsDialog() async {
    final provider = Provider.of<SavedItemsProvider>(context, listen: false);
    final stats = await provider.getSavedItemsStats();
    
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إحصائيات العناصر المحفوظة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('إجمالي العناصر: ${provider.totalSavedItemsCount}'),
              const SizedBox(height: 8),
              ...stats.entries.map((entry) {
                final type = entry.key;
                final count = entry.value;
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text('${_getTypeDisplayName(type)}: $count'),
                );
              }),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    }
  }

  String _getTypeDisplayName(SavedItemType type) {
    switch (type) {
      case SavedItemType.post:
        return 'المنشورات';
      case SavedItemType.image:
        return 'الصور';
      case SavedItemType.video:
        return 'الفيديوهات';
      case SavedItemType.story:
        return 'القصص';
      case SavedItemType.groupPost:
        return 'منشورات المجموعات';
    }
  }
}

class _SavedItemCard extends StatelessWidget {
  final SavedItem savedItem;
  final VoidCallback onTap;
  final VoidCallback onRemove;

  const _SavedItemCard({
    required this.savedItem,
    required this.onTap,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة النوع أو صورة مصغرة
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _getTypeColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: savedItem.thumbnailUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          savedItem.thumbnailUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              _getTypeIcon(),
                              color: _getTypeColor(),
                              size: 30,
                            );
                          },
                        ),
                      )
                    : Icon(
                        _getTypeIcon(),
                        color: _getTypeColor(),
                        size: 30,
                      ),
              ),

              const SizedBox(width: 16),

              // معلومات العنصر
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      savedItem.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (savedItem.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        savedItem.description!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getTypeColor().withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            savedItem.typeDisplayName,
                            style: TextStyle(
                              color: _getTypeColor(),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          savedItem.formattedSavedDate,
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // زر الإزالة
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    builder: (context) => Container(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ListTile(
                            leading: const Icon(Icons.open_in_new),
                            title: const Text('فتح'),
                            onTap: () {
                              Navigator.pop(context);
                              onTap();
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.delete, color: Colors.red),
                            title: const Text('إزالة من المحفوظات'),
                            onTap: () {
                              Navigator.pop(context);
                              onRemove();
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (savedItem.type) {
      case SavedItemType.post:
        return Icons.article;
      case SavedItemType.image:
        return Icons.image;
      case SavedItemType.video:
        return Icons.play_circle;
      case SavedItemType.story:
        return Icons.auto_stories;
      case SavedItemType.groupPost:
        return Icons.group;
    }
  }

  Color _getTypeColor() {
    switch (savedItem.type) {
      case SavedItemType.post:
        return Colors.blue;
      case SavedItemType.image:
        return Colors.green;
      case SavedItemType.video:
        return Colors.red;
      case SavedItemType.story:
        return Colors.purple;
      case SavedItemType.groupPost:
        return Colors.orange;
    }
  }
}
