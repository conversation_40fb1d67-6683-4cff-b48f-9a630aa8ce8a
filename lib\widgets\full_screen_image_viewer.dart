import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';
import '../theme/app_theme.dart';

class FullScreenImageViewer extends StatefulWidget {
  final String imageUrl;
  final String? heroTag;

  const FullScreenImageViewer({
    super.key,
    required this.imageUrl,
    this.heroTag,
  });

  @override
  State<FullScreenImageViewer> createState() => _FullScreenImageViewerState();
}

class _FullScreenImageViewerState extends State<FullScreenImageViewer>
    with SingleTickerProviderStateMixin {
  late TransformationController _transformationController;
  late AnimationController _animationController;
  bool _showAppBar = true;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // إخفاء شريط الحالة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();
    // إظهار شريط الحالة مرة أخرى
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: _showAppBar
          ? AppBar(
              backgroundColor: Colors.black.withValues(alpha: 0.5),
              elevation: 0,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: _shareImage,
                ),
                IconButton(
                  icon: const Icon(Icons.download, color: Colors.white),
                  onPressed: _downloadImage,
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onSelected: _handleMenuAction,
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'save_to_gallery',
                      child: Row(
                        children: [
                          Icon(Icons.save_alt),
                          SizedBox(width: 8),
                          Text('حفظ في المعرض'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'copy_link',
                      child: Row(
                        children: [
                          Icon(Icons.link),
                          SizedBox(width: 8),
                          Text('نسخ الرابط'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'set_wallpaper',
                      child: Row(
                        children: [
                          Icon(Icons.wallpaper),
                          SizedBox(width: 8),
                          Text('تعيين كخلفية'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            )
          : null,
      body: GestureDetector(
        onTap: _toggleAppBar,
        child: Center(
          child: Hero(
            tag: widget.heroTag ?? 'image_hero',
            child: InteractiveViewer(
              transformationController: _transformationController,
              minScale: 0.5,
              maxScale: 4.0,
              child: _buildImage(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImage() {
    if (widget.imageUrl.startsWith('http')) {
      // صورة من الإنترنت
      return Image.network(
        widget.imageUrl,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(height: 16),
                const Text(
                  'جاري تحميل الصورة...',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  size: 100,
                  color: Colors.white54,
                ),
                SizedBox(height: 16),
                Text(
                  'فشل في تحميل الصورة',
                  style: TextStyle(
                    color: Colors.white54,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          );
        },
      );
    } else {
      // صورة محلية
      return Image.file(
        File(widget.imageUrl),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  size: 100,
                  color: Colors.white54,
                ),
                SizedBox(height: 16),
                Text(
                  'فشل في تحميل الصورة',
                  style: TextStyle(
                    color: Colors.white54,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  void _toggleAppBar() {
    setState(() {
      _showAppBar = !_showAppBar;
    });
  }

  void _shareImage() async {
    try {
      if (widget.imageUrl.startsWith('http')) {
        await Share.share(widget.imageUrl, subject: 'مشاركة صورة');
      } else {
        await Share.shareXFiles([XFile(widget.imageUrl)], text: 'مشاركة صورة');
      }
    } catch (e) {
      _showSnackBar('خطأ في مشاركة الصورة');
    }
  }

  void _downloadImage() async {
    try {
      // محاكاة تحميل الصورة
      _showSnackBar('جاري تحميل الصورة...');
      await Future.delayed(const Duration(seconds: 2));
      _showSnackBar('تم تحميل الصورة بنجاح');
    } catch (e) {
      _showSnackBar('خطأ في تحميل الصورة');
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'save_to_gallery':
        _saveToGallery();
        break;
      case 'copy_link':
        _copyLink();
        break;
      case 'set_wallpaper':
        _setWallpaper();
        break;
    }
  }

  void _saveToGallery() async {
    try {
      _showSnackBar('جاري حفظ الصورة في المعرض...');
      await Future.delayed(const Duration(seconds: 2));
      _showSnackBar('تم حفظ الصورة في المعرض');
    } catch (e) {
      _showSnackBar('خطأ في حفظ الصورة');
    }
  }

  void _copyLink() async {
    try {
      await Clipboard.setData(ClipboardData(text: widget.imageUrl));
      _showSnackBar('تم نسخ رابط الصورة');
    } catch (e) {
      _showSnackBar('خطأ في نسخ الرابط');
    }
  }

  void _setWallpaper() {
    _showSnackBar('ميزة تعيين الخلفية قيد التطوير');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.black87,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
