import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../providers/social_provider.dart';
import '../models/user.dart';
import 'user_profile_screen.dart';

class PeopleScreen extends StatefulWidget {
  const PeopleScreen({super.key});

  @override
  State<PeopleScreen> createState() => _PeopleScreenState();
}

class _PeopleScreenState extends State<PeopleScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        automaticallyImplyLeading: false,
        title: const Text(
          'الأشخاص',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن أشخاص...',
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
            ),
          ),
          
          // قائمة الأشخاص
          Expanded(
            child: Consumer<SocialProvider>(
              builder: (context, socialProvider, child) {
                // الحصول على قائمة المستخدمين (بيانات تجريبية)
                final allUsers = _getDemoUsers();
                
                // تصفية المستخدمين حسب البحث
                final filteredUsers = allUsers.where((user) {
                  return _searchQuery.isEmpty ||
                      user.name.toLowerCase().contains(_searchQuery) ||
                      user.email.toLowerCase().contains(_searchQuery);
                }).toList();

                if (filteredUsers.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 80,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد نتائج',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: filteredUsers.length,
                  itemBuilder: (context, index) {
                    final user = filteredUsers[index];
                    return _buildUserCard(user);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(User user) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openUserProfile(user),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة المستخدم مع أفاتار حقيقي
              GestureDetector(
                onTap: () => _openUserProfile(user),
                child: CircleAvatar(
                  radius: 30,
                  backgroundColor: AppTheme.primaryColor,
                  backgroundImage: user.avatar != null
                      ? NetworkImage(user.avatar!)
                      : null,
                  child: user.avatar == null
                      ? Text(
                          user.name.isNotEmpty ? user.name[0].toUpperCase() : '؟',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
              ),
            
            const SizedBox(width: 16),
            
            // معلومات المستخدم
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (user.bio != null && user.bio!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      user.bio!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            
            // زر المتابعة
            ElevatedButton(
              onPressed: () => _toggleFollow(user),
              style: ElevatedButton.styleFrom(
                backgroundColor: _isFollowing(user) ? Colors.grey[300] : AppTheme.primaryColor,
                foregroundColor: _isFollowing(user) ? Colors.black : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              ),
              child: Text(
                _isFollowing(user) ? 'إلغاء المتابعة' : 'متابعة',
                style: const TextStyle(fontSize: 12),
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }

  bool _isFollowing(User user) {
    // منطق فحص المتابعة (يمكن تطويره لاحقاً)
    return false;
  }

  void _openUserProfile(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(user: user),
      ),
    );
  }

  void _toggleFollow(User user) {
    // منطق المتابعة/إلغاء المتابعة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isFollowing(user)
              ? 'تم إلغاء متابعة ${user.name}'
              : 'تم متابعة ${user.name}',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  List<User> _getDemoUsers() {
    return [
      User(
        id: 'user1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        bio: 'مطور تطبيقات موبايل | محب للتكنولوجيا',
        work: 'مطور في شركة تقنية',
        city: 'الرياض',
        joinDate: DateTime.now().subtract(const Duration(days: 30)),
        followersCount: 245,
        followingCount: 180,
        postsCount: 42,
        isVerified: true,
      ),
      User(
        id: 'user2',
        name: 'فاطمة علي',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        bio: 'مصممة جرافيك | فنانة رقمية',
        work: 'مصممة جرافيك',
        city: 'جدة',
        joinDate: DateTime.now().subtract(const Duration(days: 45)),
        followersCount: 189,
        followingCount: 156,
        postsCount: 38,
      ),
      User(
        id: 'user3',
        name: 'محمد أحمد',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        bio: 'مهندس برمجيات | خبير في الذكاء الاصطناعي',
        work: 'مهندس برمجيات',
        education: 'بكالوريوس علوم الحاسوب',
        city: 'الدمام',
        joinDate: DateTime.now().subtract(const Duration(days: 60)),
        followersCount: 312,
        followingCount: 98,
        postsCount: 67,
        isVerified: true,
      ),
      User(
        id: 'user4',
        name: 'نور الهدى',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        bio: 'كاتبة ومدونة | محبة للقراءة والكتابة',
        work: 'كاتبة ومدونة',
        education: 'ماجستير في الأدب العربي',
        city: 'مكة المكرمة',
        joinDate: DateTime.now().subtract(const Duration(days: 20)),
        followersCount: 156,
        followingCount: 203,
        postsCount: 29,
      ),
      User(
        id: 'user5',
        name: 'عبدالله سالم',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        bio: 'مصور فوتوغرافي | مسافر حول العالم',
        work: 'مصور فوتوغرافي',
        city: 'أبها',
        joinDate: DateTime.now().subtract(const Duration(days: 90)),
        followersCount: 423,
        followingCount: 267,
        postsCount: 89,
      ),
      User(
        id: 'user6',
        name: 'مريم خالد',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        bio: 'طبيبة | متخصصة في طب الأطفال',
        work: 'طبيبة أطفال',
        education: 'دكتوراه في الطب',
        city: 'المدينة المنورة',
        joinDate: DateTime.now().subtract(const Duration(days: 15)),
        followersCount: 78,
        followingCount: 134,
        postsCount: 15,
        isVerified: true,
      ),
      User(
        id: 'user7',
        name: 'يوسف إبراهيم',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        bio: 'معلم رياضيات | مدرب كرة قدم',
        work: 'معلم رياضيات',
        education: 'بكالوريوس رياضيات',
        city: 'الطائف',
        joinDate: DateTime.now().subtract(const Duration(days: 75)),
        followersCount: 167,
        followingCount: 89,
        postsCount: 34,
      ),
      User(
        id: 'user8',
        name: 'سارة أحمد',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
        bio: 'طالبة جامعية | دراسة الهندسة المعمارية',
        education: 'طالبة هندسة معمارية',
        city: 'الخبر',
        joinDate: DateTime.now().subtract(const Duration(days: 10)),
        followersCount: 45,
        followingCount: 67,
        postsCount: 8,
      ),
    ];
  }
}
