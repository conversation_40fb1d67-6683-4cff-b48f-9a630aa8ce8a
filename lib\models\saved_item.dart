enum SavedItemType {
  post,
  image,
  video,
  story,
  groupPost,
}

class SavedItem {
  final String id;
  final SavedItemType type;
  final String itemId;
  final String title;
  final String? description;
  final String? thumbnailUrl;
  final String? authorName;
  final String? authorId;
  final DateTime savedAt;
  final Map<String, dynamic> metadata;

  const SavedItem({
    required this.id,
    required this.type,
    required this.itemId,
    required this.title,
    this.description,
    this.thumbnailUrl,
    this.authorName,
    this.authorId,
    required this.savedAt,
    this.metadata = const {},
  });

  SavedItem copyWith({
    String? id,
    SavedItemType? type,
    String? itemId,
    String? title,
    String? description,
    String? thumbnailUrl,
    String? authorName,
    String? authorId,
    DateTime? savedAt,
    Map<String, dynamic>? metadata,
  }) {
    return SavedItem(
      id: id ?? this.id,
      type: type ?? this.type,
      itemId: itemId ?? this.itemId,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      authorName: authorName ?? this.authorName,
      authorId: authorId ?? this.authorId,
      savedAt: savedAt ?? this.savedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'itemId': itemId,
      'title': title,
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'authorName': authorName,
      'authorId': authorId,
      'savedAt': savedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory SavedItem.fromJson(Map<String, dynamic> json) {
    return SavedItem(
      id: json['id'],
      type: SavedItemType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SavedItemType.post,
      ),
      itemId: json['itemId'],
      title: json['title'],
      description: json['description'],
      thumbnailUrl: json['thumbnailUrl'],
      authorName: json['authorName'],
      authorId: json['authorId'],
      savedAt: DateTime.parse(json['savedAt']),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  String get typeDisplayName {
    switch (type) {
      case SavedItemType.post:
        return 'منشور';
      case SavedItemType.image:
        return 'صورة';
      case SavedItemType.video:
        return 'فيديو';
      case SavedItemType.story:
        return 'قصة';
      case SavedItemType.groupPost:
        return 'منشور مجموعة';
    }
  }

  String get formattedSavedDate {
    final now = DateTime.now();
    final difference = now.difference(savedAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
