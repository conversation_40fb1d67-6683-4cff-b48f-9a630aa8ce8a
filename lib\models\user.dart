class User {
  final String id;
  final String name;
  final String email;
  final String? avatar;
  final String? coverImageUrl;
  final String? bio;
  final bool isOnline;
  final DateTime? lastSeen;
  final DateTime joinDate;
  final bool isVerified;
  final int followersCount;
  final int followingCount;
  final int postsCount;

  // معلومات شخصية إضافية
  final String? phone;
  final String? city;
  final String? country;
  final DateTime? birthDate;
  final String? gender;
  final String? relationship;
  final String? work;
  final String? education;
  final String? website;
  final List<String> interests;
  final List<String> languages;

  // إعدادات الخصوصية
  final String postsVisibility; // 'everyone', 'friends', 'only_me'
  final String photosVisibility;
  final String videosVisibility;
  final String infoVisibility;
  final bool showEmail;
  final bool showPhone;
  final bool showBirthDate;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.avatar,
    this.coverImageUrl,
    this.bio,
    this.isOnline = false,
    this.lastSeen,
    required this.joinDate,
    this.isVerified = false,
    this.followersCount = 0,
    this.followingCount = 0,
    this.postsCount = 0,
    this.phone,
    this.city,
    this.country,
    this.birthDate,
    this.gender,
    this.relationship,
    this.work,
    this.education,
    this.website,
    this.interests = const [],
    this.languages = const [],
    this.postsVisibility = 'everyone',
    this.photosVisibility = 'everyone',
    this.videosVisibility = 'everyone',
    this.infoVisibility = 'everyone',
    this.showEmail = true,
    this.showPhone = true,
    this.showBirthDate = true,
  });

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? avatar,
    String? coverImageUrl,
    String? bio,
    bool? isOnline,
    DateTime? lastSeen,
    DateTime? joinDate,
    bool? isVerified,
    int? followersCount,
    int? followingCount,
    int? postsCount,
    String? phone,
    String? city,
    String? country,
    DateTime? birthDate,
    String? gender,
    String? relationship,
    String? work,
    String? education,
    String? website,
    List<String>? interests,
    List<String>? languages,
    String? postsVisibility,
    String? photosVisibility,
    String? videosVisibility,
    String? infoVisibility,
    bool? showEmail,
    bool? showPhone,
    bool? showBirthDate,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      bio: bio ?? this.bio,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      joinDate: joinDate ?? this.joinDate,
      isVerified: isVerified ?? this.isVerified,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      postsCount: postsCount ?? this.postsCount,
      phone: phone ?? this.phone,
      city: city ?? this.city,
      country: country ?? this.country,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      relationship: relationship ?? this.relationship,
      work: work ?? this.work,
      education: education ?? this.education,
      website: website ?? this.website,
      interests: interests ?? this.interests,
      languages: languages ?? this.languages,
      postsVisibility: postsVisibility ?? this.postsVisibility,
      photosVisibility: photosVisibility ?? this.photosVisibility,
      videosVisibility: videosVisibility ?? this.videosVisibility,
      infoVisibility: infoVisibility ?? this.infoVisibility,
      showEmail: showEmail ?? this.showEmail,
      showPhone: showPhone ?? this.showPhone,
      showBirthDate: showBirthDate ?? this.showBirthDate,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'coverImageUrl': coverImageUrl,
      'bio': bio,
      'isOnline': isOnline,
      'lastSeen': lastSeen?.toIso8601String(),
      'joinDate': joinDate.toIso8601String(),
      'isVerified': isVerified,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'postsCount': postsCount,
      'phone': phone,
      'city': city,
      'country': country,
      'birthDate': birthDate?.toIso8601String(),
      'gender': gender,
      'relationship': relationship,
      'work': work,
      'education': education,
      'website': website,
      'interests': interests,
      'languages': languages,
      'postsVisibility': postsVisibility,
      'photosVisibility': photosVisibility,
      'videosVisibility': videosVisibility,
      'infoVisibility': infoVisibility,
      'showEmail': showEmail,
      'showPhone': showPhone,
      'showBirthDate': showBirthDate,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      avatar: json['avatar'],
      coverImageUrl: json['coverImageUrl'],
      bio: json['bio'],
      isOnline: json['isOnline'] ?? false,
      lastSeen: json['lastSeen'] != null
          ? DateTime.parse(json['lastSeen'])
          : null,
      joinDate: DateTime.parse(json['joinDate']),
      isVerified: json['isVerified'] ?? false,
      followersCount: json['followersCount'] ?? 0,
      followingCount: json['followingCount'] ?? 0,
      postsCount: json['postsCount'] ?? 0,
      phone: json['phone'],
      city: json['city'],
      country: json['country'],
      birthDate: json['birthDate'] != null ? DateTime.parse(json['birthDate']) : null,
      gender: json['gender'],
      relationship: json['relationship'],
      work: json['work'],
      education: json['education'],
      website: json['website'],
      interests: List<String>.from(json['interests'] ?? []),
      languages: List<String>.from(json['languages'] ?? []),
      postsVisibility: json['postsVisibility'] ?? 'everyone',
      photosVisibility: json['photosVisibility'] ?? 'everyone',
      videosVisibility: json['videosVisibility'] ?? 'everyone',
      infoVisibility: json['infoVisibility'] ?? 'everyone',
      showEmail: json['showEmail'] ?? true,
      showPhone: json['showPhone'] ?? true,
      showBirthDate: json['showBirthDate'] ?? true,
    );
  }

  // حساب العمر من تاريخ الميلاد
  int? get age {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate!.year;
    if (now.month < birthDate!.month ||
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      age--;
    }
    return age;
  }
}
