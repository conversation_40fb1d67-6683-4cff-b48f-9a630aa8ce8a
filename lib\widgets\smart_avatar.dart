import 'package:flutter/material.dart';
import '../models/user.dart';
import '../theme/app_theme.dart';
import '../services/avatar_service.dart';

class SmartAvatar extends StatelessWidget {
  final User? user;
  final String? imageUrl;
  final String? name;
  final String? gender;
  final double radius;
  final Color? backgroundColor;
  final Color? textColor;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const SmartAvatar({
    super.key,
    this.user,
    this.imageUrl,
    this.name,
    this.gender,
    this.radius = 30,
    this.backgroundColor,
    this.textColor,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2,
  });

  @override
  Widget build(BuildContext context) {
    final String? finalImageUrl = imageUrl ?? user?.avatar;
    final String finalName = name ?? user?.name ?? 'مستخدم';
    final String? finalGender = gender ?? user?.gender;

    Widget avatarChild;

    if (finalImageUrl != null && finalImageUrl.isNotEmpty) {
      // إذا كان هناك صورة حقيقية
      avatarChild = ClipOval(
        child: Image.network(
          finalImageUrl,
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildDefaultAvatar(finalName, finalGender);
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return _buildLoadingAvatar();
          },
        ),
      );
    } else {
      // استخدام الأفاتار الافتراضي
      avatarChild = _buildDefaultAvatar(finalName, finalGender);
    }

    if (showBorder) {
      return Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: borderColor ?? Colors.white,
            width: borderWidth,
          ),
        ),
        child: avatarChild,
      );
    }

    return avatarChild;
  }

  Widget _buildDefaultAvatar(String name, String? gender) {
    // استخدام AvatarService للحصول على الصورة الافتراضية المناسبة
    final avatarService = AvatarService();

    return avatarService.buildAvatarWithFallback(
      name: name,
      imageUrl: null, // لا توجد صورة
      radius: radius,
      backgroundColor: backgroundColor,
      showBorder: showBorder,
      borderColor: borderColor ?? Colors.white,
      borderWidth: borderWidth,
    );
  }

  Widget _buildLoadingAvatar() {
    return CircleAvatar(
      radius: radius,
      backgroundColor: Colors.grey[200],
      child: SizedBox(
        width: radius,
        height: radius,
        child: const CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
      ),
    );
  }


}

enum AvatarType {
  male,
  female,
  neutral,
}

// Widget مساعد للأفاتار مع النص
class SmartAvatarWithText extends StatelessWidget {
  final User? user;
  final String? imageUrl;
  final String? name;
  final String? gender;
  final double radius;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const SmartAvatarWithText({
    super.key,
    this.user,
    this.imageUrl,
    this.name,
    this.gender,
    this.radius = 30,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2,
  });

  @override
  Widget build(BuildContext context) {
    final String? finalImageUrl = imageUrl ?? user?.avatar;
    final String finalName = name ?? user?.name ?? 'مستخدم';

    if (finalImageUrl != null && finalImageUrl.isNotEmpty) {
      // إذا كان هناك صورة حقيقية، استخدم SmartAvatar العادي
      return SmartAvatar(
        user: user,
        imageUrl: imageUrl,
        name: name,
        gender: gender,
        radius: radius,
        showBorder: showBorder,
        borderColor: borderColor,
        borderWidth: borderWidth,
      );
    }

    // إذا لم تكن هناك صورة، استخدم AvatarService
    final avatarService = AvatarService();

    return avatarService.buildAvatarWithFallback(
      name: finalName,
      imageUrl: null,
      radius: radius,
      showBorder: showBorder,
      borderColor: borderColor ?? Colors.white,
      borderWidth: borderWidth,
    );
  }


}
