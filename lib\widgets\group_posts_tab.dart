import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../models/post.dart';
import '../providers/social_provider.dart';
import '../widgets/post_card.dart';
import '../widgets/create_group_post_dialog.dart';



class GroupPostsTab extends StatefulWidget {
  final Group group;

  const GroupPostsTab({
    super.key,
    required this.group,
  });

  @override
  State<GroupPostsTab> createState() => _GroupPostsTabState();
}

class _GroupPostsTabState extends State<GroupPostsTab> {
  String _selectedFilter = 'all'; // all, posts, polls, photos, videos

  @override
  void initState() {
    super.initState();
    // تحميل منشورات المجموعة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<SocialProvider>(context, listen: false)
          .loadGroupPosts(widget.group.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SocialProvider>(
      builder: (context, socialProvider, child) {
        final groupPosts = socialProvider.getGroupPosts(widget.group.id);
        final filteredPosts = _filterPosts(groupPosts);

        return Column(
          children: [
            // شريط إنشاء المنشورات
            if (widget.group.isUserMember('current_user'))
              _buildCreatePostBar(),
            
            // فلاتر المنشورات
            _buildPostFilters(),
            
            // قائمة المنشورات
            Expanded(
              child: filteredPosts.isEmpty
                  ? _buildEmptyState()
                  : RefreshIndicator(
                      onRefresh: () => socialProvider.loadGroupPosts(widget.group.id),
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        itemCount: filteredPosts.length,
                        itemBuilder: (context, index) {
                          final post = filteredPosts[index];
                          return _buildPostItem(post);
                        },
                      ),
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCreatePostBar() {
    return Container(
      margin: const EdgeInsets.all(12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط إنشاء منشور
          Row(
            children: [
              // صورة المستخدم
              CircleAvatar(
                radius: 20,
                backgroundColor: Theme.of(context).primaryColor,
                child: const Icon(Icons.person, color: Colors.white),
              ),
              
              const SizedBox(width: 12),
              
              // حقل النص
              Expanded(
                child: GestureDetector(
                  onTap: () => _showCreatePostDialog(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      'ما الذي تريد مشاركته مع المجموعة؟',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // أزرار الإجراءات السريعة
          Row(
            children: [
              _buildQuickActionButton(
                icon: Icons.photo_library,
                label: 'صورة',
                color: Colors.green,
                onTap: () => _showCreatePostDialog(type: 'photo'),
              ),
              
              _buildQuickActionButton(
                icon: Icons.videocam,
                label: 'فيديو',
                color: Colors.red,
                onTap: () => _showCreatePostDialog(type: 'video'),
              ),
              
              _buildQuickActionButton(
                icon: Icons.poll,
                label: 'استطلاع',
                color: Colors.blue,
                onTap: () => _showCreatePollDialog(),
              ),
              
              _buildQuickActionButton(
                icon: Icons.event,
                label: 'حدث',
                color: Colors.orange,
                onTap: () => _showCreateEventDialog(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostFilters() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('all', 'الكل'),
          _buildFilterChip('posts', 'المنشورات'),
          _buildFilterChip('polls', 'الاستطلاعات'),
          _buildFilterChip('photos', 'الصور'),
          _buildFilterChip('videos', 'الفيديوهات'),
          _buildFilterChip('events', 'الأحداث'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = value;
          });
        },
        selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
        checkmarkColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildPostItem(dynamic post) {
    if (post is Post) {
      return PostCard(post: post);
    } else {
      // TODO: إضافة دعم للاستطلاعات والأحداث
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Text('نوع منشور غير مدعوم: ${post.runtimeType}'),
          ),
        ),
      );
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.forum_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منشورات بعد',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'كن أول من ينشر في هذه المجموعة!',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.group.isUserMember('current_user')) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showCreatePostDialog(),
              icon: const Icon(Icons.add),
              label: const Text('إنشاء منشور'),
            ),
          ],
        ],
      ),
    );
  }

  List<dynamic> _filterPosts(List<dynamic> posts) {
    switch (_selectedFilter) {
      case 'posts':
        return posts.where((p) => p is Post && p.type == PostType.text).toList();
      case 'polls':
        return posts.where((p) => p.runtimeType.toString().contains('Poll')).toList();
      case 'photos':
        return posts.where((p) => p is Post && p.type == PostType.image).toList();
      case 'videos':
        return posts.where((p) => p is Post && p.type == PostType.video).toList();
      case 'events':
        return posts.where((p) => p.runtimeType.toString().contains('Event')).toList();
      default:
        return posts;
    }
  }

  void _showCreatePostDialog({String? type}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateGroupPostDialog(
        group: widget.group,
        initialType: type,
      ),
    );
  }

  void _showCreatePollDialog() {
    // TODO: إنشاء حوار إنشاء استطلاع
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء استطلاع')),
    );
  }

  void _showCreateEventDialog() {
    // TODO: إنشاء حوار إنشاء حدث
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء حدث')),
    );
  }
}
