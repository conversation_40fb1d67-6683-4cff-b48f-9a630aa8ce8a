import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../providers/messenger_provider.dart';
import '../models/chat.dart';
import '../models/message.dart';
import '../models/online_friend.dart';
import '../models/user.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import '../widgets/online_friends_widget.dart';
import 'messenger_chat_screen.dart';
import 'messenger_new_chat_screen.dart';
import 'messenger_create_group_screen.dart';
import 'messenger_archived_chats_screen.dart';
import 'messenger_settings_screen.dart';

class MessengerScreen extends StatefulWidget {
  const MessengerScreen({super.key});

  @override
  State<MessengerScreen> createState() => _MessengerScreenState();
}

class _MessengerScreenState extends State<MessengerScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  late OnlineFriendsData _onlineFriendsData;

  @override
  void initState() {
    super.initState();
    _onlineFriendsData = MockOnlineFriendsData.getMockOnlineFriendsData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: const Text(
          'الماسنجر',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            onPressed: _showMessengerOptions,
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في المحادثات...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // المحتوى الرئيسي مع التمرير
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // قسم الأصدقاء المتصلين
                  OnlineFriendsWidget(
                    friendsData: _onlineFriendsData,
                    onFriendTap: _startChatWithFriend,
                    onCallTap: _makeVoiceCall,
                    onVideoCallTap: _makeVideoCall,
                  ),

                  // قائمة المحادثات
                  Consumer<MessengerProvider>(
                    builder: (context, messengerProvider, child) {
                      if (messengerProvider.isLoading) {
                        return const SizedBox(
                          height: 200,
                          child: Center(child: CircularProgressIndicator()),
                        );
                      }

                      if (messengerProvider.errorMessage != null) {
                        return _buildErrorState(messengerProvider);
                      }

                      final filteredChats = _getFilteredChats(messengerProvider.chats);

                      if (filteredChats.isEmpty) {
                        return _buildEmptyState();
                      }

                      return ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        itemCount: filteredChats.length,
                        itemBuilder: (context, index) {
                          final chat = filteredChats[index];
                          return _buildChatItem(chat);
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _startNewChat,
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.chat, color: Colors.white),
      ),
    );
  }

  Widget _buildChatItem(Chat chat) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Stack(
          children: [
            SmartAvatarWithText(
              user: chat.otherUser,
              radius: 28,
            ),
            
            // نقطة الاتصال
            if (chat.otherUser.isOnline)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
          ],
        ),
        
        title: Row(
          children: [
            Expanded(
              child: Text(
                chat.type == ChatType.group 
                    ? (chat.groupName ?? chat.otherUser.name)
                    : chat.otherUser.name,
                style: TextStyle(
                  fontWeight: chat.unreadCount > 0 ? FontWeight.bold : FontWeight.w500,
                  fontSize: 16,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // وقت آخر رسالة
            if (chat.lastMessage != null)
              Text(
                timeago.format(chat.lastMessage!.timestamp, locale: 'ar'),
                style: TextStyle(
                  fontSize: 12,
                  color: chat.unreadCount > 0 ? AppTheme.primaryColor : Colors.grey[500],
                  fontWeight: chat.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
          ],
        ),
        
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                // أيقونة نوع الرسالة
                if (chat.lastMessage != null) ...[
                  if (chat.lastMessage!.senderId == 'current_user')
                    Icon(
                      chat.lastMessage!.status == MessageStatus.read
                          ? Icons.done_all
                          : Icons.done,
                      size: 16,
                      color: chat.lastMessage!.status == MessageStatus.read
                          ? Colors.blue
                          : Colors.grey,
                    ),
                  const SizedBox(width: 4),
                ],
                
                Expanded(
                  child: Text(
                    chat.lastMessage?.content ?? 'لا توجد رسائل',
                    style: TextStyle(
                      color: chat.unreadCount > 0 ? Colors.black87 : Colors.grey[600],
                      fontWeight: chat.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                
                // عدد الرسائل غير المقروءة
                if (chat.unreadCount > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      chat.unreadCount > 99 ? '99+' : chat.unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        
        onTap: () => _openChat(chat),
        onLongPress: () => _showChatActions(chat),
      ),
    );
  }

  Widget _buildErrorState(MessengerProvider messengerProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            messengerProvider.errorMessage!,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => messengerProvider.loadChats(),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty 
                ? 'لا توجد محادثات'
                : 'لا توجد نتائج للبحث',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'ابدأ محادثة جديدة مع أصدقائك'
                : 'جرب البحث بكلمات مختلفة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _startNewChat,
              icon: const Icon(Icons.chat),
              label: const Text('بدء محادثة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<Chat> _getFilteredChats(List<Chat> chats) {
    if (_searchQuery.isEmpty) return chats;
    
    return chats.where((chat) {
      final name = chat.type == ChatType.group 
          ? (chat.groupName ?? chat.otherUser.name)
          : chat.otherUser.name;
      return name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             (chat.lastMessage?.content.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
    }).toList();
  }

  void _openChat(Chat chat) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MessengerChatScreen(chat: chat),
      ),
    );
  }

  void _startNewChat() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MessengerNewChatScreen(),
      ),
    );
  }

  void _showChatActions(Chat chat) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.archive, color: Colors.orange),
              title: const Text('أرشفة المحادثة'),
              onTap: () {
                Navigator.pop(context);
                _archiveChat(chat);
              },
            ),

            ListTile(
              leading: Icon(
                chat.isMuted ? Icons.volume_up : Icons.volume_off,
                color: Colors.blue,
              ),
              title: Text(chat.isMuted ? 'إلغاء الكتم' : 'كتم الإشعارات'),
              onTap: () {
                Navigator.pop(context);
                _toggleMute(chat);
              },
            ),

            ListTile(
              leading: Icon(
                chat.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
                color: Colors.red,
              ),
              title: Text(chat.isPinned ? 'إلغاء التثبيت' : 'تثبيت المحادثة'),
              onTap: () {
                Navigator.pop(context);
                _togglePin(chat);
              },
            ),

            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف المحادثة', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteChatDialog(chat);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showMessengerOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.group_add, color: Colors.blue),
              title: const Text('إنشاء مجموعة'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MessengerCreateGroupScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.archive, color: Colors.orange),
              title: const Text('المحادثات المؤرشفة'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MessengerArchivedChatsScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.settings, color: Colors.grey),
              title: const Text('إعدادات الماسنجر'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MessengerSettingsScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _archiveChat(Chat chat) {
    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);
    messengerProvider.archiveChat(chat.id);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم أرشفة محادثة ${chat.otherUser.name}')),
    );
  }

  void _toggleMute(Chat chat) {
    // تنفيذ كتم/إلغاء كتم المحادثة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          chat.isMuted
              ? 'تم إلغاء كتم محادثة ${chat.otherUser.name}'
              : 'تم كتم محادثة ${chat.otherUser.name}',
        ),
      ),
    );
  }

  void _togglePin(Chat chat) {
    // تنفيذ تثبيت/إلغاء تثبيت المحادثة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          chat.isPinned
              ? 'تم إلغاء تثبيت محادثة ${chat.otherUser.name}'
              : 'تم تثبيت محادثة ${chat.otherUser.name}',
        ),
      ),
    );
  }

  void _showDeleteChatDialog(Chat chat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المحادثة'),
        content: Text('هل تريد حذف المحادثة مع ${chat.otherUser.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteChat(chat);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteChat(Chat chat) {
    // تنفيذ حذف المحادثة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم حذف محادثة ${chat.otherUser.name}')),
    );
  }

  void _startChatWithFriend(OnlineFriend friend) {
    // إنشاء محادثة جديدة مع الصديق
    final messengerProvider = Provider.of<MessengerProvider>(context, listen: false);

    // البحث عن محادثة موجودة أو إنشاء جديدة
    final existingChat = messengerProvider.chats.firstWhere(
      (chat) => chat.otherUser.id == friend.id,
      orElse: () => Chat(
        id: 'chat_${friend.id}_${DateTime.now().millisecondsSinceEpoch}',
        otherUser: User(
          id: friend.id,
          name: friend.name,
          email: '${friend.name.toLowerCase().replaceAll(' ', '.')}@example.com',
          avatar: friend.profileImageUrl,
          isOnline: friend.isOnline,
          joinDate: DateTime.now(),
        ),
        lastActivity: DateTime.now(),
      ),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MessengerChatScreen(chat: existingChat),
      ),
    );
  }

  void _makeVoiceCall(OnlineFriend friend) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('اتصال صوتي مع ${friend.name}'),
        action: SnackBarAction(
          label: 'إلغاء',
          onPressed: () {},
        ),
      ),
    );
  }

  void _makeVideoCall(OnlineFriend friend) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('اتصال مرئي مع ${friend.name}'),
        action: SnackBarAction(
          label: 'إلغاء',
          onPressed: () {},
        ),
      ),
    );
  }
}
